// #include <cuda_runtime.h>
// #include <thrust/device_ptr.h>
// #include <thrust/scan.h>
// #include <thrust/sequence.h>
// #include <thrust/sort.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
//
// // 错误检查宏
// #define CHECK_CUDA_ERROR(call) \
// do { \
//     cudaError_t err = (call); \
//     if (err != cudaSuccess) { \
//         std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__ << ": " \
//                   << cudaGetErrorString(err) << std::endl; \
//         exit(EXIT_FAILURE); \
//     } \
// } while (0)
//
// // 常量定义
// const int TILE_SIZE = 32;
// const int VECTOR_SIZE = 4; // float4 向量化加载
// const int HIGH_DENSITY_THRESHOLD = 32; // 密度分区阈值
// const int HIGH_DENSITY_THREADS = 256; // 高密度区线程数/块
//
// // ELLPACK 格式数据结构
// struct ELLMatrix {
//     int *col_idx;
//     int *csr_idx; // 新增：存储CSR中的原始索引
//     float *values;
//     int max_nnz_per_row;
//     int rows, cols;
// };
//
// // 混合 CSR 矩阵结构（支持分区优化）
// struct HybridCSRMatrix {
//     // CSR 基础结构
//     int *row_ptr; // 行指针
//     int *col_idx; // 列索引
//     float *values; // 非零值
//     int rows, cols, nnz;
//
//     // 分区和格式信息
//     int split_index; // 高/低密度区分界点
//     ELLMatrix ell_high_dens; // 高密度区 ELL 格式
// };
//
// // GPU 行非零元统计内核
// __global__ void compute_row_nnz_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int rows
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         row_nnz[row] = row_ptr[row + 1] - row_ptr[row];
//     }
// }
//
// // 自定义内核：查找分区点（split_index）
// __global__ void find_split_index_kernel(
//     const int *row_nnz,
//     int *split_index,
//     int threshold,
//     int rows
// ) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (idx < rows && row_nnz[idx] <= threshold) {
//         atomicMin(split_index, idx);
//     }
// }
//
// // CSR 转 ELLPACK 格式转换内核
// __global__ void csr_to_ell_kernel(
//     const int *csr_row_ptr,
//     const int *csr_col_idx,
//     int *ell_col_idx,
//     int *ell_csr_idx, // 新增参数
//     float *ell_values,
//     int max_nnz,
//     int rows
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row >= rows) return;
//
//     int start = csr_row_ptr[row];
//     int end = csr_row_ptr[row + 1];
//     int nnz_in_row = end - start;
//
//     for (int i = 0; i < nnz_in_row; i++) {
//         int ell_idx = row + i * rows;
//         ell_col_idx[ell_idx] = csr_col_idx[start + i];
//         ell_csr_idx[ell_idx] = start + i; // 记录CSR索引
//         ell_values[ell_idx] = 0.0f;
//     }
//     for (int i = nnz_in_row; i < max_nnz; i++) {
//         int ell_idx = row + i * rows;
//         ell_col_idx[ell_idx] = -1;
//         ell_csr_idx[ell_idx] = -1; // 无效索引标记
//     }
// }
//
// // 高密度区 SDDMM 内核（ELLPACK 格式）
// __global__ void sddmm_high_density_ell_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ ell_col_idx,
//     const int *__restrict__ ell_csr_idx, // 新增参数
//     float *__restrict__ ell_values,
//     float *__restrict__ csr_values, // CSR值数组指针
//     int max_nnz, int M, int K
// ) {
//     int row = blockIdx.y * blockDim.y + threadIdx.y;
//     if (row >= M) return;
//
//     // 共享内存缓存 A 的行数据
//     extern __shared__ float As[]; // 动态共享内存，大小为K
//     int tid = threadIdx.x;
//     for (int k = tid; k < K; k += blockDim.x) {
//         As[k] = A[row * K + k];
//     }
//     __syncthreads();
//
//     // 处理当前行所有非零元
//     for (int idx = threadIdx.x; idx < max_nnz; idx += blockDim.x) {
//         int flat_idx = row + idx * M;
//         int col = ell_col_idx[flat_idx];
//         if (col == -1) continue; // 跳过填充
//
//         float sum = 0.0f;
//         // 向量化计算（float4）
//         int k = 0;
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4 *>(&As[k]);
//             float4 b_vec = *reinterpret_cast<const float4 *>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x;
//             sum += a_vec.y * b_vec.y;
//             sum += a_vec.z * b_vec.z;
//             sum += a_vec.w * b_vec.w;
//         }
//         // 处理剩余元素
//         for (; k < K; k++) {
//             sum += As[k] * B[col * K + k];
//         }
//         ell_values[flat_idx] = sum;
//
//         // 写回CSR值数组
//         int csr_index = ell_csr_idx[flat_idx];
//         if (csr_index != -1) {
//             csr_values[csr_index] = sum;
//         }
//     }
// }
//
// // 低密度区 SDDMM 内核（优化版）
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     float *__restrict__ result,
//     int start_row, int end_row, int K
// ) {
//     int global_row = start_row + blockIdx.x * 4 + threadIdx.y; // 每块处理4行
//     if (global_row >= end_row) return;
//
//     int row_start = row_ptr[global_row];
//     int row_end = row_ptr[global_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 每个线程处理一行中的多个非零元
//     for (int idx = threadIdx.x; idx < nnz_in_row; idx += blockDim.x) {
//         int col = col_idx[row_start + idx];
//         float sum = 0.0f;
//
//         // 向量化计算（float4）
//         int k = 0;
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4 *>(&A[global_row * K + k]);
//             float4 b_vec = *reinterpret_cast<const float4 *>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x;
//             sum += a_vec.y * b_vec.y;
//             sum += a_vec.z * b_vec.z;
//             sum += a_vec.w * b_vec.w;
//         }
//         // 处理剩余元素
//         for (; k < K; k++) {
//             sum += A[global_row * K + k] * B[col * K + k];
//         }
//         result[row_start + idx] = sum;
//     }
// }
//
// // 查找最大非零元素数
// __global__ void find_max_nnz_kernel(const int *row_nnz, int *max_nnz, int rows) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (idx < rows) {
//         atomicMax(max_nnz, row_nnz[idx]);
//     }
// }
//
// // 主优化函数
// void sddmm_csr_optimized(
//     const float *d_A,
//     const float *d_B,
//     HybridCSRMatrix &sparse,
//     int K
// ) {
//     // 1. 统计每行非零元素数量
//     int *d_row_nnz;
//     CHECK_CUDA_ERROR(cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int)));
//     dim3 block_stat(256);
//     dim3 grid_stat((sparse.rows + block_stat.x - 1) / block_stat.x);
//     compute_row_nnz_kernel<<<grid_stat, block_stat>>>(sparse.row_ptr, d_row_nnz, sparse.rows);
//     CHECK_CUDA_ERROR(cudaDeviceSynchronize());
//
//     // 2. 动态分区计算 - 使用自定义内核查找分区点
//     int *d_split_index;
//     CHECK_CUDA_ERROR(cudaMalloc(&d_split_index, sizeof(int)));
//     int initial_split = sparse.rows; // 初始化为最大行号
//     CHECK_CUDA_ERROR(cudaMemcpy(d_split_index, &initial_split, sizeof(int), cudaMemcpyHostToDevice));
//
//     dim3 block_find(256);
//     dim3 grid_find((sparse.rows + block_find.x - 1) / block_find.x);
//     find_split_index_kernel<<<grid_find, block_find>>>(d_row_nnz, d_split_index, HIGH_DENSITY_THRESHOLD, sparse.rows);
//     CHECK_CUDA_ERROR(cudaDeviceSynchronize());
//
//     // 将结果复制回主机
//     CHECK_CUDA_ERROR(cudaMemcpy(&sparse.split_index, d_split_index, sizeof(int), cudaMemcpyDeviceToHost));
//     CHECK_CUDA_ERROR(cudaFree(d_split_index));
//
//     // 3. 高密度区处理（ELLPACK 格式）
//     if (sparse.split_index > 0) {
//         // 查找最大非零元数（自定义内核替代Thrust）
//         int *d_max_nnz;
//         CHECK_CUDA_ERROR(cudaMalloc(&d_max_nnz, sizeof(int)));
//         int initial_max = 0;
//         CHECK_CUDA_ERROR(cudaMemcpy(d_max_nnz, &initial_max, sizeof(int), cudaMemcpyHostToDevice));
//
//         dim3 block_max(256);
//         dim3 grid_max((sparse.split_index + block_max.x - 1) / block_max.x);
//         find_max_nnz_kernel<<<grid_max, block_max>>>(d_row_nnz, d_max_nnz, sparse.split_index);
//         CHECK_CUDA_ERROR(cudaDeviceSynchronize());
//
//         int max_nnz = 0;
//         CHECK_CUDA_ERROR(cudaMemcpy(&max_nnz, d_max_nnz, sizeof(int), cudaMemcpyDeviceToHost));
//         CHECK_CUDA_ERROR(cudaFree(d_max_nnz));
//
//         sparse.ell_high_dens.rows = sparse.split_index;
//         sparse.ell_high_dens.cols = sparse.cols;
//         sparse.ell_high_dens.max_nnz_per_row = max_nnz;
//
//         // 分配 ELL 格式内存
//         size_t ell_size = sparse.split_index * max_nnz;
//         CHECK_CUDA_ERROR(cudaMalloc(&sparse.ell_high_dens.col_idx, ell_size * sizeof(int)));
//         CHECK_CUDA_ERROR(cudaMalloc(&sparse.ell_high_dens.csr_idx, ell_size * sizeof(int))); // 新增
//         CHECK_CUDA_ERROR(cudaMalloc(&sparse.ell_high_dens.values, ell_size * sizeof(float)));
//
//         // 转换 CSR 到 ELL
//         dim3 block_ell(256);
//         dim3 grid_ell((sparse.split_index + block_ell.x - 1) / block_ell.x);
//         csr_to_ell_kernel<<<grid_ell, block_ell>>>(
//             sparse.row_ptr, sparse.col_idx,
//             sparse.ell_high_dens.col_idx, sparse.ell_high_dens.csr_idx, sparse.ell_high_dens.values,
//             max_nnz, sparse.split_index
//         );
//         CHECK_CUDA_ERROR(cudaDeviceSynchronize());
//
//         // 执行高密度区计算
//         dim3 block_hd(32, 8); // 256 线程/块
//         dim3 grid_hd(1, (sparse.split_index + block_hd.y - 1) / block_hd.y);
//         size_t shared_mem_size = K * sizeof(float); // 动态共享内存大小
//         sddmm_high_density_ell_kernel<<<grid_hd, block_hd, shared_mem_size>>>(
//             d_A, d_B,
//             sparse.ell_high_dens.col_idx,
//             sparse.ell_high_dens.csr_idx, // 新增
//             sparse.ell_high_dens.values,
//             sparse.values, // 传入CSR值数组
//             max_nnz, sparse.split_index, K
//         );
//         CHECK_CUDA_ERROR(cudaDeviceSynchronize());
//     }
//
//     // 4. 低密度区处理（优化 CSR）
//     if (sparse.split_index < sparse.rows) {
//         int low_rows = sparse.rows - sparse.split_index;
//         dim3 block_ld(128, 4); // 512线程/块（128×4）
//         dim3 grid_ld((low_rows + 3) / 4); // 每块处理4行
//         sddmm_low_density_kernel<<<grid_ld, block_ld>>>(
//             d_A, d_B,
//             sparse.row_ptr, sparse.col_idx,
//             sparse.values,
//             sparse.split_index, sparse.rows, K
//         );
//         CHECK_CUDA_ERROR(cudaDeviceSynchronize());
//     }
//
//     // 5. 释放临时资源
//     CHECK_CUDA_ERROR(cudaFree(d_row_nnz));
//     if (sparse.split_index > 0) {
//         CHECK_CUDA_ERROR(cudaFree(sparse.ell_high_dens.col_idx));
//         CHECK_CUDA_ERROR(cudaFree(sparse.ell_high_dens.csr_idx));
//         CHECK_CUDA_ERROR(cudaFree(sparse.ell_high_dens.values));
//     }
// }
//
// // 从 Matrix Market 文件加载 COO 格式稀疏矩阵
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &coo_rows, std::vector<int> &coo_cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     // 跳过注释行
//     while (std::getline(file, line) && line[0] == '%');
//
//     // 读取矩阵元数据
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     // 调整向量大小
//     coo_rows.resize(nnz);
//     coo_cols.resize(nnz);
//
//     // 读取非零元素
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value; // 忽略值，只关心位置
//         file >> row >> col;
//         if (file.peek() != '\n') file >> value; // 如果有值则读取
//         coo_rows[i] = row - 1; // 转换为0-based索引
//         coo_cols[i] = col - 1;
//     }
//     file.close();
// }
//
// // 将 COO 格式转换为 CSR 格式
// void coo_to_csr(const std::vector<int> &coo_rows, const std::vector<int> &coo_cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
//     // 初始化行指针
//     csr_row_ptr.resize(M + 1, 0);
//
//     // 计算每行非零元素数量
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[coo_rows[i]]++;
//     }
//
//     // 前缀和计算行指针
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     // 创建临时行计数器和列索引数组
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     // 填充列索引
//     for (int i = 0; i < nnz; ++i) {
//         int row = coo_rows[i];
//         int col = coo_cols[i];
//         int index = csr_row_ptr[row] + row_counter[row];
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// // CPU 参考实现
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr)); // 初始化随机种子
//
//     // 解析命令行参数
//     std::string filename = argv[1];
//     int K = (argc > 2) ? std::atoi(argv[2]) : 128; // 默认公共维度
//
//     // 1. 从文件加载稀疏矩阵
//     int M, N, nnz;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//     std::cout << "加载矩阵: " << filename << "\n行数: " << M
//             << ", 列数: " << N << ", 非零元数: " << nnz << std::endl;
//
//     // 2. 转换为CSR格式
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx);
//
//     // 3. 初始化稠密矩阵A和B（值范围[0,1)）
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     std::generate(h_A.begin(), h_A.end(), []() { return (rand() % 100) / 100.0f; });
//     std::generate(h_B.begin(), h_B.end(), []() { return (rand() % 100) / 100.0f; });
//
//     // 4. 设备端内存分配
//     float *d_A, *d_B;
//     CHECK_CUDA_ERROR(cudaMalloc(&d_A, M * K * sizeof(float)));
//     CHECK_CUDA_ERROR(cudaMalloc(&d_B, N * K * sizeof(float)));
//     CHECK_CUDA_ERROR(cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice));
//     CHECK_CUDA_ERROR(cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice));
//
//     // 5. 初始化混合CSR矩阵结构
//     HybridCSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//
//     // 分配设备内存
//     CHECK_CUDA_ERROR(cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int)));
//     CHECK_CUDA_ERROR(cudaMalloc(&sparse.col_idx, nnz * sizeof(int)));
//     CHECK_CUDA_ERROR(cudaMalloc(&sparse.values, nnz * sizeof(float)));
//
//     // 复制CSR结构到设备
//     CHECK_CUDA_ERROR(cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
//     CHECK_CUDA_ERROR(cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice));
//     CHECK_CUDA_ERROR(cudaMemset(sparse.values, 0, nnz * sizeof(float))); // 结果初始化为0
//
//     // 6. 创建CUDA事件用于计时
//     cudaEvent_t start, stop;
//     CHECK_CUDA_ERROR(cudaEventCreate(&start));
//     CHECK_CUDA_ERROR(cudaEventCreate(&stop));
//
//     // 7. 执行SDDMM
//     std::cout << "\n>>> 开始执行SDDMM优化计算..." << std::endl;
//     CHECK_CUDA_ERROR(cudaEventRecord(start));
//
//     sddmm_csr_optimized(d_A, d_B, sparse, K); // 调用优化函数
//
//     CHECK_CUDA_ERROR(cudaEventRecord(stop));
//     CHECK_CUDA_ERROR(cudaEventSynchronize(stop));
//     std::cout << "<<< SDDMM计算完成" << std::endl;
//
//     // 8. 计算性能指标
//     float milliseconds = 0;
//     CHECK_CUDA_ERROR(cudaEventElapsedTime(&milliseconds, start, stop));
//     double gflops = (2.0 * nnz * K) / (milliseconds * 1e6);
//     printf("计算时间: %.3f ms\n吞吐量: %.2f GFLOPS\n", milliseconds, gflops);
//
//     // 9. 验证结果
//     std::vector<float> h_values_gpu(nnz);
//     CHECK_CUDA_ERROR(cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost));
//
//     std::vector<float> h_values_cpu(nnz);
//     std::cout << "\n>>> 开始CPU参考计算..." << std::endl;
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//     std::cout << "<<< CPU参考计算完成" << std::endl;
//
//     // 10. 结果比较
//     int correct_count = 0;
//     float max_error = 0.0f;
//     const float tolerance = 1e-4f;
//
//     for (int i = 0; i < nnz; ++i) {
//         float error = fabs(h_values_gpu[i] - h_values_cpu[i]);
//         max_error = fmax(max_error, error);
//         if (error < tolerance) correct_count++;
//     }
//
//     std::cout << "\n验证结果:\n最大绝对误差: " << max_error
//             << "\n正确率: " << (100.0 * correct_count / nnz) << "%" << std::endl;
//
//     // 11. 资源释放
//     CHECK_CUDA_ERROR(cudaFree(d_A));
//     CHECK_CUDA_ERROR(cudaFree(d_B));
//     CHECK_CUDA_ERROR(cudaFree(sparse.row_ptr));
//     CHECK_CUDA_ERROR(cudaFree(sparse.col_idx));
//     CHECK_CUDA_ERROR(cudaFree(sparse.values));
//     CHECK_CUDA_ERROR(cudaEventDestroy(start));
//     CHECK_CUDA_ERROR(cudaEventDestroy(stop));
//
//     return 0;
// }
