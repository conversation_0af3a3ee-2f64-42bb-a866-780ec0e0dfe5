// #include <iostream>
// #include <fstream>
// #include <sstream>
// #include <string>
// #include <cstdio>
// #include <cstdlib>
// #include <vector>
// #include <unordered_map>
// #include <algorithm>
// #include <cuda_runtime.h>
// #include <cassert>
// #include <random>
// #include <bits/stdc++.h>
//
// using namespace std;
//
// #define CUDA_CHECK(call) \
// do { \
//     cudaError_t err = (call); \
//     if (err != cudaSuccess) { \
//         fprintf(stderr, "CUDA error at %s:%d code=%d(%s) \"%s\"\n", \
//         __FILE__, __LINE__, err, cudaGetErrorString(err), #call); \
//         exit(EXIT_FAILURE); \
//     } \
// } while(0)
//
// void print_array(const float *arr, int size, const char *name) {
//     printf("%s: [", name);
//     for (int i = 0; i < size; i++) {
//         printf("%.1f%s", arr[i], (i == size - 1) ? "]\n" : ", ");
//     }
// }
//
// struct BlockInfo {
//     int nnz;
//     int *h_local_A_rows; // 当前块中非零元素对应的A矩阵行索引的本地映射
//     int *h_local_B_cols; // 当前块中非零元素对应的B矩阵列索引的本地映射
//     float *h_A_block; // 当前块对应的A矩阵子块数据（仅包含非高频行）
//     float *h_B_block; // 当前块对应的B矩阵子块数据（仅包含非高频列）
//     int num_A_rows; // 当前块中A矩阵的独立行数（排除高频行）
//     int num_B_cols; // 当前块中B矩阵的独立列数（排除高频列）
//     int *h_unique_rows; //当前块中所有唯一的行索引
//     int *h_unique_cols; //当前块中所有唯一的列索引
//     int num_unique_rows; //当前块中所有唯一的行索引数量（包含高频行和非高频行）
//     int num_unique_cols; //当前块中所有唯一的列索引数量（包含高频列和非高频列）
// };
//
//
// __global__ void sddmm_kernel(const float *A_block, const float *B_block, const int *local_A_rows,
//                              const int *local_B_cols, float *output, int nnz, int K, int merged_A_rows,
//                              int merged_B_cols, int TILE_K) {
//     extern __shared__ float shared[];
//     float *A_tile = shared;
//     float *B_tile = &shared[merged_A_rows * TILE_K];
//
//     int tid = threadIdx.x;
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     bool valid = idx < nnz;
//
//     float sum = 0.0f;
//     int local_row = valid ? local_A_rows[idx] : 0;
//     int local_col = valid ? local_B_cols[idx] : 0;
//
//     for (int k_base = 0; k_base < K; k_base += TILE_K) {
//         int tile_size = min(TILE_K, K - k_base);
//
//         // 加载A_tile
//         for (int i = tid; i < merged_A_rows * tile_size; i += blockDim.x) {
//             int row = i / tile_size;
//             int k = i % tile_size;
//             if (row < merged_A_rows && (k_base + k) < K) {
//                 A_tile[row * tile_size + k] = A_block[row * K + (k_base + k)];
//             }
//         }
//
//         // 加载B_tile
//         for (int i = tid; i < merged_B_cols * tile_size; i += blockDim.x) {
//             int col = i / tile_size;
//             int k = i % tile_size;
//             if (col < merged_B_cols && (k_base + k) < K) {
//                 B_tile[col * tile_size + k] = B_block[col * K + (k_base + k)];
//             }
//         }
//         __syncthreads();
//
//         if (valid) {
//             // 计算点积
//             for (int k = 0; k < tile_size; k++) {
//                 int a_pos = local_row * tile_size + k;
//                 int b_pos = local_col * tile_size + k;
//
//                 if (a_pos < merged_A_rows * tile_size && b_pos < merged_B_cols * tile_size) {
//                     sum += A_tile[a_pos] * B_tile[b_pos];
//                 }
//             }
//         }
//         __syncthreads();
//     }
//
//     if (valid) {
//         output[idx] = sum;
//     }
// }
//
//
// vector<int> find_high_freq_indices(const int *indices, int length, int top_n) {
//     unordered_map<int, int> freq_map;
//     for (int i = 0; i < length; i++) {
//         freq_map[indices[i]]++;
//     }
//
//     vector<pair<int, int> > sorted_freq(freq_map.begin(), freq_map.end());
//     sort(sorted_freq.begin(), sorted_freq.end(),
//          [](const pair<int, int> &a, const pair<int, int> &b) {
//              return a.second > b.second;
//          });
//
//     vector<int> high_freq;
//     for (int i = 0; i < min(top_n, (int) sorted_freq.size()); i++) {
//         high_freq.push_back(sorted_freq[i].first);
//     }
//     return high_freq;
// }
//
// float *d_high_freq_A = nullptr;
// float *d_high_freq_B = nullptr;
//
// void preprocess_blocks(BlockInfo *blocks, int num_blocks, const float *A, const float *B,
//                        const int *all_rows, const int *all_cols, int total_nnz, int K, int N,
//                        const vector<int> &high_freq_rows, const vector<int> &high_freq_cols,
//                        float &pre_h2d_time, float host_time) {
//     pre_h2d_time = 0.0f;
//     cudaEvent_t start, stop;
//     CUDA_CHECK(cudaEventCreate(&start));
//     CUDA_CHECK(cudaEventCreate(&stop));
//
//     int num_high_rows = high_freq_rows.size();
//     int num_high_cols = high_freq_cols.size();
//     CUDA_CHECK(cudaMalloc(&d_high_freq_A, num_high_rows * K * sizeof(float)));
//     CUDA_CHECK(cudaMalloc(&d_high_freq_B, num_high_cols * K * sizeof(float)));
//
//     // Copy high-frequency rows and columns to device
//     vector<float> high_A_data(num_high_rows * K);
//     for (int i = 0; i < num_high_rows; i++) {
//         int r = high_freq_rows[i];
//         memcpy(high_A_data.data() + i * K, A + r * K, K * sizeof(float));
//     }
//     CUDA_CHECK(cudaEventRecord(start));
//     CUDA_CHECK(cudaMemcpy(d_high_freq_A, high_A_data.data(), num_high_rows * K * sizeof(float), cudaMemcpyHostToDevice))
//     ;
//     CUDA_CHECK(cudaEventRecord(stop));
//     CUDA_CHECK(cudaEventSynchronize(stop));
//     float time;
//     CUDA_CHECK(cudaEventElapsedTime(&time, start, stop));
//     pre_h2d_time += time;
//
//     vector<float> high_B_data(num_high_cols * K);
//     for (int i = 0; i < num_high_cols; i++) {
//         int c = high_freq_cols[i];
//         for (int k = 0; k < K; k++) {
//             high_B_data[i * K + k] = B[k * N + c];
//         }
//     }
//     CUDA_CHECK(cudaEventRecord(start));
//     CUDA_CHECK(cudaMemcpy(d_high_freq_B, high_B_data.data(), num_high_cols * K * sizeof(float), cudaMemcpyHostToDevice))
//     ;
//     CUDA_CHECK(cudaEventRecord(stop));
//     CUDA_CHECK(cudaEventSynchronize(stop));
//     CUDA_CHECK(cudaEventElapsedTime(&time, start, stop));
//     pre_h2d_time += time;
//
//     unordered_set<int> high_row_set(high_freq_rows.begin(), high_freq_rows.end());
//     unordered_set<int> high_col_set(high_freq_cols.begin(), high_freq_cols.end());
//
//     int nnz_per_block = (total_nnz + num_blocks - 1) / num_blocks;
// #pragma omp parallel for
//     for (int b = 0; b < num_blocks; b++) {
//         int start = b * nnz_per_block;
//         int end = min(start + nnz_per_block, total_nnz);
//         blocks[b].nnz = end - start;
//
//         // 创建行和列的映射
//         unordered_map<int, int> row_map, col_map;
//         vector<int> unique_rows, unique_cols;
//
//         // 首先收集所有唯一的行和列
//         for (int i = start; i < end; i++) {
//             int r = all_rows[i];
//             int c = all_cols[i];
//             if (row_map.find(r) == row_map.end()) {
//                 row_map[r] = unique_rows.size();
//                 unique_rows.push_back(r);
//             }
//             if (col_map.find(c) == col_map.end()) {
//                 col_map[c] = unique_cols.size();
//                 unique_cols.push_back(c);
//             }
//         }
//
//         // 分离高频和非高频行列
//         vector<int> non_high_rows, high_rows_in_block;
//         for (int r: unique_rows) {
//             if (high_row_set.count(r)) {
//                 high_rows_in_block.push_back(r);
//             } else {
//                 non_high_rows.push_back(r);
//             }
//         }
//
//         vector<int> non_high_cols, high_cols_in_block;
//         for (int c: unique_cols) {
//             if (high_col_set.count(c)) {
//                 high_cols_in_block.push_back(c);
//             } else {
//                 non_high_cols.push_back(c);
//             }
//         }
//
//         // 更新映射
//         row_map.clear();
//         for (size_t i = 0; i < non_high_rows.size(); i++) {
//             row_map[non_high_rows[i]] = i;
//         }
//         for (size_t i = 0; i < high_rows_in_block.size(); i++) {
//             row_map[high_rows_in_block[i]] = non_high_rows.size() + i;
//         }
//
//         col_map.clear();
//         for (size_t i = 0; i < non_high_cols.size(); i++) {
//             col_map[non_high_cols[i]] = i;
//         }
//         for (size_t i = 0; i < high_cols_in_block.size(); i++) {
//             col_map[high_cols_in_block[i]] = non_high_cols.size() + i;
//         }
//
//         // 设置块信息
//         blocks[b].num_A_rows = non_high_rows.size();
//         blocks[b].num_B_cols = non_high_cols.size();
//         blocks[b].h_A_block = new float[blocks[b].num_A_rows * K];
//         blocks[b].h_B_block = new float[blocks[b].num_B_cols * K];
//         blocks[b].num_unique_rows = unique_rows.size();
//         blocks[b].num_unique_cols = unique_cols.size();
//
//         clock_t host_start = clock();
//         // 复制非高频数据
//         for (size_t i = 0; i < non_high_rows.size(); i++) {
//             int r = non_high_rows[i];
//             memcpy(blocks[b].h_A_block + i * K, A + r * K, K * sizeof(float));
//         }
//         clock_t host_end = clock();
//         host_time += (host_end - host_start) / CLOCKS_PER_SEC * 1000;
//
//         for (size_t i = 0; i < non_high_cols.size(); i++) {
//             int c = non_high_cols[i];
//             for (int k = 0; k < K; k++) {
//                 blocks[b].h_B_block[i * K + k] = B[k * N + c];
//             }
//         }
//
//         // 分配并设置本地索引
//         blocks[b].h_local_A_rows = new int[end - start];
//         blocks[b].h_local_B_cols = new int[end - start];
//         blocks[b].h_unique_rows = new int[unique_rows.size()];
//         blocks[b].h_unique_cols = new int[unique_cols.size()];
//
//         memcpy(blocks[b].h_unique_rows, unique_rows.data(), unique_rows.size() * sizeof(int));
//         memcpy(blocks[b].h_unique_cols, unique_cols.data(), unique_cols.size() * sizeof(int));
//
//         // 设置本地索引映射
//         for (int i = start; i < end; i++) {
//             int idx = i - start;
//             int global_r = all_rows[i];
//             int global_c = all_cols[i];
//             blocks[b].h_local_A_rows[idx] = row_map[global_r];
//             blocks[b].h_local_B_cols[idx] = col_map[global_c];
//         }
//     }
//
//     CUDA_CHECK(cudaEventDestroy(start));
//     CUDA_CHECK(cudaEventDestroy(stop));
// }
//
// void execute_sddmm(BlockInfo *blocks, int num_blocks, int K, int N, float *d_final_result, const int *block_offsets,
//                    const vector<int> &high_freq_rows, const vector<int> &high_freq_cols,
//                    float &elapsed_time, float &exec_h2d_time) {
//     exec_h2d_time = 0.0f;
//     const int num_buffers = 4;
//     cudaStream_t streams[num_buffers];
//     cudaEvent_t events[num_buffers];
//     cudaEvent_t kernel_start, kernel_stop, h2d_start, h2d_stop;
//     CUDA_CHECK(cudaEventCreate(&kernel_start));
//     CUDA_CHECK(cudaEventCreate(&kernel_stop));
//     CUDA_CHECK(cudaEventCreate(&h2d_start));
//     CUDA_CHECK(cudaEventCreate(&h2d_stop));
//     float kernel_time = 0.0f;
//
//     struct DeviceBuffers {
//         float *A_block = nullptr;
//         float *B_block = nullptr;
//         int *local_A_rows = nullptr;
//         int *local_B_cols = nullptr;
//         float *output = nullptr;
//     } d_buf[num_buffers];
//
//     size_t max_A_rows = 0, max_B_cols = 0, max_nnz = 0;
//     for (int b = 0; b < num_blocks; b++) {
//         max_A_rows = max(max_A_rows, (size_t) (blocks[b].num_A_rows + blocks[b].num_unique_rows));
//         max_B_cols = max(max_B_cols, (size_t) (blocks[b].num_B_cols + blocks[b].num_unique_cols));
//         max_nnz = max(max_nnz, (size_t) blocks[b].nnz);
//     }
//
//     for (int i = 0; i < num_buffers; i++) {
//         CUDA_CHECK(cudaMalloc(&d_buf[i].A_block, max_A_rows * K * sizeof(float)));
//         CUDA_CHECK(cudaMalloc(&d_buf[i].B_block, max_B_cols * K * sizeof(float)));
//         CUDA_CHECK(cudaMalloc(&d_buf[i].local_A_rows, max_nnz * sizeof(int)));
//         CUDA_CHECK(cudaMalloc(&d_buf[i].local_B_cols, max_nnz * sizeof(int)));
//         CUDA_CHECK(cudaMalloc(&d_buf[i].output, max_nnz * sizeof(float)));
//         CUDA_CHECK(cudaStreamCreate(&streams[i]));
//         CUDA_CHECK(cudaEventCreate(&events[i]));
//     }
//
//     unordered_map<int, int> row_idx_map;
//     for (size_t i = 0; i < high_freq_rows.size(); i++) row_idx_map[high_freq_rows[i]] = i;
//     unordered_map<int, int> col_idx_map;
//     for (size_t i = 0; i < high_freq_cols.size(); i++) col_idx_map[high_freq_cols[i]] = i;
//
//     int current = 0;
//     for (int b = 0; b < num_blocks; b++) {
//         int next = (current + 1) % num_buffers;
//
//         // 计算合并后的行列数
//         vector<int> high_rows_in_block;
//         for (int i = 0; i < blocks[b].num_unique_rows; i++) {
//             int r = blocks[b].h_unique_rows[i];
//             if (row_idx_map.count(r)) high_rows_in_block.push_back(r);
//         }
//
//         vector<int> high_cols_in_block;
//         for (int i = 0; i < blocks[b].num_unique_cols; i++) {
//             int c = blocks[b].h_unique_cols[i];
//             if (col_idx_map.count(c)) high_cols_in_block.push_back(c);
//         }
//
//         int merged_A_rows = blocks[b].num_A_rows + high_rows_in_block.size();
//         int merged_B_cols = blocks[b].num_B_cols + high_cols_in_block.size();
//
//         // 加载非高频数据
//         if (blocks[b].num_A_rows > 0) {
//             CUDA_CHECK(cudaEventRecord(h2d_start, streams[current]));
//             CUDA_CHECK(cudaMemcpyAsync(
//                 d_buf[current].A_block,
//                 blocks[b].h_A_block,
//                 blocks[b].num_A_rows * K * sizeof(float),
//                 cudaMemcpyHostToDevice,
//                 streams[current]
//             ));
//             CUDA_CHECK(cudaEventRecord(h2d_stop, streams[current]));
//             CUDA_CHECK(cudaEventSynchronize(h2d_stop));
//             float t;
//             CUDA_CHECK(cudaEventElapsedTime(&t, h2d_start, h2d_stop));
//             exec_h2d_time += t;
//         }
//
//         // 加载高频行数据
//         size_t high_A_offset = blocks[b].num_A_rows * K;
//         for (int r: high_rows_in_block) {
//             int idx = row_idx_map[r];
//             CUDA_CHECK(cudaMemcpyAsync(
//                 d_buf[current].A_block + high_A_offset,
//                 d_high_freq_A + idx * K,
//                 K * sizeof(float),
//                 cudaMemcpyDeviceToDevice,
//                 streams[current]
//             ));
//             high_A_offset += K;
//         }
//
//         // 加载非高频列数据
//         if (blocks[b].num_B_cols > 0) {
//             CUDA_CHECK(cudaEventRecord(h2d_start, streams[current]));
//             CUDA_CHECK(cudaMemcpyAsync(
//                 d_buf[current].B_block,
//                 blocks[b].h_B_block,
//                 blocks[b].num_B_cols * K * sizeof(float),
//                 cudaMemcpyHostToDevice,
//                 streams[current]
//             ));
//             CUDA_CHECK(cudaEventRecord(h2d_stop, streams[current]));
//             CUDA_CHECK(cudaEventSynchronize(h2d_stop));
//             float t;
//             CUDA_CHECK(cudaEventElapsedTime(&t, h2d_start, h2d_stop));
//             exec_h2d_time += t;
//         }
//
//         // 加载高频列数据
//         size_t high_B_offset = blocks[b].num_B_cols * K;
//         for (int c: high_cols_in_block) {
//             int idx = col_idx_map[c];
//             CUDA_CHECK(cudaMemcpyAsync(
//                 d_buf[current].B_block + high_B_offset,
//                 d_high_freq_B + idx * K,
//                 K * sizeof(float),
//                 cudaMemcpyDeviceToDevice,
//                 streams[current]
//             ));
//             high_B_offset += K;
//         }
//
//         // 加载索引数据
//         CUDA_CHECK(cudaMemcpyAsync(
//             d_buf[current].local_A_rows,
//             blocks[b].h_local_A_rows,
//             blocks[b].nnz * sizeof(int),
//             cudaMemcpyHostToDevice,
//             streams[current]
//         ));
//         CUDA_CHECK(cudaMemcpyAsync(
//             d_buf[current].local_B_cols,
//             blocks[b].h_local_B_cols,
//             blocks[b].nnz * sizeof(int),
//             cudaMemcpyHostToDevice,
//             streams[current]
//         ));
//
//         // 获取设备属性以确定最大共享内存
//         int device_id;
//         cudaDeviceProp prop;
//         CUDA_CHECK(cudaGetDevice(&device_id));
//         CUDA_CHECK(cudaGetDeviceProperties(&prop, device_id));
//
//         // 动态计算每个块的最大允许TILE_K
//         int TILE_K = K; // 使用完整的K值
//         size_t required_shared = (merged_A_rows * TILE_K + merged_B_cols * TILE_K) * sizeof(float);
//
//         // 如果共享内存不足，则减小TILE_K
//         while (required_shared > prop.sharedMemPerBlock && TILE_K > 1) {
//             TILE_K /= 2;
//             required_shared = (merged_A_rows * TILE_K + merged_B_cols * TILE_K) * sizeof(float);
//         }
//
//         // 确保TILE_K至少为1且不超过K
//         TILE_K = min(max(TILE_K, 1), K);
//
//         dim3 block(256);
//         dim3 grid((blocks[b].nnz + block.x - 1) / block.x);
//         size_t shared_mem_size = (merged_A_rows * TILE_K + merged_B_cols * TILE_K) * sizeof(float);
//
//         // 设置输出指针为全局结果中的对应位置
//         float *output_ptr = d_final_result + block_offsets[b];
//
//         CUDA_CHECK(cudaEventRecord(kernel_start, streams[current]));
//         sddmm_kernel<<<grid, block, shared_mem_size, streams[current]>>>(
//             d_buf[current].A_block,
//             d_buf[current].B_block,
//             d_buf[current].local_A_rows,
//             d_buf[current].local_B_cols,
//             output_ptr, // 直接写入全局结果位置
//             blocks[b].nnz,
//             K,
//             merged_A_rows,
//             merged_B_cols,
//             TILE_K
//         );
//         CUDA_CHECK(cudaEventRecord(kernel_stop, streams[current]));
//         CUDA_CHECK(cudaEventSynchronize(kernel_stop));
//         float temp_time;
//         CUDA_CHECK(cudaEventElapsedTime(&temp_time, kernel_start, kernel_stop));
//         kernel_time += temp_time;
//
//         current = next;
//     }
//
//     for (int i = 0; i < num_buffers; i++) {
//         CUDA_CHECK(cudaStreamSynchronize(streams[i]));
//     }
//
//     for (int i = 0; i < num_buffers; i++) {
//         CUDA_CHECK(cudaFree(d_buf[i].A_block));
//         CUDA_CHECK(cudaFree(d_buf[i].B_block));
//         CUDA_CHECK(cudaFree(d_buf[i].local_A_rows));
//         CUDA_CHECK(cudaFree(d_buf[i].local_B_cols));
//         CUDA_CHECK(cudaFree(d_buf[i].output));
//         CUDA_CHECK(cudaStreamDestroy(streams[i]));
//         CUDA_CHECK(cudaEventDestroy(events[i]));
//     }
//
//     CUDA_CHECK(cudaEventDestroy(kernel_start));
//     CUDA_CHECK(cudaEventDestroy(kernel_stop));
//     CUDA_CHECK(cudaEventDestroy(h2d_start));
//     CUDA_CHECK(cudaEventDestroy(h2d_stop));
//     elapsed_time = kernel_time;
// }
//
// void initial(float *X, long n, int k) {
//     // 使用C++11随机数库
//     static std::mt19937 generator(1);
//     std::uniform_real_distribution<float> distribution(0.0f, 0.1f); // 生成[0,0.1)范围的随机数
//
//     for (long r = 0; r < n; ++r) {
//         for (long t = 0; t < k; ++t)
//             X[r * k + t] = distribution(generator); // 生成目标范围的随机数
//     }
// }
//
// vector<int> adaptive_topn_selection(const int *indices, int total_nnz, float coverage_threshold = 0.8) {
//     unordered_map<int, int> freq_map;
//     for (int i = 0; i < total_nnz; i++) freq_map[indices[i]]++;
//
//     vector<pair<int, int> > sorted(freq_map.begin(), freq_map.end());
//     // 修改 lambda 参数类型为具体类型
//     sort(sorted.begin(), sorted.end(), [](const pair<int, int> &a, const pair<int, int> &b) {
//         return a.second > b.second;
//     });
//
//     int total = total_nnz, current = 0;
//     vector<int> selected;
//     for (auto &p: sorted) {
//         current += p.second;
//         selected.push_back(p.first);
//         if ((float) current / total >= coverage_threshold) break;
//     }
//     return selected;
// }
//
// int main(int argc, char *argv[]) {
//     ifstream fp(argv[1]);
//     const int K = atoi(argv[2]);
//     const int tile_sizeX = atoi(argv[3]);
//     const int tile_sizeY = atoi(argv[4]);
//
//     // 读取矩阵头信息
//     string str;
//     while (getline(fp, str) && !isdigit(str[0])) {
//     }
//     int M, N, total_nnz;
//     istringstream header(str);
//     header >> M >> N >> total_nnz;
//     printf("M = %d, N = %d, total_nnz = %d\n", M, N, total_nnz);
//
//     // 初始化A、B
//     float *A = new float[M * K];
//     initial(A, M, K);
//     float *B = new float[K * N];
//     initial(B, K, N);
//
//     // 读取非零元素数据
//     int *all_rows = new int[total_nnz];
//     int *all_cols = new int[total_nnz];
//     for (int idx = 0; idx < total_nnz; ++idx) {
//         int rid, cid;
//         float val;
//         fp >> rid >> cid;
//         // printf("rid: %d, cid: %d\n", rid, cid);
//         // fp >> rid >> cid >> val;
//         all_rows[idx] = rid - 1;
//         all_cols[idx] = cid - 1;
//     }
//
//     // 统计高频行列
//     const int num_blocks = (total_nnz + tile_sizeX - 1) / tile_sizeX;
//     vector<int> high_freq_rows = adaptive_topn_selection(all_rows, total_nnz, 0.8);
//     vector<int> high_freq_cols = adaptive_topn_selection(all_cols, total_nnz, 0.8);
//
//     // 预处理和计算
//     BlockInfo *blocks = new BlockInfo[num_blocks];
//
//     // 分配全局设备结果内存
//     float *d_final_result;
//     CUDA_CHECK(cudaMalloc(&d_final_result, total_nnz * sizeof(float)));
//
//     // 计算每个块的偏移量
//     int *block_offsets = new int[num_blocks];
//     int offset = 0;
//     for (int b = 0; b < num_blocks; b++) {
//         block_offsets[b] = offset;
//         offset += (tile_sizeX < total_nnz - b * tile_sizeX) ? tile_sizeX : total_nnz - b * tile_sizeX;
//     }
//
//     float tot_ms, total_d2h_time;
//     cudaEvent_t event1, event2, event3, event4;
//     cudaEventCreate(&event1);
//     cudaEventCreate(&event2);
//
//     cudaDeviceSynchronize();
//     cudaEventRecord(event1, 0);
//
//     float pre_h2d_time = 0.0f;
//     float host_time = 0.0f;
//
//     preprocess_blocks(blocks, num_blocks, A, B, all_rows, all_cols, total_nnz, K, N,
//                       high_freq_rows, high_freq_cols, pre_h2d_time, host_time);
//
//     cudaEventRecord(event2, 0);
//
//     cudaEventSynchronize(event1);
//     cudaEventSynchronize(event2);
//     cudaEventElapsedTime(&tot_ms, event1, event2);
//     cudaDeviceSynchronize();
//
//     float exec_h2d_time = 0.0f;
//     float elapsed_time;
//     execute_sddmm(blocks, num_blocks, K, N, d_final_result, block_offsets,
//                   high_freq_rows, high_freq_cols,
//                   elapsed_time, exec_h2d_time);
//
//     // 一次性将结果从设备复制回主机
//     float *final_result = new float[total_nnz];
//     cudaEventCreate(&event3);
//     cudaEventCreate(&event4);
//     cudaEventRecord(event3, 0);
//
//     CUDA_CHECK(cudaMemcpy(final_result, d_final_result, total_nnz * sizeof(float), cudaMemcpyDeviceToHost));
//
//     cudaEventRecord(event4, 0);
//     cudaEventSynchronize(event3);
//     cudaEventSynchronize(event4);
//     cudaEventElapsedTime(&total_d2h_time, event3, event4);
//     cudaDeviceSynchronize();
//
//     // 计算并打印时间
//     float total_transfer_time = pre_h2d_time + exec_h2d_time;
//     printf("pre_h2d_time: %f\n", pre_h2d_time);
//     printf("exec_h2d_time: %f\n", exec_h2d_time);
//     printf("Total Host-to-Device (H2D) Transfer Time: %.4f ms\n", total_transfer_time);
//     printf("Total Device-to-Host (D2H) Transfer Time: %.4f ms\n", total_d2h_time);
//     printf("preprocess time: %f ms\n", tot_ms);
//
//     // CPU计算结果
//     float *expected = new float[total_nnz];
//     for (int i = 0; i < total_nnz; ++i) {
//         int row = all_rows[i];
//         int col = all_cols[i];
//         float sum = 0.0f;
//         for (int k = 0; k < K; ++k) {
//             sum += A[row * K + k] * B[k * N + col]; // B是列优先
//         }
//         expected[i] = sum;
//     }
//
//     // 正确性检验
//     printf("\n****** 正确性检验 *******\n");
//
//     // 打印首尾各3个元素对比
//     for (int i = 0; i < 3; ++i) {
//         printf("索引 %4d: GPU=%-8.5f  CPU=%-8.5f\n",
//                i, final_result[i], expected[i]);
//     }
//     for (int i = total_nnz - 3; i < total_nnz; ++i) {
//         if (i >= 0)
//             printf("索引 %4d: GPU=%-8.5f  CPU=%-8.5f\n",
//                    i, final_result[i], expected[i]);
//     }
//
//     // 统计差异数量
//     long diff_count = 0;
//     const float epsilon = 1e-5;
//     for (int i = 0; i < total_nnz; ++i) {
//         if (fabs(final_result[i] - expected[i]) > epsilon) {
//             if (diff_count < 5) {
//                 // 打印前5个差异
//                 printf("差异索引 %d: GPU=%.6f  CPU=%.6f 差值=%.6f\n",
//                        i, final_result[i], expected[i],
//                        final_result[i] - expected[i]);
//             }
//             diff_count++;
//         }
//     }
//
//     // 输出统计结果
//     if (diff_count == 0) {
//         printf("\n结果完全一致!\n");
//     } else {
//         printf("\n发现差异: 总差异元素数量 = %ld (容差=%.0e)\n",
//                diff_count, epsilon);
//     }
//
//     // 释放内存
//     delete[] expected;
//
//     // 打印时间
//     printf("Time for SDDMM with K = 256: %.4f ms\n", elapsed_time);
//
//     // 释放资源
//     for (int b = 0; b < num_blocks; b++) {
//         delete[] blocks[b].h_A_block;
//         delete[] blocks[b].h_B_block;
//         delete[] blocks[b].h_local_A_rows;
//         delete[] blocks[b].h_local_B_cols;
//         delete[] blocks[b].h_unique_rows;
//         delete[] blocks[b].h_unique_cols;
//     }
//     delete[] blocks;
//     delete[] final_result;
//     CUDA_CHECK(cudaFree(d_final_result));
//     delete[] block_offsets;
//
//     return 0;
// }
