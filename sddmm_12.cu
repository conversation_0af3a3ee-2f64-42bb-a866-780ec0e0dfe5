// #include <cuda_runtime.h>
// #include <cuda_fp16.h>
// #include <cstdint>
// #include <stdexcept>
// #include <cstdio>
// #include <vector>
// #include <algorithm>
// #include <cmath>
// #include <fstream>
// #include <sstream>
// #include <string>
// #include <iostream>
// #include <cctype>
// #include <regex>
//
// constexpr int TILE_SIZE = 32; // K维度分块大小
// constexpr int NNZ_PER_BLOCK = 128; // 每个块处理的非零元素数
//
// // SDDMM核心CUDA内核
// template<typename T, typename T_Compute>
// __global__ void sddmm_kernel(
//     int M, int K, int N, int nnz,
//     const T *__restrict__ A, int lda,
//     const T *__restrict__ B, int ldb,
//     const int *__restrict__ row_idx,
//     const int *__restrict__ col_idx,
//     T *__restrict__ result) {
//     // 当前线程块处理的非零元素索引范围
//     const int block_id = blockIdx.x;
//     const int start_id = block_id * NNZ_PER_BLOCK;
//     const int end_id = min(start_id + NNZ_PER_BLOCK, nnz);
//     const int nnz_in_block = end_id - start_id;
//     const int tid = threadIdx.x;
//
//     // 共享内存分配（确保对齐）
//     extern __shared__ __align__(128) char smem_char[]; //128字节对齐保证内存访问效率
//     T_Compute *A_tile = reinterpret_cast<T_Compute *>(smem_char); //将共享内存首地址转换为计算类型指针（用于存储A矩阵的平铺数据）
//     T_Compute *B_tile = reinterpret_cast<T_Compute *>(A_tile + TILE_SIZE); // 在A_tile之后分配B矩阵的平铺存储空间
//     int *col_idx_buffer = reinterpret_cast<int *>(B_tile + TILE_SIZE * NNZ_PER_BLOCK); //存储当前块处理的非零元素的列索引
//
//     // 获取当前线程处理的非零元素的行列索引
//     int my_row = -1;
//     int my_col = -1;
//     if (tid < nnz_in_block) {
//         my_row = row_idx[start_id + tid];
//         my_col = col_idx[start_id + tid];
//         col_idx_buffer[tid] = my_col;
//     }
//
//     // 检测当前块是否所有非零元素都在同一行
//     __shared__ bool shared_common_row; //标记当前块是否所有非零元素同行
//     __shared__ int shared_base_row; //存储公共行索引（如果存在）
//
//     if (tid == 0) {
//         shared_common_row = (nnz_in_block > 0); // 初始化标记：当块内有非零元素时才可能存在公共行
//         if (shared_common_row) {
//             shared_base_row = row_idx[start_id];
//             for (int i = 1; i < nnz_in_block; i++) {
//                 // 遍历块内所有非零元素的行索引，发现不同行元素时立即终止检测
//                 if (row_idx[start_id + i] != shared_base_row) {
//                     shared_common_row = false;
//                     break;
//                 }
//             }
//         }
//     }
//     __syncthreads();
//
//     bool common_row = shared_common_row;
//     int base_row = shared_base_row;
//
//     // 初始化累加器
//     T_Compute acc = static_cast<T_Compute>(0);
//
//     // K维度分块处理
//     for (int kb = 0; kb < K; kb += TILE_SIZE) {
//         const int kb_end = min(kb + TILE_SIZE, K);
//         const int tile_size = kb_end - kb;
//
//         // 加载A的行块（如果所有行相同，整个块共享）
//         if (common_row && base_row >= 0 && base_row < M) {
//             if (tid < TILE_SIZE) {
//                 int k = kb + tid;
//                 if (k < kb_end) {
//                     A_tile[tid] = static_cast<T_Compute>(A[base_row * lda + k]);
//                 } else {
//                     A_tile[tid] = static_cast<T_Compute>(0);
//                 }
//             }
//         } else if (!common_row && tid < nnz_in_block) {
//             // 每个线程加载自己的A行元素（不缓存）
//         }
//         __syncthreads();
//
//         // 加载B的列块
//         if (tid < nnz_in_block && my_col >= 0 && my_col < N) {
//             const int col = col_idx_buffer[tid];
//             for (int k_idx = 0; k_idx < tile_size; k_idx++) {
//                 int k = kb + k_idx;
//                 if (k < K) {
//                     // 列优先访问: B[k + col * ldb]
//                     B_tile[k_idx * NNZ_PER_BLOCK + tid] =
//                             static_cast<T_Compute>(B[k + col * ldb]);
//                 } else {
//                     B_tile[k_idx * NNZ_PER_BLOCK + tid] = static_cast<T_Compute>(0);
//                 }
//             }
//         }
//         __syncthreads();
//
//         // 计算部分点积
//         if (tid < nnz_in_block) {
//             if (common_row && base_row >= 0 && base_row < M) {
//                 // 使用共享的A行块
//                 for (int k_idx = 0; k_idx < tile_size; k_idx++) {
//                     if (kb + k_idx < K) {
//                         acc += A_tile[k_idx] * B_tile[k_idx * NNZ_PER_BLOCK + tid];
//                     }
//                 }
//             } else if (my_row >= 0 && my_row < M) {
//                 // 使用线程私有的A行
//                 for (int k_idx = 0; k_idx < tile_size; k_idx++) {
//                     int k = kb + k_idx;
//                     if (k < K) {
//                         T_Compute a_val = static_cast<T_Compute>(A[my_row * lda + k]);
//                         acc += a_val * B_tile[k_idx * NNZ_PER_BLOCK + tid];
//                     }
//                 }
//             }
//         }
//         __syncthreads();
//     }
//
//     // 存储结果
//     if (tid < nnz_in_block) {
//         result[start_id + tid] = static_cast<T>(acc);
//     }
// }
//
// // 启动SDDMM内核的封装函数
// cudaError_t launch_sddmm_kernel(
//     int M, int K, int N, int nnz,
//     const void *A, int lda, cudaDataType A_type,
//     const void *B, int ldb, cudaDataType B_type,
//     const int *row_idx, const int *col_idx,
//     void *result, cudaDataType result_type,
//     cudaStream_t stream = 0) {
//     // 检查参数有效性
//     if (M <= 0 || K <= 0 || N <= 0 || nnz <= 0) {
//         return cudaErrorInvalidValue;
//     }
//     if (!A || !B || !row_idx || !col_idx || !result) {
//         return cudaErrorInvalidDevicePointer;
//     }
//
//     // 计算网格和线程块尺寸
//     const int num_blocks = (nnz + NNZ_PER_BLOCK - 1) / NNZ_PER_BLOCK;
//     const dim3 grid_dim(num_blocks);
//     const dim3 block_dim(NNZ_PER_BLOCK);
//
//     // 计算共享内存大小
//     size_t compute_type_size = 0;
//
//     switch (result_type) {
//         case CUDA_R_16F:
//             compute_type_size = sizeof(float); // FP16使用FP32计算
//             break;
//         case CUDA_R_32F:
//             compute_type_size = sizeof(float);
//             break;
//         case CUDA_R_64F:
//             compute_type_size = sizeof(double);
//             break;
//         default:
//             return cudaErrorInvalidValue;
//     }
//
//     const size_t shared_mem_size =
//             TILE_SIZE * compute_type_size + // A_tile
//             TILE_SIZE * NNZ_PER_BLOCK * compute_type_size + // B_tile
//             NNZ_PER_BLOCK * sizeof(int) + // col_idx_buffer
//             128; // 额外填充确保对齐
//
//     // 根据数据类型调用对应kernel
//     switch (result_type) {
//         case CUDA_R_16F: {
//             using T = half;
//             using T_Compute = float;
//             sddmm_kernel<T, T_Compute><<<grid_dim, block_dim, shared_mem_size, stream>>>(
//                 M, K, N, nnz,
//                 static_cast<const T *>(A), lda,
//                 static_cast<const T *>(B), ldb,
//                 row_idx, col_idx,
//                 static_cast<T *>(result));
//             break;
//         }
//         case CUDA_R_32F: {
//             using T = float;
//             using T_Compute = float;
//             sddmm_kernel<T, T_Compute><<<grid_dim, block_dim, shared_mem_size, stream>>>(
//                 M, K, N, nnz,
//                 static_cast<const T *>(A), lda,
//                 static_cast<const T *>(B), ldb,
//                 row_idx, col_idx,
//                 static_cast<T *>(result));
//             break;
//         }
//         case CUDA_R_64F: {
//             using T = double;
//             using T_Compute = double;
//             sddmm_kernel<T, T_Compute><<<grid_dim, block_dim, shared_mem_size, stream>>>(
//                 M, K, N, nnz,
//                 static_cast<const T *>(A), lda,
//                 static_cast<const T *>(B), ldb,
//                 row_idx, col_idx,
//                 static_cast<T *>(result));
//             break;
//         }
//         default:
//             return cudaErrorInvalidValue;
//     }
//
//     return cudaGetLastError();
// }
//
// // 主机端包装函数（含错误检查）
// void sddmm(
//     int M, int K, int N, int nnz,
//     const void *A, int lda, cudaDataType A_type,
//     const void *B, int ldb, cudaDataType B_type,
//     const int *row_idx, const int *col_idx,
//     void *result, cudaDataType result_type) {
//     // 检查一致性
//     if (A_type != result_type || B_type != result_type) {
//         throw std::invalid_argument("Input and output data types must match");
//     }
//
//     // 调用内核启动函数
//     cudaError_t err = launch_sddmm_kernel(
//         M, K, N, nnz,
//         A, lda, A_type,
//         B, ldb, B_type,
//         row_idx, col_idx,
//         result, result_type);
//
//     // 错误处理
//     if (err != cudaSuccess) {
//         // 获取更多错误信息
//         cudaDeviceSynchronize();
//         cudaError_t sync_err = cudaGetLastError();
//         if (sync_err != cudaSuccess) {
//             throw std::runtime_error(std::string("Kernel error: ") + cudaGetErrorString(sync_err));
//         }
//         throw std::runtime_error(std::string("Launch error: ") + cudaGetErrorString(err));
//     }
//
//     // 同步确保完成
//     err = cudaDeviceSynchronize();
//     if (err != cudaSuccess) {
//         throw std::runtime_error(std::string("Synchronization error: ") + cudaGetErrorString(err));
//     }
// }
//
// // 解析MTX文件格式
// struct MatrixMarketInfo {
//     int rows = 0;
//     int cols = 0;
//     int nnz = 0;
//     bool symmetric = false;
//     bool pattern = false;
//     bool real = false;
//     bool integer = false;
//     bool complex = false;
// };
//
// MatrixMarketInfo parse_mtx_header(const std::string &line) {
//     MatrixMarketInfo info;
//     std::istringstream iss(line);
//     std::string token;
//
//     // 读取标记
//     if (!(iss >> token) || token != "%%MatrixMarket") {
//         throw std::runtime_error("Invalid Matrix Market header");
//     }
//
//     // 读取矩阵类型
//     std::string type;
//     if (!(iss >> type)) {
//         throw std::runtime_error("Invalid Matrix Market header");
//     }
//
//     // 读取数据类型
//     std::string data_type;
//     if (!(iss >> data_type)) {
//         throw std::runtime_error("Invalid Matrix Market header");
//     }
//
//     // 读取存储格式
//     std::string storage;
//     if (!(iss >> storage)) {
//         throw std::runtime_error("Invalid Matrix Market header");
//     }
//
//     // 设置数据类型标志
//     if (data_type == "real") info.real = true;
//     else if (data_type == "integer") info.integer = true;
//     else if (data_type == "complex") info.complex = true;
//     else if (data_type == "pattern") info.pattern = true;
//
//     // 设置对称性
//     if (storage == "symmetric") info.symmetric = true;
//
//     return info;
// }
//
// // 从MTX文件中读取稀疏矩阵的非零元素索引
// void read_mtx_file(const std::string &file_path,
//                    std::vector<int> &row_indices,
//                    std::vector<int> &col_indices,
//                    int &M, int &N, int &nnz) {
//     std::ifstream file(file_path);
//     if (!file.is_open()) {
//         throw std::runtime_error("Failed to open file: " + file_path);
//     }
//
//     std::string line;
//     bool header_parsed = false;
//     MatrixMarketInfo info;
//
//     while (std::getline(file, line)) {
//         // 跳过空行
//         if (line.empty()) continue;
//
//         // 跳过注释行
//         if (line[0] == '%') {
//             if (!header_parsed) {
//                 // 解析头部信息
//                 try {
//                     info = parse_mtx_header(line);
//                     header_parsed = true;
//                 } catch (const std::exception &) {
//                     // 非头部注释行，跳过
//                 }
//             }
//             continue;
//         }
//
//         // 第一行有效数据：矩阵维度
//         if (M == 0) {
//             std::istringstream iss(line);
//             if (!(iss >> M >> N >> nnz)) {
//                 throw std::runtime_error("Failed to parse matrix dimensions");
//             }
//
//             // 为对称矩阵调整非零元数量
//             if (info.symmetric) nnz *= 2;
//
//             row_indices.reserve(nnz);
//             col_indices.reserve(nnz);
//             continue;
//         }
//
//         // 解析非零元素行
//         std::istringstream iss(line);
//         int i, j;
//         double val;
//
//         // 尝试读取前两个整数值（行和列索引）
//         if (!(iss >> i >> j)) {
//             throw std::runtime_error("Failed to parse matrix entry: " + line);
//         }
//
//         // MTX使用1-based索引，转换为0-based
//         row_indices.push_back(i - 1);
//         col_indices.push_back(j - 1);
//
//         // 对于对称矩阵，添加对称项
//         if (info.symmetric && (i != j)) {
//             row_indices.push_back(j - 1);
//             col_indices.push_back(i - 1);
//         }
//     }
//
//     if (row_indices.size() != static_cast<size_t>(nnz)) {
//         throw std::runtime_error("Unexpected number of non-zero elements: expected " +
//                                  std::to_string(nnz) + ", got " +
//                                  std::to_string(row_indices.size()));
//     }
//
//     // 关闭文件
//     file.close();
// }
//
// // 测试用例 - 从文件读取稀疏矩阵
// void test_sddmm_from_file(const std::string &mtx_file, int K = 128, bool generate_random = true,
//                           bool verify = true, int test_samples = 10000) {
//     // 读取稀疏矩阵信息
//     std::vector<int> row_indices;
//     std::vector<int> col_indices;
//     int M, N, nnz;
//
//     std::cout << "Reading MTX file: " << mtx_file << std::endl;
//     read_mtx_file(mtx_file, row_indices, col_indices, M, N, nnz);
//     std::cout << "Matrix info: " << M << " x " << N
//             << ", Non-zero entries: " << nnz << std::endl;
//
//     // 打印内存需求估算
//     size_t mem_required =
//             M * K * sizeof(float) + // 矩阵A
//             K * N * sizeof(float) + // 矩阵B
//             nnz * sizeof(int) * 2 + // 行列索引
//             nnz * sizeof(float); // 结果
//
//     printf("Memory required: %.2f GB\n", mem_required / (1024.0 * 1024.0 * 1024.0));
//
//     // 检查GPU内存
//     size_t free_mem, total_mem;
//     cudaMemGetInfo(&free_mem, &total_mem);
//     printf("Available GPU memory: %.2f GB\n", free_mem / (1024.0 * 1024.0 * 1024.0));
//     if (mem_required > free_mem * 0.9) {
//         throw std::runtime_error("Insufficient GPU memory");
//     }
//
//     // 分配主机内存
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(K * N);
//     std::vector<float> h_result(nnz);
//     std::vector<float> h_expected;
//
//     if (verify) {
//         h_expected.resize(nnz);
//     }
//
//     // 随机初始化矩阵
//
//     if (generate_random) {
//         std::srand(12345);
//         for (int i = 0; i < M * K; i++) {
//             h_A[i] = static_cast<float>(rand()) / RAND_MAX;
//         }
//         for (int i = 0; i < K * N; i++) {
//             // 列优先存储
//             h_B[i] = static_cast<float>(rand()) / RAND_MAX;
//         }
//     }
//
//     // 计算CPU参考结果
//     if (verify) {
//         std::cout << "Computing CPU reference values..." << std::endl;
//         for (int idx = 0; idx < nnz; idx++) {
//             int row = row_indices[idx];
//             int col = col_indices[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; k++) {
//                 sum += h_A[row * K + k] * h_B[col * K + k]; // 列优先访问
//             }
//             h_expected[idx] = sum;
//         }
//     }
//
//     // 分配设备内存
//     float *d_A, *d_B, *d_result;
//     int *d_row_idx, *d_col_idx;
//
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, K * N * sizeof(float));
//     cudaMalloc(&d_row_idx, nnz * sizeof(int));
//     cudaMalloc(&d_col_idx, nnz * sizeof(int));
//     cudaMalloc(&d_result, nnz * sizeof(float));
//
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//
//     // 记录开始时间
//     cudaEventRecord(start);
//
//     // 复制数据到设备
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), K * N * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_row_idx, row_indices.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_col_idx, col_indices.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//
//     // 执行SDDMM
//     try {
//         // 创建事件计时器
//         // cudaEvent_t start, stop;
//         // cudaEventCreate(&start);
//         // cudaEventCreate(&stop);
//         //
//         // // 记录开始时间
//         // cudaEventRecord(start);
//
//         // 注意：B矩阵是列优先存储，ldb = K
//         sddmm(M, K, N, nnz,
//               d_A, K, CUDA_R_32F, // A行优先，lda=K
//               d_B, K, CUDA_R_32F, // B列优先，ldb=K
//               d_row_idx, d_col_idx,
//               d_result, CUDA_R_32F);
//
//         // // 记录结束时间
//         // cudaEventRecord(stop);
//         // cudaEventSynchronize(stop);
//         //
//         // // 计算执行时间
//         // float milliseconds = 0;
//         // cudaEventElapsedTime(&milliseconds, start, stop);
//         //
//         // printf("SDDMM execution time: %.3f ms\n", milliseconds);
//         // printf("GFLOPS: %.2f\n", (2.0 * nnz * K) / (milliseconds * 1e6));
//
//         // 复制结果回主机
//         cudaMemcpy(h_result.data(), d_result, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//         // 记录结束时间
//         cudaEventRecord(stop);
//         cudaEventSynchronize(stop);
//
//         // 计算执行时间
//         float milliseconds = 0;
//         cudaEventElapsedTime(&milliseconds, start, stop);
//
//         printf("SDDMM execution time: %.3f ms\n", milliseconds);
//         printf("GFLOPS: %.2f\n", (2.0 * nnz * K) / (milliseconds * 1e6));
//
//         // 验证结果 (部分样本)
//         if (verify) {
//             int num_errors = 0;
//             const float tolerance = 1e-3f;
//             const int samples = std::min(test_samples, nnz);
//
//             for (int i = 0; i < samples; i++) {
//                 int idx = rand() % nnz; // 随机选择样本
//                 float expected = h_expected[idx];
//                 float computed = h_result[idx];
//                 float diff = fabs(computed - expected);
//                 float rel_error = diff / (fabs(expected) + 1e-6f);
//
//                 if (rel_error > tolerance) {
//                     if (num_errors < 10) {
//                         printf("Error at %d: Computed %.6f, Expected %.6f (Rel error %.6f)\n",
//                                idx, computed, expected, rel_error);
//                     }
//                     num_errors++;
//                 }
//             }
//
//             if (num_errors == 0) {
//                 printf("SDDMM test passed! %d samples verified.\n", samples);
//             } else {
//                 printf("SDDMM test failed! %d errors in %d samples.\n", num_errors, samples);
//             }
//         }
//     } catch (const std::exception &e) {
//         printf("SDDMM failed: %s\n", e.what());
//     }
//
//     // 清理资源
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(d_row_idx);
//     cudaFree(d_col_idx);
//     cudaFree(d_result);
// }
//
// // 打印帮助信息
// void print_help() {
//     std::cout << "Usage: sddmm_test <mtx_file> [options]\n"
//             << "Options:\n"
//             << "  -k <value>       Set K dimension (default: 128)\n"
//             << "  --no-gen         Do not generate random matrices (requires input files)\n"
//             << "  --no-verify      Disable result verification\n"
//             << "  --samples <n>    Number of test samples (default: 1000)\n"
//             << "  -h, --help       Display this help message\n";
// }
//
// // 主函数
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "Error: Missing MTX file path" << std::endl;
//         print_help();
//         return 1;
//     }
//
//     // 默认参数
//     std::string mtx_file;
//     int K = 128;
//     bool generate_random = true;
//     bool verify = true;
//     int test_samples = 10000;
//
//     // 解析命令行参数
//     for (int i = 1; i < argc; i++) {
//         std::string arg = argv[i];
//
//         if (arg == "-h" || arg == "--help") {
//             print_help();
//             return 0;
//         } else if (arg == "-k") {
//             if (i + 1 < argc) {
//                 K = std::atoi(argv[++i]);
//             } else {
//                 std::cerr << "Error: Missing value for -k" << std::endl;
//                 return 1;
//             }
//         } else if (arg == "--no-gen") {
//             generate_random = false;
//         } else if (arg == "--no-verify") {
//             verify = false;
//         } else if (arg == "--samples") {
//             if (i + 1 < argc) {
//                 test_samples = std::atoi(argv[++i]);
//             } else {
//                 std::cerr << "Error: Missing value for --samples" << std::endl;
//                 return 1;
//             }
//         } else if (arg[0] != '-') {
//             mtx_file = arg;
//         } else {
//             std::cerr << "Error: Unknown option " << arg << std::endl;
//             return 1;
//         }
//     }
//
//     // 检查文件是否存在
//     std::ifstream test_file(mtx_file);
//     if (!test_file.good()) {
//         std::cerr << "Error: MTX file not found: " << mtx_file << std::endl;
//         return 1;
//     }
//
//     try {
//         test_sddmm_from_file(mtx_file, K, generate_random, verify, test_samples);
//     } catch (const std::exception &e) {
//         std::cerr << "Error: " << e.what() << std::endl;
//         return 1;
//     }
//
//     return 0;
// }
