// #include <iostream>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <chrono>
// #include <cuda_runtime.h>
// #include <sstream>
// #include <string>
// #include "sputnik/cuda_utils.h"
// #include "sputnik/sddmm/cuda_sddmm.h"
// #include "sputnik/sputnik.h"
//
// // COO格式数据结构
// struct COOMatrix {
//     int M, N, nnz;
//     std::vector<int> rows;
//     std::vector<int> cols;
// };
//
// // CSR格式数据结构
// struct CSRMatrix {
//     int M, N, nnz;
//     std::vector<int> row_ptr;
//     std::vector<int> col_idx;
// };
//
// // 加载矩阵数据（Matrix Market格式）
// COOMatrix LoadCOO(const char *filename) {
//     COOMatrix coo;
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     // 跳过注释行
//     std::string line;
//     while (std::getline(file, line) && line[0] == '%');
//
//     // 读取矩阵头
//     std::istringstream header(line);
//     header >> coo.N >> coo.M >> coo.nnz;
//     printf("N = %d, M = %d\n", coo.N, coo.M);
//
//     // 读取非零元素
//     coo.rows.resize(coo.nnz);
//     coo.cols.resize(coo.nnz);
//     for (int i = 0; i < coo.nnz; ++i) {
//         int row, col;
//         file >> col >> row;
//         coo.rows[i] = row - 1; // 转换为0-based索引
//         coo.cols[i] = col - 1;
//     }
//     return coo;
// }
//
// // COO转CSR格式
// CSRMatrix COO_to_CSR(const COOMatrix &coo) {
//     CSRMatrix csr;
//     csr.M = coo.M;
//     csr.N = coo.N;
//     csr.nnz = coo.nnz;
//
//     std::vector<int> row_counts(coo.M, 0);
//     for (int row: coo.rows) {
//         row_counts[row]++;
//     }
//
//     csr.row_ptr.resize(coo.M + 1);
//     csr.row_ptr[0] = 0;
//     for (int i = 0; i < coo.M; ++i) {
//         csr.row_ptr[i + 1] = csr.row_ptr[i] + row_counts[i];
//     }
//
//     std::vector<std::vector<int> > cols_per_row(coo.M);
//     for (size_t i = 0; i < coo.rows.size(); ++i) {
//         cols_per_row[coo.rows[i]].push_back(coo.cols[i]);
//     }
//
//     csr.col_idx.resize(coo.nnz);
//     int idx = 0;
//     for (int row = 0; row < coo.M; ++row) {
//         std::sort(cols_per_row[row].begin(), cols_per_row[row].end());
//         for (int col: cols_per_row[row]) {
//             csr.col_idx[idx++] = col;
//         }
//     }
//     return csr;
// }
//
// // CPU
// void sddmm_cpu(int M, int K, int N,
//                const int *row_ptr, const int *col_idx,
//                const float *A, const float *B,
//                float *output) {
//     for (int row = 0; row < M; ++row) {
//         for (int j = row_ptr[row]; j < row_ptr[row + 1]; ++j) {
//             int col = col_idx[j];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             output[j] = sum;
//         }
//     }
// }
//
// // 验证结果
// bool verify_results(const float *cpu, const float *gpu, int n, float eps = 1e-4f) {
//     for (int i = 0; i < n; ++i) {
//         if (std::abs(cpu[i] - gpu[i]) > eps) {
//             std::cout << "结果不匹配在位置 " << i
//                     << ": CPU=" << cpu[i]
//                     << " GPU=" << gpu[i]
//                     << std::endl;
//             return false;
//         }
//     }
//     return true;
// }
//
// int main(int argc, char **argv) {
//     if (argc != 5) {
//         std::cerr << "Usage: " << argv[0]
//                 << " <matrix file> <K> <iterations> <tile size>\n"
//                 << "Example: ./sddmm_test matrix.mtx 256 100 32\n";
//         return 1;
//     }
//
//     const char *filename = argv[1];
//     const int K = atoi(argv[2]);
//     const int iterations = atoi(argv[3]);
//     const int tile_size = atoi(argv[4]);
//
//     COOMatrix coo = LoadCOO(filename);
//     CSRMatrix csr = COO_to_CSR(coo);
//     const int M = csr.M;
//     const int N = csr.N;
//     const int nnz = csr.nnz;
//
//     std::vector<float> A(M * K);
//     std::vector<float> B(N * K);
//     std::generate(A.begin(), A.end(), [] { return rand() / (float) RAND_MAX; });
//     std::generate(B.begin(), B.end(), [] { return rand() / (float) RAND_MAX; });
//
//     // 设备内存分配（增加错误检查）
//     int *d_row_ptr, *d_col_idx, *d_row_indices;
//     float *d_A, *d_B, *d_output;
//     cudaMalloc(&d_row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&d_col_idx, nnz * sizeof(int));
//     cudaMalloc(&d_row_indices, M * sizeof(int)); // 行索引数组
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//     cudaMalloc(&d_output, nnz * sizeof(float));
//
//     cudaEvent_t h2d_start, h2d_stop;
//     cudaEventCreate(&h2d_start);
//     cudaEventCreate(&h2d_stop);
//     float total_h2d_time = 0.0f;
//
//     // 生成行索引数组
//     std::vector<int> row_indices(M);
//     std::iota(row_indices.begin(), row_indices.end(), 0);
//
//     cudaEventRecord(h2d_start);
//     cudaMemcpy(d_row_indices, row_indices.data(), M * sizeof(int), cudaMemcpyHostToDevice);
//     cudaEventRecord(h2d_stop);
//     cudaEventSynchronize(h2d_stop);
//     float elapsed_time = 0;
//     cudaEventElapsedTime(&elapsed_time, h2d_start, h2d_stop);
//     total_h2d_time += elapsed_time;
//
//     cudaEventRecord(h2d_start);
//     // 拷贝数据到设备
//     cudaMemcpy(d_row_ptr, csr.row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_col_idx, csr.col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_A, A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaEventRecord(h2d_stop);
//     cudaEventSynchronize(h2d_stop);
//     cudaEventElapsedTime(&elapsed_time, h2d_start, h2d_stop);
//     total_h2d_time += elapsed_time;
//
//     // CPU参考结果
//     std::vector<float> cpu_result(nnz);
//     sddmm_cpu(M, K, N, csr.row_ptr.data(), csr.col_idx.data(),
//               A.data(), B.data(), cpu_result.data());
//
//     // CUDA流设置
//     cudaStream_t stream;
//     cudaStreamCreate(&stream);
//
//     // 预热运行（使用正确参数）
//     sputnik::CudaSddmm( // 添加 sputnik 命名空间
//         M, K, N, nnz,
//         d_row_indices,
//         d_row_ptr,
//         d_col_idx,
//         d_A, d_B,
//         d_output,
//         stream
//     );
//     cudaStreamSynchronize(stream);
//
//     // 性能测试（修正参数顺序）
//     float tot_ms;
//     cudaEvent_t event1, event2;
//     cudaEventCreate(&event1);
//     cudaEventCreate(&event2);
//
//     cudaDeviceSynchronize();
//     cudaEventRecord(event1, 0);
//     //auto start = std::chrono::high_resolution_clock::now();
//     for (int i = 0; i < iterations; i++) {
//         sputnik::CudaSddmm(
//             M, K, N, nnz,
//             d_row_indices,
//             d_row_ptr,
//             d_col_idx,
//             d_A,
//             d_B,
//             d_output,
//             stream
//         );
//     }
//     //cudaStreamSynchronize(stream);
//     cudaEventRecord(event2, 0);
//
//     cudaEventSynchronize(event1);
//     cudaEventSynchronize(event2);
//     cudaEventElapsedTime(&tot_ms, event1, event2);
//     cudaDeviceSynchronize();
//     //auto end = std::chrono::high_resolution_clock::now();
//
//     // 结果验证
//     std::vector<float> gpu_result(nnz);
//     cudaMemcpy(gpu_result.data(), d_output, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     bool success = verify_results(cpu_result.data(), gpu_result.data(), nnz);
//     std::cout << "验证结果: " << (success ? "通过" : "失败") << std::endl;
//
//     // 性能计算
//     //float avg_time = std::chrono::duration<float>(end - start).count() / iterations;
//     //std::cout << "平均耗时: " << avg_time * 1000 << " ms\n";
//     //std::cout << "吞吐量: " << (2.0 * nnz * K) / (avg_time * 1e9) << " GFLOP/s\n";
//     double gflops = (double) iterations * (double) nnz * 2 * K / tot_ms / 1000000;
//     printf("平均耗时: %f ms\n", tot_ms / iterations);
//     std::cout << "Host-to-Device总传输时间: " << total_h2d_time << " ms\n";
//
//     // 资源释放
//     cudaFree(d_row_ptr);
//     // [其他资源释放...]
//     return success ? 0 : 1;
// }
