#include "sddmm_optimized.cu"
#include <chrono>

// CPU参考实现 (用于验证)
void sddmm_cpu_reference(
    const float *A, const float *B,
    const int *row_ptr, const int *col_idx,
    float *values, int M, int N, int K) {
    for (int row = 0; row < M; ++row) {
        int start = row_ptr[row];
        int end = row_ptr[row + 1];
        for (int idx = start; idx < end; ++idx) {
            int col = col_idx[idx];
            float sum = 0.0f;
            for (int k = 0; k < K; ++k) {
                sum += A[row * K + k] * B[col * K + k];
            }
            values[idx] = sum;
        }
    }
}

// 从.mtx文件加载矩阵
void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
                     std::vector<int> &rows, std::vector<int> &cols) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filename << std::endl;
        exit(1);
    }

    std::string line;
    while (std::getline(file, line) && line[0] == '%');

    std::istringstream header(line);
    header >> M >> N >> nnz;

    rows.resize(nnz);
    cols.resize(nnz);

    for (int i = 0; i < nnz; ++i) {
        int row, col;
        file >> row >> col;
        std::getline(file, line);
        rows[i] = row - 1;
        cols[i] = col - 1;
    }
    file.close();
}

// COO转CSR格式
void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
                int M, int nnz,
                std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
    csr_row_ptr.assign(M + 1, 0);
    csr_col_idx.resize(nnz);

    for (int i = 0; i < nnz; ++i) {
        csr_row_ptr[rows[i] + 1]++;
    }

    for (int i = 0; i < M; ++i) {
        csr_row_ptr[i + 1] += csr_row_ptr[i];
    }

    std::vector<int> row_count = csr_row_ptr;
    for (int i = 0; i < nnz; ++i) {
        int row = rows[i];
        int index = row_count[row];
        csr_col_idx[index] = cols[i];
        row_count[row]++;
    }
}

// 性能测试函数
void performance_test(const std::string &matrix_file, int K) {
    std::cout << "\n=== SDDMM 性能优化测试 ===" << std::endl;
    std::cout << "矩阵文件: " << matrix_file << std::endl;
    std::cout << "K维度: " << K << std::endl;

    // 加载矩阵
    int M, N, nnz;
    std::vector<int> coo_rows, coo_cols;
    load_coo_matrix(matrix_file, M, N, nnz, coo_rows, coo_cols);

    std::vector<int> h_csr_row_ptr, h_csr_col_idx;
    coo_to_csr(coo_rows, coo_cols, M, nnz, h_csr_row_ptr, h_csr_col_idx);

    // 生成随机密集矩阵
    std::vector<float> h_A(M * K);
    std::vector<float> h_B(N * K);
    std::srand(std::time(nullptr));
    for (int i = 0; i < M * K; ++i) h_A[i] = (rand() % 100) / 100.0f;
    for (int i = 0; i < N * K; ++i) h_B[i] = (rand() % 100) / 100.0f;

    // GPU内存分配
    float *d_A, *d_B;
    cudaMalloc(&d_A, M * K * sizeof(float));
    cudaMalloc(&d_B, N * K * sizeof(float));
    cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
    cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);

    CSRMatrix sparse;
    sparse.rows = M;
    sparse.cols = N;
    sparse.nnz = nnz;
    cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
    cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
    cudaMalloc(&sparse.values, nnz * sizeof(float));

    cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
    cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);

    printf("矩阵信息: M=%d, N=%d, K=%d, nnz=%d\n", M, N, K, nnz);
    printf("稀疏度: %.4f%%\n", 100.0 * nnz / (double(M) * N));

    // 预热GPU
    std::cout << "\n预热GPU..." << std::endl;
    for (int i = 0; i < 3; i++) {
        cudaMemset(sparse.values, 0, nnz * sizeof(float));
        sddmm_csr_advanced(d_A, d_B, sparse, K);
        cudaDeviceSynchronize();
    }

    // 性能测试
    std::cout << "\n开始性能测试..." << std::endl;
    
    const int num_runs = 5;
    std::vector<float> times;
    
    for (int run = 0; run < num_runs; run++) {
        cudaMemset(sparse.values, 0, nnz * sizeof(float));
        
        cudaEvent_t start, stop;
        cudaEventCreate(&start);
        cudaEventCreate(&stop);
        
        cudaEventRecord(start);
        sddmm_csr_advanced(d_A, d_B, sparse, K);
        cudaEventRecord(stop);
        
        cudaEventSynchronize(stop);
        float milliseconds = 0;
        cudaEventElapsedTime(&milliseconds, start, stop);
        times.push_back(milliseconds);
        
        printf("运行 %d: %.3f ms\n", run + 1, milliseconds);
        
        cudaEventDestroy(start);
        cudaEventDestroy(stop);
    }

    // 计算统计信息
    float min_time = *std::min_element(times.begin(), times.end());
    float max_time = *std::max_element(times.begin(), times.end());
    float avg_time = std::accumulate(times.begin(), times.end(), 0.0f) / num_runs;
    
    printf("\n性能统计:\n");
    printf("最小时间: %.3f ms\n", min_time);
    printf("最大时间: %.3f ms\n", max_time);
    printf("平均时间: %.3f ms\n", avg_time);
    
    // 计算GFLOPS
    double gflops = (2.0 * nnz * K) / (min_time * 1e6);
    printf("峰值性能: %.2f GFLOPS\n", gflops);

    // 验证正确性
    std::cout << "\n验证计算正确性..." << std::endl;
    std::vector<float> h_values_gpu(nnz);
    cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);

    std::vector<float> h_values_cpu(nnz, 0.0f);
    auto cpu_start = std::chrono::high_resolution_clock::now();
    sddmm_cpu_reference(h_A.data(), h_B.data(),
                        h_csr_row_ptr.data(), h_csr_col_idx.data(),
                        h_values_cpu.data(), M, N, K);
    auto cpu_end = std::chrono::high_resolution_clock::now();
    
    auto cpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(cpu_end - cpu_start);
    printf("CPU参考实现时间: %ld ms\n", cpu_duration.count());
    printf("GPU加速比: %.2fx\n", float(cpu_duration.count()) / min_time);

    int correct = 0;
    float max_error = 0.0f;
    for (int i = 0; i < nnz; ++i) {
        float diff = fabs(h_values_cpu[i] - h_values_gpu[i]);
        if (diff < 1e-4) {
            correct++;
        }
        if (diff > max_error) {
            max_error = diff;
        }
    }

    printf("验证结果:\n");
    printf("最大绝对误差: %e\n", max_error);
    printf("正确率: %.2f%%\n", (100.0f * correct / nnz));

    // 释放内存
    cudaFree(d_A);
    cudaFree(d_B);
    cudaFree(sparse.row_ptr);
    cudaFree(sparse.col_idx);
    cudaFree(sparse.values);
}

__global__ void warmup_kernel() {}

int main(int argc, char **argv) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
        return 1;
    }

    std::string filename = argv[1];
    int K = (argc > 2) ? std::atoi(argv[2]) : 128;

    // 显示GPU信息
    int device;
    cudaGetDevice(&device);
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device);
    
    printf("GPU信息: %s\n", prop.name);
    printf("计算能力: %d.%d\n", prop.major, prop.minor);
    printf("全局内存: %.1f GB\n", prop.totalGlobalMem / (1024.0 * 1024.0 * 1024.0));
    printf("共享内存/块: %zu KB\n", prop.sharedMemPerBlock / 1024);
    printf("最大线程/块: %d\n", prop.maxThreadsPerBlock);

    performance_test(filename, K);

    return 0;
}
