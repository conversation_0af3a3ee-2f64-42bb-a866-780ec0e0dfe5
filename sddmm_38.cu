// #include <iostream>
// #include <vector>
// #include <string>
// #include <fstream>
// #include <sstream>
// #include <algorithm>
// #include <numeric>
// #include <cmath>
// #include <chrono>
// #include <ctime>
// #include <cstdlib>
// #include <limits>
// #include <iomanip> // For std::setprecision
//
// #include <cuda_runtime.h>
// #include <cassert>
//
// // =================================================================
// // 常量定义
// // =================================================================
// const int MEDIUM_DENSITY_THRESHOLD = 32;
// const int HIGH_DENSITY_THRESHOLD = 256;
// const int VECTOR_KERNEL_BLOCK_SIZE = 128;
// const int FINAL_HIGH_DENSITY_BLOCK_SIZE = 1024;
//
// #define CUDA_CHECK(err) { \
//     cudaError_t e = err; \
//     if (e != cudaSuccess) { \
//         printf("Cuda error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
//         exit(EXIT_FAILURE); \
//     } \
// }
//
// struct CSRMatrix {
//     int *row_ptr, *col_idx;
//     float *values;
//     int rows, cols, nnz;
// };
//
// // =================================================================
// // CUDA 核函数
// // =================================================================
//
// // --- 分类核函数 ---
// __global__ void classify_rows_multi_level_kernel(
//     const int *row_ptr, int rows,
//     int *d_low_rows, int *d_medium_rows, int *d_high_rows,
//     int *low_count, int *medium_count, int *high_count) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row >= rows) return;
//     int nnz = row_ptr[row + 1] - row_ptr[row];
//     if (nnz > 0 && nnz <= MEDIUM_DENSITY_THRESHOLD) {
//         int pos = atomicAdd(low_count, 1);
//         d_low_rows[pos] = row;
//     } else if (nnz > MEDIUM_DENSITY_THRESHOLD && nnz <= HIGH_DENSITY_THRESHOLD) {
//         int pos = atomicAdd(medium_count, 1);
//         d_medium_rows[pos] = row;
//     } else if (nnz > HIGH_DENSITY_THRESHOLD) {
//         int pos = atomicAdd(high_count, 1);
//         d_high_rows[pos] = row;
//     }
// }
//
// // --- 【低密度区专用】向量内核 ---
// __global__ void sddmm_vector_kernel(
//     const float *__restrict__ A, const float *__restrict__ B,
//     const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices, float *__restrict__ result,
//     int num_rows, int K) {
//     int row_idx_in_group = blockIdx.x;
//     if (row_idx_in_group >= num_rows) return;
//     extern __shared__ float a_row_s[];
//     int row = row_indices[row_idx_in_group];
//     for (int k = threadIdx.x; k < K; k += blockDim.x) a_row_s[k] = A[(size_t) row * K + k];
//     __syncthreads();
//     int row_start = row_ptr[row];
//     int row_end = row_ptr[row + 1];
//     for (int i = row_start + threadIdx.x; i < row_end; i += blockDim.x) {
//         int col = col_idx[i];
//         float sum = 0.0f;
//         for (int k = 0; k < K; ++k) sum += a_row_s[k] * B[(size_t) col * K + k];
//         result[i] = sum;
//     }
// }
//
// // --- 【中/高密度区专用】Warp协作内核 ---
// __global__ void sddmm_final_high_density_kernel(
//     const float *__restrict__ A, const float *__restrict__ B,
//     const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
//     const int *__restrict__ d_rows, float *__restrict__ result,
//     int num_rows, int K) {
//     int row_idx = blockIdx.x;
//     if (row_idx >= num_rows) return;
//     extern __shared__ float a_row_s[];
//     int row = d_rows[row_idx];
//     for (int k = threadIdx.x; k < K; k += blockDim.x) a_row_s[k] = A[(size_t) row * K + k];
//     __syncthreads();
//     int warp_id = threadIdx.x / 32, lane_id = threadIdx.x % 32;
//     int warps_in_block = blockDim.x / 32;
//     int row_start = row_ptr[row], nnz_in_row = row_ptr[row + 1] - row_start;
//     for (int nnz_offset = warp_id; nnz_offset < nnz_in_row; nnz_offset += warps_in_block) {
//         int global_idx = row_start + nnz_offset;
//         int col = col_idx[global_idx];
//         float partial_sum = 0.0f;
//         for (int k = lane_id; k < K; k += 32) partial_sum += a_row_s[k] * B[(size_t) col * K + k];
//         for (int offset = 16; offset > 0; offset /= 2) partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
//         if (lane_id == 0) result[global_idx] = partial_sum;
//     }
// }
//
// // =================================================================
// // 主控函数 (支持分区计时)
// // =================================================================
// void sddmm_ultimate_final(
//     const float *d_A, const float *d_B, CSRMatrix &sparse, int K, size_t shared_mem_per_block,
//     float &time_low, float &time_med, float &time_high) {
//     int *d_low_rows, *d_medium_rows, *d_high_rows;
//     int *d_low_count, *d_medium_count, *d_high_count;
//     CUDA_CHECK(cudaMalloc(&d_low_rows, (size_t)sparse.rows * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_medium_rows, (size_t)sparse.rows * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_high_rows, (size_t)sparse.rows * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_low_count, sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_medium_count, sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_high_count, sizeof(int)));
//     CUDA_CHECK(cudaMemset(d_low_count, 0, sizeof(int)));
//     CUDA_CHECK(cudaMemset(d_medium_count, 0, sizeof(int)));
//     CUDA_CHECK(cudaMemset(d_high_count, 0, sizeof(int)));
//
//     cudaStream_t streams[3];
//     for (int i = 0; i < 3; ++i) CUDA_CHECK(cudaStreamCreate(&streams[i]));
//
//     cudaEvent_t start_low, stop_low, start_med, stop_med, start_high, stop_high;
//     CUDA_CHECK(cudaEventCreate(&start_low));
//     CUDA_CHECK(cudaEventCreate(&stop_low));
//     CUDA_CHECK(cudaEventCreate(&start_med));
//     CUDA_CHECK(cudaEventCreate(&stop_med));
//     CUDA_CHECK(cudaEventCreate(&start_high));
//     CUDA_CHECK(cudaEventCreate(&stop_high));
//
//     dim3 block_classify(256);
//     dim3 grid_classify((sparse.rows + block_classify.x - 1) / block_classify.x);
//     classify_rows_multi_level_kernel<<<grid_classify, block_classify, 0, streams[0]>>>(
//         sparse.row_ptr, sparse.rows, d_low_rows, d_medium_rows, d_high_rows,
//         d_low_count, d_medium_count, d_high_count);
//
//     int h_counts[3];
//     CUDA_CHECK(cudaMemcpyAsync(&h_counts[0], d_low_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
//     CUDA_CHECK(cudaMemcpyAsync(&h_counts[1], d_medium_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
//     CUDA_CHECK(cudaMemcpyAsync(&h_counts[2], d_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
//     CUDA_CHECK(cudaStreamSynchronize(streams[0]));
//
//     printf("行分类结果: 低密度(<=%d)=%d, 中密度(<=%d)=%d, 高密度(>%d)=%d\n",
//            MEDIUM_DENSITY_THRESHOLD, h_counts[0], HIGH_DENSITY_THRESHOLD, h_counts[1], HIGH_DENSITY_THRESHOLD,
//            h_counts[2]);
//
//     size_t required_mem = (size_t) K * sizeof(float);
//     if (required_mem > shared_mem_per_block) {
//         printf("警告: K=%d 过大，无法使用共享内存优化。\n", K);
//         time_low = time_med = time_high = -1.0f;
//     } else {
//         if (h_counts[0] > 0) {
//             CUDA_CHECK(cudaEventRecord(start_low, streams[0]));
//             dim3 block(VECTOR_KERNEL_BLOCK_SIZE);
//             dim3 grid(h_counts[0]);
//             sddmm_vector_kernel<<<grid, block, required_mem, streams[0]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_low_rows,
//                 sparse.values, h_counts[0], K);
//             CUDA_CHECK(cudaEventRecord(stop_low, streams[0]));
//         }
//         if (h_counts[1] > 0) {
//             CUDA_CHECK(cudaEventRecord(start_med, streams[1]));
//             dim3 block(FINAL_HIGH_DENSITY_BLOCK_SIZE);
//             dim3 grid(h_counts[1]);
//             sddmm_final_high_density_kernel<<<grid, block, required_mem, streams[1]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_medium_rows,
//                 sparse.values, h_counts[1], K);
//             CUDA_CHECK(cudaEventRecord(stop_med, streams[1]));
//         }
//         if (h_counts[2] > 0) {
//             if (h_counts[1] > 0) printf("高密度区策略: Warp协作模型\n");
//             CUDA_CHECK(cudaEventRecord(start_high, streams[2]));
//             dim3 block(FINAL_HIGH_DENSITY_BLOCK_SIZE);
//             dim3 grid(h_counts[2]);
//             sddmm_final_high_density_kernel<<<grid, block, required_mem, streams[2]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_high_rows,
//                 sparse.values, h_counts[2], K);
//             CUDA_CHECK(cudaEventRecord(stop_high, streams[2]));
//         }
//     }
//
//     for (int i = 0; i < 3; ++i) CUDA_CHECK(cudaStreamSynchronize(streams[i]));
//
//     time_low = time_med = time_high = 0.0f;
//     if (h_counts[0] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_low, start_low, stop_low));
//     if (h_counts[1] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_med, start_med, stop_med));
//     if (h_counts[2] > 0) CUDA_CHECK(cudaEventElapsedTime(&time_high, start_high, stop_high));
//
//     CUDA_CHECK(cudaEventDestroy(start_low));
//     CUDA_CHECK(cudaEventDestroy(stop_low));
//     CUDA_CHECK(cudaEventDestroy(start_med));
//     CUDA_CHECK(cudaEventDestroy(stop_med));
//     CUDA_CHECK(cudaEventDestroy(start_high));
//     CUDA_CHECK(cudaEventDestroy(stop_high));
//     for (int i = 0; i < 3; ++i) CUDA_CHECK(cudaStreamDestroy(streams[i]));
//     CUDA_CHECK(cudaFree(d_low_rows));
//     CUDA_CHECK(cudaFree(d_medium_rows));
//     CUDA_CHECK(cudaFree(d_high_rows));
//     CUDA_CHECK(cudaFree(d_low_count));
//     CUDA_CHECK(cudaFree(d_medium_count));
//     CUDA_CHECK(cudaFree(d_high_count));
// }
//
//
// void sddmm_cpu_reference(const float *A, const float *B, const int *row_ptr, const int *col_idx, float *values, int M,
//                          int N, int K) {
// #pragma omp parallel for schedule(dynamic)
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
// #pragma GCC ivdep
//             for (int k = 0; k < K; ++k) {
//                 sum += A[(size_t) row * K + k] * B[(size_t) col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz, std::vector<int> &coo_rows,
//                      std::vector<int> &coo_cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "错误: 无法打开矩阵文件: " << filename << std::endl;
//         exit(1);
//     }
//     while (file.peek() == '%') file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
//     file >> M >> N >> nnz;
//     coo_rows.resize(nnz);
//     coo_cols.resize(nnz);
//     for (int i = 0; i < nnz; ++i) {
//         int r, c;
//         file >> r >> c;
//         coo_rows[i] = r - 1;
//         coo_cols[i] = c - 1;
//         file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
//     }
//     file.close();
// }
//
// void coo_to_csr(int M, int nnz, const std::vector<int> &coo_rows_in, const std::vector<int> &coo_cols_in,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
//     csr_row_ptr.assign(M + 1, 0);
//     std::vector<std::pair<int, int> > coo(nnz);
//     for (int i = 0; i < nnz; ++i) coo[i] = {coo_rows_in[i], coo_cols_in[i]};
//     std::sort(coo.begin(), coo.end());
//     csr_col_idx.resize(nnz);
//     for (int i = 0; i < nnz; ++i) {
//         csr_col_idx[i] = coo[i].second;
//         csr_row_ptr[coo[i].first + 1]++;
//     }
//     for (int i = 0; i < M; ++i) csr_row_ptr[i + 1] += csr_row_ptr[i];
// }
//
// __global__ void warmup_kernel() {
// }
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr));
//     std::string filename = argv[1];
//     int K = (argc > 2) ? std::atoi(argv[2]) : 128;
//
//     int device;
//     cudaGetDevice(&device);
//     cudaDeviceProp prop;
//     cudaGetDeviceProperties(&prop, device);
//     printf("=== GPU信息 ===\n设备名称: %s (Compute Capability %d.%d)\n", prop.name, prop.major, prop.minor);
//     printf("设备共享内存/块: %zu bytes\n\n", prop.sharedMemPerBlock);
//
//     int M, N, nnz;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(M, nnz, coo_rows, coo_cols, h_csr_row_ptr, h_csr_col_idx);
//
//     std::vector<float> h_A((size_t) M * K), h_B((size_t) N * K);
//     for (size_t i = 0; i < (size_t) M * K; ++i) h_A[i] = (rand() % 100 + 1) / 100.0f;
//     for (size_t i = 0; i < (size_t) N * K; ++i) h_B[i] = (rand() % 100 + 1) / 100.0f;
//
//     float *d_A, *d_B;
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//     CUDA_CHECK(cudaMalloc(&d_A, (size_t)M * K * sizeof(float)));
//     CUDA_CHECK(cudaMalloc(&d_B, (size_t)N * K * sizeof(float)));
//     CUDA_CHECK(cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&sparse.col_idx, (size_t)nnz * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&sparse.values, (size_t)nnz * sizeof(float)));
//     CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), (size_t)M * K * sizeof(float), cudaMemcpyHostToDevice));
//     CUDA_CHECK(cudaMemcpy(d_B, h_B.data(), (size_t)N * K * sizeof(float), cudaMemcpyHostToDevice));
//     CUDA_CHECK(cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
//     CUDA_CHECK(cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), (size_t)nnz * sizeof(int), cudaMemcpyHostToDevice));
//
//     printf("=== 矩阵信息 ===\n");
//     printf("矩阵文件: %s\n", filename.c_str());
//     printf("矩阵维度: M=%d, N=%d, K=%d\n", M, N, K);
//     printf("非零元素: %d (稀疏度: %.4f%%)\n\n", nnz, 100.0 * nnz / ((double) M * N));
//
//     std::cout << "预热GPU..." << std::endl;
//     warmup_kernel<<<1, 1>>>();
//     CUDA_CHECK(cudaDeviceSynchronize());
//
//     std::cout << "开始执行SDDMM分区计时版本..." << std::endl;
//     const int num_runs = 5;
//     std::vector<float> total_times;
//     cudaEvent_t start_total, stop_total;
//     CUDA_CHECK(cudaEventCreate(&start_total));
//     CUDA_CHECK(cudaEventCreate(&stop_total));
//
//     for (int run = 0; run < num_runs; run++) {
//         CUDA_CHECK(cudaMemset(sparse.values, 0, (size_t)nnz * sizeof(float)));
//         CUDA_CHECK(cudaDeviceSynchronize());
//         float ms_low, ms_med, ms_high;
//         CUDA_CHECK(cudaEventRecord(start_total));
//         sddmm_ultimate_final(d_A, d_B, sparse, K, prop.sharedMemPerBlock, ms_low, ms_med, ms_high);
//         CUDA_CHECK(cudaEventRecord(stop_total));
//         CUDA_CHECK(cudaEventSynchronize(stop_total));
//         float ms_total;
//         CUDA_CHECK(cudaEventElapsedTime(&ms_total, start_total, stop_total));
//         total_times.push_back(ms_total);
//         printf("运行 %d: %.3f ms (低: %.3f ms, 中: %.3f ms, 高: %.3f ms)\n",
//                run + 1, ms_total, ms_low, ms_med, ms_high);
//     }
//     CUDA_CHECK(cudaEventDestroy(start_total));
//     CUDA_CHECK(cudaEventDestroy(stop_total));
//
//     float min_time = *std::min_element(total_times.begin(), total_times.end());
//     float avg_time = std::accumulate(total_times.begin(), total_times.end(), 0.0f) / num_runs;
//     double gflops = (2.0 * nnz * K) / (min_time * 1e6);
//     printf("\n=== 性能统计 ===\n");
//     printf("平均总时间: %.3f ms\n", avg_time);
//     printf("最佳总时间: %.3f ms\n", min_time);
//     printf("峰值性能: %.2f GFLOPS\n", gflops);
//
//     std::cout << "\n验证计算正确性..." << std::endl;
//     std::vector<float> h_values_gpu(nnz);
//     CUDA_CHECK(cudaMemcpy(h_values_gpu.data(), sparse.values, (size_t)nnz * sizeof(float), cudaMemcpyDeviceToHost));
//     std::vector<float> h_values_cpu(nnz, 0.0f);
//     auto cpu_start = std::chrono::high_resolution_clock::now();
//     sddmm_cpu_reference(h_A.data(), h_B.data(), h_csr_row_ptr.data(), h_csr_col_idx.data(), h_values_cpu.data(), M, N,
//                         K);
//     auto cpu_end = std::chrono::high_resolution_clock::now();
//     auto cpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(cpu_end - cpu_start);
//
//     std::cout << "CPU (OMP) 参考实现时间: " << cpu_duration.count() << " ms" << std::endl;
//     if (min_time > 0)
//         std::cout << "GPU加速比 (vs CPU OMP): " << std::fixed << std::setprecision(2) << (float) cpu_duration
//                 .count() / min_time << "x" << std::endl;
//
//     int correct_count = 0;
//     float max_error = 0.0f;
//     double total_abs_error = 0.0, l1_norm_cpu = 0.0;
//
//     for (int i = 0; i < nnz; i++) {
//         float abs_diff = std::abs(h_values_cpu[i] - h_values_gpu[i]);
//         total_abs_error += abs_diff;
//         l1_norm_cpu += std::abs(h_values_cpu[i]);
//
//         if (abs_diff > max_error) {
//             max_error = abs_diff;
//         }
//
//         bool is_correct = false;
//         if (std::abs(h_values_cpu[i]) > 1e-9) {
//             if ((abs_diff / std::abs(h_values_cpu[i])) < 1e-4) {
//                 is_correct = true;
//             }
//         } else {
//             if (abs_diff < 1e-6) {
//                 is_correct = true;
//             }
//         }
//         if (is_correct) correct_count++;
//     }
//
//     std::cout << "\n=== 验证结果 ===" << std::endl;
//     std::cout << std::scientific << "最大绝对误差: " << max_error << std::endl;
//     if (l1_norm_cpu > 0) {
//         std::cout << "相对L1误差: " << (total_abs_error / l1_norm_cpu) << std::endl;
//     }
//     std::cout << std::fixed << std::setprecision(4) << "近似正确率: " << (100.0f * correct_count / nnz) << "%" << std::endl;
//
//     CUDA_CHECK(cudaFree(d_A));
//     CUDA_CHECK(cudaFree(d_B));
//     CUDA_CHECK(cudaFree(sparse.row_ptr));
//     CUDA_CHECK(cudaFree(sparse.col_idx));
//     CUDA_CHECK(cudaFree(sparse.values));
//
//     std::cout << "\n程序正常结束。" << std::endl;
//     return 0;
// }
//
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/kron_g500-logn20.mtx
// // === GPU信息 ===
// // 设备名称: Tesla P100-PCIE-16GB (Compute Capability 6.0)
// // 设备共享内存/块: 49152 bytes
// //
// // === 矩阵信息 ===
// // 矩阵文件: /data/data/qjs/CLionProjects/CUDAProjects/dataset/kron_g500-logn20.mtx
// // 矩阵维度: M=1048576, N=1048576, K=128
// // 非零元素: 44620272 (稀疏度: 0.0041%)
// //
// // 预热GPU...
// // 开始执行SDDMM分区计时版本...
// // 行分类结果: 低密度(<=32)=520533, 中密度(<=256)=109756, 高密度(>256)=32260
// // 高密度区策略: Warp协作模型
// // 运行 1: 48.882 ms (低: 11.906 ms, 中: 47.588 ms, 高: 29.823 ms)
// // 行分类结果: 低密度(<=32)=520533, 中密度(<=256)=109756, 高密度(>256)=32260
// // 高密度区策略: Warp协作模型
// // 运行 2: 50.442 ms (低: 11.899 ms, 中: 21.110 ms, 高: 49.277 ms)
// // 行分类结果: 低密度(<=32)=520533, 中密度(<=256)=109756, 高密度(>256)=32260
// // 高密度区策略: Warp协作模型
// // 运行 3: 51.734 ms (低: 11.811 ms, 中: 20.966 ms, 高: 49.481 ms)
// // 行分类结果: 低密度(<=32)=520533, 中密度(<=256)=109756, 高密度(>256)=32260
// // 高密度区策略: Warp协作模型
// // 运行 4: 51.118 ms (低: 11.824 ms, 中: 20.988 ms, 高: 49.679 ms)
// // 行分类结果: 低密度(<=32)=520533, 中密度(<=256)=109756, 高密度(>256)=32260
// // 高密度区策略: Warp协作模型
// // 运行 5: 50.921 ms (低: 11.830 ms, 中: 20.994 ms, 高: 49.706 ms)
// //
// // === 性能统计 ===
// // 平均总时间: 50.619 ms
// // 最佳总时间: 48.882 ms
// // 峰值性能: 233.68 GFLOPS
// //
// // 验证计算正确性...
// // CPU (OMP) 参考实现时间: 19025 ms
// // GPU加速比 (vs CPU OMP): 389.20x
// //
// // === 验证结果 ===
// // 最大绝对误差: 3.81e-05
// // 相对L1误差: 1.23e-07
// // 近似正确率: 100.0000%
// //
// // 程序正常结束。
// //
// // 进程已结束，退出代码为 0
