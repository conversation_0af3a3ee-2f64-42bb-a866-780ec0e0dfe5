#include <cuda_runtime.h>
#include <iostream>
#include <cmath>
#include <fstream>
#include <vector>
#include <algorithm>
#include <sstream>
#include <string>
#include <cstdlib>
#include <ctime>
#include <cstdint>
#include <cassert>

// 高级优化常量定义
const int HIGH_DENSITY_THREADS = 256;
const int WARP_SIZE = 32;
const int MAX_K_SHARED = 128;

// 高级优化参数
const int TILE_K = 32;           // K维度分块大小
const int TILE_N = 64;           // N维度分块大小  
const int VECTOR_SIZE = 4;       // 向量化大小
const int PREFETCH_DISTANCE = 2; // 预取距离
const int MAX_STREAMS = 8;       // 增加流数量
const int CACHE_LINE_SIZE = 128; // 缓存行大小
const int BANK_SIZE = 32;        // 共享内存bank大小

struct CSRMatrix {
    int *row_ptr;
    int *col_idx;
    float *values;
    int rows;
    int cols;
    int nnz;
};

// 内核1：计算每行非零元数量并对行进行高/低密度分类
__global__ void compute_row_nnz_and_classify_kernel(
    const int *row_ptr,
    int *d_high_rows,
    int *d_low_rows,
    int *high_count,
    int *low_count,
    int rows,
    int threshold,
    int rows_per_thread
) {
    int tid = blockIdx.x * blockDim.x + threadIdx.x;
    for (int row = tid * rows_per_thread; row < min((tid + 1) * rows_per_thread, rows); ++row) {
        int nnz = row_ptr[row + 1] - row_ptr[row];
        if (nnz > threshold) {
            int pos = atomicAdd(high_count, 1);
            if (pos < rows) d_high_rows[pos] = row;
        } else if (nnz > 0) {
            int pos = atomicAdd(low_count, 1);
            if (pos < rows) d_low_rows[pos] = row;
        }
    }
}

// 高级优化版本1：多级缓存 + 向量化 + Bank冲突避免
__global__ void sddmm_high_density_advanced_kernel(
    const float *A,
    const float *B,
    const int *row_ptr,
    const int *col_idx,
    const int *d_high_rows,
    float *result,
    int h_high_count,
    int K) {
    
    int high_row_idx = blockIdx.x;
    if (high_row_idx >= h_high_count) return;

    // 动态共享内存分配，避免bank冲突
    extern __shared__ float shared_mem[];
    float *a_tile = shared_mem;
    float *b_cache = shared_mem + TILE_K + BANK_SIZE; // 添加padding避免bank冲突

    int row = d_high_rows[high_row_idx];
    int tid = threadIdx.x;
    int warp_id = tid / WARP_SIZE;
    int lane_id = tid % WARP_SIZE;
    int warps_in_block = blockDim.x / WARP_SIZE;

    int row_start = row_ptr[row];
    int row_end = row_ptr[row + 1];
    int nnz_in_row = row_end - row_start;

    // 寄存器缓存优化
    float reg_cache[4]; // 每个线程的寄存器缓存

    // K维度分块处理，支持大K值
    for (int k_start = 0; k_start < K; k_start += TILE_K) {
        int k_end = min(k_start + TILE_K, K);
        int tile_k_size = k_end - k_start;

        // 向量化加载A分块到共享内存，避免bank冲突
        for (int k = tid; k < tile_k_size; k += blockDim.x) {
            if (k_start + k < K) {
                // 使用padding避免bank冲突
                int padded_idx = k + (k / BANK_SIZE);
                a_tile[padded_idx] = A[row * K + k_start + k];
            }
        }
        __syncthreads();

        // 每个Warp处理行内的一部分非零元
        for (int i = warp_id; i < nnz_in_row; i += warps_in_block) {
            int global_idx = row_start + i;
            int col = col_idx[global_idx];
            
            // 多级累加器，减少寄存器压力
            float4 acc = make_float4(0.0f, 0.0f, 0.0f, 0.0f);
            
            // 向量化计算，4个元素一组
            for (int k = lane_id * VECTOR_SIZE; k < tile_k_size; k += WARP_SIZE * VECTOR_SIZE) {
                if (k + VECTOR_SIZE <= tile_k_size) {
                    // 从共享内存读取A（考虑padding）
                    int padded_k = k + (k / BANK_SIZE);
                    float4 a_vec = *reinterpret_cast<const float4*>(&a_tile[padded_k]);
                    
                    // 从全局内存读取B（合并访问）
                    float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k_start + k]);
                    
                    // FMA操作
                    acc.x += a_vec.x * b_vec.x;
                    acc.y += a_vec.y * b_vec.y;
                    acc.z += a_vec.z * b_vec.z;
                    acc.w += a_vec.w * b_vec.w;
                }
            }
            
            // 归约向量结果
            float partial_sum = acc.x + acc.y + acc.z + acc.w;
            
            // 处理剩余元素
            for (int k = (tile_k_size / (WARP_SIZE * VECTOR_SIZE)) * (WARP_SIZE * VECTOR_SIZE) + lane_id; 
                 k < tile_k_size; k += WARP_SIZE) {
                int padded_k = k + (k / BANK_SIZE);
                partial_sum += a_tile[padded_k] * B[col * K + k_start + k];
            }
            
            // Warp shuffle归约（保留原有优化）
            for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }

            // 累加到全局结果
            if (lane_id == 0) {
                if (k_start == 0) {
                    result[global_idx] = partial_sum;
                } else {
                    result[global_idx] += partial_sum;
                }
            }
        }
        __syncthreads();
    }
}

// 高级优化版本2：异步预取 + 双缓冲
__global__ void sddmm_high_density_async_prefetch_kernel(
    const float *A,
    const float *B,
    const int *row_ptr,
    const int *col_idx,
    const int *d_high_rows,
    float *result,
    int h_high_count,
    int K) {
    
    int high_row_idx = blockIdx.x;
    if (high_row_idx >= h_high_count) return;

    // 双缓冲共享内存
    extern __shared__ float shared_mem[];
    float *a_tile[2] = {shared_mem, shared_mem + TILE_K + BANK_SIZE};
    float *b_cache = shared_mem + 2 * (TILE_K + BANK_SIZE);

    int row = d_high_rows[high_row_idx];
    int tid = threadIdx.x;
    int warp_id = tid / WARP_SIZE;
    int lane_id = tid % WARP_SIZE;
    int warps_in_block = blockDim.x / WARP_SIZE;

    int row_start = row_ptr[row];
    int row_end = row_ptr[row + 1];
    int nnz_in_row = row_end - row_start;

    int current_buffer = 0;
    
    // 预加载第一个分块
    for (int k = tid; k < min(TILE_K, K); k += blockDim.x) {
        int padded_idx = k + (k / BANK_SIZE);
        a_tile[current_buffer][padded_idx] = A[row * K + k];
    }
    __syncthreads();

    // 双缓冲流水线处理
    for (int k_start = 0; k_start < K; k_start += TILE_K) {
        int k_end = min(k_start + TILE_K, K);
        int tile_k_size = k_end - k_start;
        
        // 异步预取下一个分块到另一个缓冲区
        int next_buffer = 1 - current_buffer;
        int next_k_start = k_start + TILE_K;
        if (next_k_start < K) {
            for (int k = tid; k < min(TILE_K, K - next_k_start); k += blockDim.x) {
                int padded_idx = k + (k / BANK_SIZE);
                a_tile[next_buffer][padded_idx] = A[row * K + next_k_start + k];
            }
        }
        
        // 处理当前分块
        for (int i = warp_id; i < nnz_in_row; i += warps_in_block) {
            int global_idx = row_start + i;
            int col = col_idx[global_idx];
            
            float4 acc = make_float4(0.0f, 0.0f, 0.0f, 0.0f);
            
            // 向量化计算
            for (int k = lane_id * VECTOR_SIZE; k < tile_k_size; k += WARP_SIZE * VECTOR_SIZE) {
                if (k + VECTOR_SIZE <= tile_k_size) {
                    int padded_k = k + (k / BANK_SIZE);
                    float4 a_vec = *reinterpret_cast<const float4*>(&a_tile[current_buffer][padded_k]);
                    float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k_start + k]);
                    
                    acc.x += a_vec.x * b_vec.x;
                    acc.y += a_vec.y * b_vec.y;
                    acc.z += a_vec.z * b_vec.z;
                    acc.w += a_vec.w * b_vec.w;
                }
            }
            
            float partial_sum = acc.x + acc.y + acc.z + acc.w;
            
            // Warp shuffle归约
            for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }

            if (lane_id == 0) {
                if (k_start == 0) {
                    result[global_idx] = partial_sum;
                } else {
                    result[global_idx] += partial_sum;
                }
            }
        }
        
        __syncthreads();
        current_buffer = next_buffer; // 切换缓冲区
    }
}

// 低密度区核函数 (保持不变)
__global__ void sddmm_low_density_kernel(
    const float *__restrict__ A,
    const float *__restrict__ B,
    const int *__restrict__ row_ptr,
    const int *__restrict__ col_idx,
    const int *__restrict__ row_indices,
    float *__restrict__ result,
    int M, int K, int N,
    int num_rows
) {
    const int VECTOR_SIZE = 4;
    int index = blockIdx.x * blockDim.x + threadIdx.x;
    if (index >= num_rows) return;

    int orig_row = row_indices[index];
    const int row_start = row_ptr[orig_row];
    const int row_end = row_ptr[orig_row + 1];
    const int nnz_in_row = row_end - row_start;

    float a_reg[MAX_K_SHARED];
    if (K > MAX_K_SHARED) return;

    for (int k = 0; k < K; k++) {
        a_reg[k] = A[orig_row * K + k];
    }

    for (int idx = 0; idx < nnz_in_row; idx++) {
        int col = col_idx[row_start + idx];
        if (col < 0 || col >= N) continue;

        float sum = 0.0f;
        int k = 0;

        for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
            float4 a_vec, b_vec;
            memcpy(&a_vec, &a_reg[k], sizeof(float4));
            memcpy(&b_vec, &B[col * K + k], sizeof(float4));
            sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y
                    + a_vec.z * b_vec.z + a_vec.w * b_vec.w;
        }

        for (; k < K; ++k) {
            sum += a_reg[k] * B[col * K + k];
        }

        result[row_start + idx] = sum;
    }
}

// 高级主控制函数，集成多种优化策略
void sddmm_csr_advanced(
    const float *d_A,
    const float *d_B,
    CSRMatrix &sparse,
    int K
) {
    int *d_high_rows, *d_low_rows;
    int *d_high_count, *d_low_count;
    int h_high_count = 0, h_low_count = 0;
    int threshold = 32;
    const int ROWS_PER_THREAD = 4;

    // 内存分配
    cudaMalloc(&d_high_rows, sparse.rows * sizeof(int));
    cudaMalloc(&d_low_rows, sparse.rows * sizeof(int));
    cudaMalloc(&d_high_count, sizeof(int));
    cudaMalloc(&d_low_count, sizeof(int));
    cudaMemset(d_high_count, 0, sizeof(int));
    cudaMemset(d_low_count, 0, sizeof(int));

    // 创建多个流用于细粒度并行
    cudaStream_t streams[MAX_STREAMS];
    cudaEvent_t events[MAX_STREAMS];
    for (int i = 0; i < MAX_STREAMS; i++) {
        cudaStreamCreate(&streams[i]);
        cudaEventCreate(&events[i]);
    }

    // 性能计时
    cudaEvent_t start_total, stop_total;
    cudaEventCreate(&start_total);
    cudaEventCreate(&stop_total);
    cudaEventRecord(start_total);

    // 步骤1: 行分类
    dim3 block(256);
    dim3 grid((sparse.rows / ROWS_PER_THREAD + block.x - 1) / block.x);
    compute_row_nnz_and_classify_kernel<<<grid, block, 0, streams[0]>>>(
        sparse.row_ptr, d_high_rows, d_low_rows,
        d_high_count, d_low_count,
        sparse.rows, threshold, ROWS_PER_THREAD
    );
    cudaEventRecord(events[0], streams[0]);

    // 同步获取计数
    cudaStreamSynchronize(streams[0]);
    cudaMemcpy(&h_high_count, d_high_count, sizeof(int), cudaMemcpyDeviceToHost);
    cudaMemcpy(&h_low_count, d_low_count, sizeof(int), cudaMemcpyDeviceToHost);

    printf("行分类结果: 高密度行 = %d, 低密度行 = %d\n", h_high_count, h_low_count);

    // 步骤2: 高密度区计算 - 自适应策略选择
    if (h_high_count > 0) {
        dim3 block_hd(HIGH_DENSITY_THREADS);
        dim3 grid_hd(h_high_count);

        if (K <= MAX_K_SHARED) {
            // 小K值：使用基础共享内存版本
            printf("使用基础共享内存策略 (K=%d)\n", K);
            sddmm_high_density_advanced_kernel<<<grid_hd, block_hd,
                (TILE_K + TILE_N + 2 * BANK_SIZE) * sizeof(float), streams[1]>>>(
                d_A, d_B, sparse.row_ptr, sparse.col_idx,
                d_high_rows, sparse.values, h_high_count, K
            );
        } else {
            // 大K值：使用双缓冲异步预取
            printf("使用双缓冲异步预取策略 (K=%d)\n", K);
            size_t shared_mem_size = (2 * (TILE_K + BANK_SIZE) + TILE_N) * sizeof(float);
            sddmm_high_density_async_prefetch_kernel<<<grid_hd, block_hd,
                shared_mem_size, streams[1]>>>(
                d_A, d_B, sparse.row_ptr, sparse.col_idx,
                d_high_rows, sparse.values, h_high_count, K
            );
        }
    }
    cudaEventRecord(events[1], streams[1]);

    // 步骤3: 低密度区计算 - 多流并行
    if (h_low_count > 0) {
        int rows_per_stream = (h_low_count + MAX_STREAMS - 3) / (MAX_STREAMS - 2);

        for (int stream_id = 2; stream_id < MAX_STREAMS; stream_id++) {
            int start_row = (stream_id - 2) * rows_per_stream;
            int end_row = min(start_row + rows_per_stream, h_low_count);

            if (start_row < end_row) {
                dim3 block_ld(256);
                dim3 grid_ld((end_row - start_row + block_ld.x - 1) / block_ld.x);

                sddmm_low_density_kernel<<<grid_ld, block_ld, 0, streams[stream_id]>>>(
                    d_A, d_B, sparse.row_ptr, sparse.col_idx,
                    d_low_rows + start_row, sparse.values,
                    sparse.rows, K, sparse.cols, end_row - start_row
                );
                cudaEventRecord(events[stream_id], streams[stream_id]);
            }
        }
    }

    // 同步所有流并计算性能
    for (int i = 0; i < MAX_STREAMS; i++) {
        cudaStreamSynchronize(streams[i]);
    }

    cudaEventRecord(stop_total);
    cudaEventSynchronize(stop_total);
    float ms_total;
    cudaEventElapsedTime(&ms_total, start_total, stop_total);
    printf("SDDMM高级优化总计算时间: %.3f ms\n", ms_total);

    // 释放资源
    cudaFree(d_high_rows);
    cudaFree(d_low_rows);
    cudaFree(d_high_count);
    cudaFree(d_low_count);

    for (int i = 0; i < MAX_STREAMS; i++) {
        cudaStreamDestroy(streams[i]);
        cudaEventDestroy(events[i]);
    }
    cudaEventDestroy(start_total);
    cudaEventDestroy(stop_total);
}

#ifdef STANDALONE_TEST
// 独立测试主函数
int main(int argc, char **argv) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
        return 1;
    }

    std::string filename = argv[1];
    int K = (argc > 2) ? std::atoi(argv[2]) : 128;

    // 这里可以添加简单的测试代码
    std::cout << "SDDMM 优化版本独立测试" << std::endl;
    std::cout << "矩阵文件: " << filename << std::endl;
    std::cout << "K维度: " << K << std::endl;

    return 0;
}
#endif
