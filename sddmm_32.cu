// #include <cuda_runtime.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
// #include <cstdint>
// #include <cassert>
//
// // 常量定义
// const int TILE_SIZE = 32;
// const int VECTOR_SIZE = 4;
// const int HIGH_DENSITY_THREADS = 256;
// const int BLOCK_SIZE = 32; // 调整为32以匹配BITMAP_WORD_SIZE
// const int BITMAP_WORD_SIZE = 32; // Bitmap每字位数
// const int WARP_SIZE = 32; // Warp大小
//
// // 稀疏矩阵结构体 (CSR格式 + Bitmap)
// struct CSRMatrix {
//     int *row_ptr;      // 行指针
//     int *col_idx;      // 列索引
//     float *values;     // 非零值
//     uint32_t *bitmap;  // 块位图
//     int *bitmap_offset; // 块位图偏移
//     int rows;          // 矩阵行数
//     int cols;          // 矩阵列数
//     int nnz;           // 非零元数量
// };
//
// // 合并的行统计和分类内核
// __global__ void compute_row_nnz_and_classify_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int *d_high_rows,
//     int *d_low_rows,
//     int *high_count,
//     int *low_count,
//     int rows,
//     int threshold
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         int nnz = row_ptr[row + 1] - row_ptr[row];
//         row_nnz[row] = nnz;
//
//         if (nnz > threshold) {
//             int pos = atomicAdd(high_count, 1);
//             if (pos < rows) d_high_rows[pos] = row;
//         } else {
//             int pos = atomicAdd(low_count, 1);
//             if (pos < rows) d_low_rows[pos] = row;
//         }
//     }
// }
//
// __global__ void sddmm_high_density_bitmap_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const uint32_t *__restrict__ bitmap,
//     const int *__restrict__ bitmap_offset,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     // 动态共享内存：用于存储 A 的分块
//     extern __shared__ float shared_A[];
//     const int warp_id = threadIdx.x / WARP_SIZE;           // Warp 索引
//     const int lane_id = threadIdx.x % WARP_SIZE;           // Warp 内线程索引
//     const int row_in_group = blockIdx.x;                   // 处理的行
//     const int SHARED_BLOCK_SIZE = 128;                     // 共享内存分块大小（可调）
//
//     if (row_in_group >= num_rows) return;
//
//     int orig_row = row_indices[row_in_group];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 获取位图信息
//     int bitmap_start = bitmap_offset[orig_row];
//     int bitmap_end = bitmap_offset[orig_row + 1];
//     int bitmap_words = bitmap_end - bitmap_start;
//
//     // 分块处理 K 维度
//     for (int k_base = 0; k_base < K; k_base += SHARED_BLOCK_SIZE) {
//         int k_limit = min(SHARED_BLOCK_SIZE, K - k_base);
//
//         // 加载 A 的当前块到共享内存
//         for (int k = threadIdx.x; k < k_limit; k += blockDim.x) {
//             shared_A[k] = A[orig_row * K + k_base + k];
//         }
//         __syncthreads(); // 仅在加载完成后同步
//
//         // 处理所有位图字
//         for (int word_idx = warp_id; word_idx < bitmap_words; word_idx += blockDim.x / WARP_SIZE) {
//             uint32_t bitmap_word = bitmap[bitmap_start + word_idx];
//             int block_start = row_start + word_idx * BLOCK_SIZE;
//             int valid_nnz = min(BLOCK_SIZE, nnz_in_row - word_idx * BLOCK_SIZE);
//
//             if (lane_id < valid_nnz) {
//                 int flag = (bitmap_word >> lane_id) & 0x1;
//                 if (flag) {
//                     int global_idx = block_start + lane_id;
//                     int col = col_idx[global_idx];
//                     float sum = 0.0f;
//
//                     // 计算当前块的内积
//                     int k = 0;
//                     for (; k <= k_limit - VECTOR_SIZE; k += VECTOR_SIZE) {
//                         float4 a_vec = *reinterpret_cast<const float4*>(&shared_A[k]);
//                         float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k_base + k]);
//                         sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y +
//                                a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//                     }
//                     // 处理剩余元素
//                     for (; k < k_limit; k++) {
//                         sum += shared_A[k] * B[col * K + k_base + k];
//                     }
//
//                     // 原子更新结果（避免后续块覆盖）
//                     atomicAdd(&result[global_idx], sum);
//                 }
//             }
//         }
//         __syncthreads(); // 下一块加载前同步
//     }
// }
//
// // 优化后的低密度区SDDMM内核
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int index = blockIdx.x * blockDim.x + threadIdx.x;
//     if (index >= num_rows) return;
//
//     int orig_row = row_indices[index];
//     const int row_start = row_ptr[orig_row];
//     const int row_end = row_ptr[orig_row + 1];
//     const int nnz_in_row = row_end - row_start;
//
//     float a_reg[128];
//     for (int k = 0; k < K; k++) {
//         a_reg[k] = A[orig_row * K + k];
//     }
//
//     for (int idx = 0; idx < nnz_in_row; idx++) {
//         int col = col_idx[row_start + idx];
//         if (col < 0 || col >= N) continue;
//
//         float sum = 0.0f;
//         int k = 0;
//
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec, b_vec;
//             memcpy(&a_vec, &a_reg[k], sizeof(float4));
//             memcpy(&b_vec, &B[col * K + k], sizeof(float4));
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y
//                     + a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//
//         for (; k < K; ++k) {
//             sum += a_reg[k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 生成块Bitmap格式
// void generate_block_bitmap_format(
//     const std::vector<int> &h_row_ptr,
//     const std::vector<int> &h_col_idx,
//     std::vector<uint32_t> &h_bitmap,
//     std::vector<int> &h_bitmap_offset,
//     int M
// ) {
//     h_bitmap_offset.resize(M + 1, 0);
//     for (int row = 0; row < M; ++row) {
//         int nnz = h_row_ptr[row + 1] - h_row_ptr[row];
//         int blocks = (nnz + BLOCK_SIZE - 1) / BLOCK_SIZE;
//         h_bitmap_offset[row + 1] = h_bitmap_offset[row] + blocks;
//     }
//
//     int total_blocks = h_bitmap_offset[M];
//     h_bitmap.resize(total_blocks, 0);
//
//     for (int row = 0; row < M; ++row) {
//         int start = h_row_ptr[row];
//         int end = h_row_ptr[row + 1];
//         int block_start = h_bitmap_offset[row];
//         int blocks = h_bitmap_offset[row + 1] - h_bitmap_offset[row];
//
//         for (int block_idx = 0; block_idx < blocks; ++block_idx) {
//             uint32_t bitmap_word = 0;
//             int block_nnz_start = start + block_idx * BLOCK_SIZE;
//             int block_nnz_end = min(start + (block_idx + 1) * BLOCK_SIZE, end);
//             int valid_nnz = block_nnz_end - block_nnz_start;
//
//             for (int i = 0; i < valid_nnz; ++i) {
//                 bitmap_word |= (1u << i);
//             }
//             h_bitmap[block_start + block_idx] = bitmap_word;
//         }
//     }
// }
//
// // 主SDDMM函数
// void sddmm_csr(
//     const float *d_A,
//     const float *d_B,
//     CSRMatrix &sparse,
//     int K
// ) {
//     // 直接在GPU上统计和分类
//     int *d_row_nnz, *d_high_rows, *d_low_rows;
//     int *d_high_count, *d_low_count;
//     int h_high_count = 0, h_low_count = 0;
//     int threshold = 32;
//
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_low_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_count, sizeof(int));
//     cudaMalloc(&d_low_count, sizeof(int));
//     cudaMemset(d_high_count, 0, sizeof(int));
//     cudaMemset(d_low_count, 0, sizeof(int));
//
//     cudaEvent_t step_start, step_stop;
//     cudaEventCreate(&step_start);
//     cudaEventCreate(&step_stop);
//     cudaEventRecord(step_start);
//
//     dim3 block(256);
//     dim3 grid((sparse.rows + block.x - 1) / block.x);
//     compute_row_nnz_and_classify_kernel<<<grid, block>>>(
//         sparse.row_ptr, d_row_nnz, d_high_rows, d_low_rows,
//         d_high_count, d_low_count, sparse.rows, threshold
//     );
//
//     cudaEventRecord(step_stop);
//     cudaEventSynchronize(step_stop);
//     float step_ms;
//     cudaEventElapsedTime(&step_ms, step_start, step_stop);
//     printf("[步骤1] 行分类计算耗时: %.3f ms\n", step_ms);
//
//     cudaMemcpy(&h_high_count, d_high_count, sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(&h_low_count, d_low_count, sizeof(int), cudaMemcpyDeviceToHost);
//
//     cudaEvent_t step4_start, step4_stop;
//     cudaEventCreate(&step4_start);
//     cudaEventCreate(&step4_stop);
//     cudaEventRecord(step4_start);
//
//     // 执行高密度区内核 (Bitmap优化)
//     if (h_high_count > 0) {
//         const int BLOCK_SIZE_HD = 128; // 动态调整线程块大小
//         size_t shared_mem_size = 128 * sizeof(float); // SHARED_BLOCK_SIZE = 128
//         dim3 block_hd(BLOCK_SIZE_HD);
//         dim3 grid_hd(h_high_count);
//         sddmm_high_density_bitmap_kernel<<<grid_hd, block_hd, shared_mem_size>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx,
//             sparse.bitmap, sparse.bitmap_offset, d_high_rows,
//             sparse.values, sparse.rows, K, sparse.cols, h_high_count
//         );
//     }
//
//
//     cudaEventRecord(step4_stop);
//     cudaEventSynchronize(step4_stop);
//     float step4_ms;
//     cudaEventElapsedTime(&step4_ms, step4_start, step4_stop);
//     printf("[步骤2] 高密度区计算耗时: %.3f ms\n", step4_ms);
//
//     cudaEvent_t step5_start, step5_stop;
//     cudaEventCreate(&step5_start);
//     cudaEventCreate(&step5_stop);
//     cudaEventRecord(step5_start);
//
//     // 执行低密度区内核
//     if (h_low_count > 0) {
//         dim3 block_ld(256);
//         dim3 grid_ld((h_low_count + block_ld.x - 1) / block_ld.x);
//         sddmm_low_density_kernel<<<grid_ld, block_ld>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx,
//             d_low_rows, sparse.values,
//             sparse.rows, K, sparse.cols, h_low_count
//         );
//     }
//
//     cudaEventRecord(step5_stop);
//     cudaEventSynchronize(step5_stop);
//     float step5_ms;
//     cudaEventElapsedTime(&step5_ms, step5_start, step5_stop);
//     printf("[步骤3] 低密度区计算耗时: %.3f ms\n", step5_ms);
//
//     // 释放资源
//     cudaFree(d_row_nnz);
//     cudaFree(d_high_rows);
//     cudaFree(d_low_rows);
//     cudaFree(d_high_count);
//     cudaFree(d_low_count);
// }
//
// // CPU参考实现
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// // 从Matrix Market文件加载COO格式稀疏矩阵
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     // 跳过注释行
//     while (std::getline(file, line) && line[0] == '%');
//
//     // 读取矩阵元数据
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     // 调整向量大小
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     // 读取非零元素
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value; // 忽略值，只关心位置
//         file >> row >> col;
//         if (file.peek() != '\n') {
//             file >> value; // 如果有值则读取
//         }
//         // 转换为0-based索引
//         rows[i] = row - 1;
//         cols[i] = col - 1;
//     }
//     file.close();
// }
//
// // 将COO格式转换为CSR格式
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx,
//                 int &max_nnz_per_row) {
//     // 初始化行指针
//     csr_row_ptr.resize(M + 1, 0);
//     max_nnz_per_row = 0;
//
//     // 计算每行非零元素数量
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[rows[i]]++;
//     }
//
//     // 前缀和计算行指针
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         if (temp > max_nnz_per_row) max_nnz_per_row = temp;
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     // 创建临时行计数器和列索引数组
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     // 填充列索引
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int col = cols[i];
//         int index = csr_row_ptr[row] + row_counter[row];
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// __global__ void start(){}
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "Usage: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr));
//     std::string filename = argv[1];
//     int K = (argc > 2) ? std::atoi(argv[2]) : 128;
//
//     // 加载矩阵
//     int M, N, nnz, max_nnz_per_row = 0;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//
//     // 转换为CSR格式
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx, max_nnz_per_row);
//
//     // 生成块Bitmap格式
//     std::vector<uint32_t> h_bitmap;
//     std::vector<int> h_bitmap_offset;
//     generate_block_bitmap_format(h_csr_row_ptr, h_csr_col_idx, h_bitmap, h_bitmap_offset, M);
//
//     // 初始化稠密矩阵
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     for (int i = 0; i < M * K; ++i) h_A[i] = (rand() % 100) / 100.0f;
//     for (int i = 0; i < N * K; ++i) h_B[i] = (rand() % 100) / 100.0f;
//
//     // 分配设备内存
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//
//     // 设置稀疏矩阵结构
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//     cudaMalloc(&sparse.bitmap, h_bitmap.size() * sizeof(uint32_t));
//     cudaMalloc(&sparse.bitmap_offset, (M + 1) * sizeof(int));
//
//     cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.bitmap, h_bitmap.data(), h_bitmap.size() * sizeof(uint32_t), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.bitmap_offset, h_bitmap_offset.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 预热
//     start<<<1, 1>>>();
//
//     // 执行SDDMM
//     std::cout << "开始执行SDDMM..." << std::endl;
//
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//     cudaEventRecord(start);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     float ms;
//     cudaEventElapsedTime(&ms, start, stop);
//     printf("SDDMM计算时间: %.3f ms\n", ms);
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     // 验证结果
//     std::vector<float> h_values_gpu(nnz);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     // CPU参考计算
//     std::vector<float> h_values_cpu(nnz);
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//
//     // 结果对比
//     int correct = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_cpu[i] - h_values_gpu[i]);
//         if (diff < 1e-4) correct++;
//         if (diff > max_error) max_error = diff;
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct / nnz) << "%" << std::endl;
//
//     // 释放资源
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//     cudaFree(sparse.bitmap);
//     cudaFree(sparse.bitmap_offset);
//
//     return 0;
// }
//
// // 行分块+bitmap
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 开始执行SDDMM...
// // [步骤1] 行分类计算耗时: 0.046 ms
// // [步骤2] 高密度区计算耗时: 98.031 ms
// // [步骤3] 低密度区计算耗时: 2.059 ms
// // SDDMM计算时间: 100.430 ms
// // SDDMM执行完成
// // 验证结果:
// // 最大绝对误差: 3.43323e-05
// // 正确率: 100%
// //
// // 进程已结束，退出代码为 0
//
//
