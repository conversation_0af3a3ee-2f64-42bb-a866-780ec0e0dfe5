// #include <cuda_runtime.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
//
// // 常量定义
// const int VECTOR_SIZE = 4;        // 向量化加载大小(float4)
// const int NUM_STREAMS = 3;        // 使用的CUDA流数量
// const int HIGH_DENSITY_THRESHOLD = 32; // 高密度行阈值
// const int XLONG_THRESHOLD = 256;   // 超长任务阈值
// const int LONG_THRESHOLD = 64;     // 长任务阈值
//
// // 任务类型枚举
// enum TaskType {
//     TASK_XLONG,   // 超长任务 (NNZ > XLONG_THRESHOLD)
//     TASK_LONG,    // 长任务 (LONG_THRESHOLD < NNZ <= XLONG_THRESHOLD)
//     TASK_SHORT,   // 短任务 (NNZ <= LONG_THRESHOLD)
//     TASK_LOW_DENSITY // 低密度任务
// };
//
// // 稀疏矩阵结构体 (CSR格式)
// struct CSRMatrix {
//     int *row_ptr;    // 行指针
//     int *col_idx;    // 列索引
//     float *values;   // 非零值 (SDDMM结果存放位置)
//     int rows;        // 矩阵行数
//     int cols;        // 矩阵列数
//     int nnz;         // 非零元数量
// };
//
// // GPU端行非零元统计内核
// __global__ void compute_row_nnz_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int rows
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         row_nnz[row] = row_ptr[row + 1] - row_ptr[row];
//     }
// }
//
// // 流压缩分类内核 (GPU加速预处理)
// __global__ void classify_rows_kernel(
//     const int *row_nnz,
//     int *high_density_rows,
//     int *low_density_rows,
//     int *high_count,
//     int *low_count,
//     int rows,
//     int threshold
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         int nnz = row_nnz[row];
//         if (nnz > threshold) {
//             int idx = atomicAdd(high_count, 1);
//             high_density_rows[idx] = row;
//         } else {
//             int idx = atomicAdd(low_count, 1);
//             low_density_rows[idx] = row;
//         }
//     }
// }
//
// // 三级负载均衡内核
// __global__ void balance_workload_kernel(
//     const int *row_nnz,
//     const int *high_density_rows,
//     int high_count,
//     int *xlong_rows,
//     int *long_rows,
//     int *short_rows,
//     int *task_counts,  // 大小为3的数组: [0]:超长, [1]:长, [2]:短
//     int xlong_thresh,
//     int long_thresh
// ) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (idx < high_count) {
//         int row = high_density_rows[idx];
//         int nnz = row_nnz[row];
//
//         if (nnz > xlong_thresh) {
//             int pos = atomicAdd(&task_counts[0], 1);
//             xlong_rows[pos] = row;
//         } else if (nnz > long_thresh) {
//             int pos = atomicAdd(&task_counts[1], 1);
//             long_rows[pos] = row;
//         } else {
//             int pos = atomicAdd(&task_counts[2], 1);
//             short_rows[pos] = row;
//         }
//     }
// }
//
// // 超长任务内核 (NNZ > XLONG_THRESHOLD)256
// __global__ void sddmm_xlong_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int row_in_group = blockIdx.x;
//     if (row_in_group >= num_rows) return;
//
//     int orig_row = row_indices[row_in_group];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     int tid = threadIdx.x;
//     int num_threads = blockDim.x;
//
//     // 处理该行的所有非零元素 (每个线程处理多个元素)
//     for (int idx = tid; idx < nnz_in_row; idx += num_threads) {
//         int col = col_idx[row_start + idx];
//         float sum = 0.0f;
//         int k = 0;
//
//         // 向量化处理
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4*>(&A[orig_row * K + k]);
//             float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y +
//                    a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//
//         // 标量处理剩余部分
//         for (; k < K; ++k) {
//             sum += A[orig_row * K + k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 长任务内核 (LONG_THRESHOLD < NNZ <= XLONG_THRESHOLD)
// __global__ void sddmm_long_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int row_in_group = blockIdx.x;
//     if (row_in_group >= num_rows) return;
//
//     int orig_row = row_indices[row_in_group];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     int tid = threadIdx.x;
//     int num_threads = blockDim.x;
//
//     // 处理该行的所有非零元素
//     for (int idx = tid; idx < nnz_in_row; idx += num_threads) {
//         int col = col_idx[row_start + idx];
//         float sum = 0.0f;
//         int k = 0;
//
//         // 向量化处理
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4*>(&A[orig_row * K + k]);
//             float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y +
//                    a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//
//         // 标量处理剩余部分
//         for (; k < K; ++k) {
//             sum += A[orig_row * K + k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 短任务内核 (NNZ <= LONG_THRESHOLD)
// __global__ void sddmm_short_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (idx >= num_rows) return;
//
//     int orig_row = row_indices[idx];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 直接访问全局内存，避免寄存器缓存溢出
//     for (int j = 0; j < nnz_in_row; j++) {
//         int col = col_idx[row_start + j];
//         float sum = 0.0f;
//         int k = 0;
//
//         // 向量化处理
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4*>(&A[orig_row * K + k]);
//             float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y +
//                    a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//
//         // 标量处理剩余部分
//         for (; k < K; ++k) {
//             sum += A[orig_row * K + k] * B[col * K + k];
//         }
//
//         result[row_start + j] = sum;
//     }
// }
//
// // 低密度区SDDMM内核 (每行非零元 <= HIGH_DENSITY_THRESHOLD)
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices, // 低密度行索引
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int index = blockIdx.x * blockDim.x + threadIdx.x;
//     if (index >= num_rows) return;
//
//     int orig_row = row_indices[index];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 寄存器缓存A矩阵行
//     float a_reg[32];
//     for (int k = 0; k < K; k++) {
//         a_reg[k] = A[orig_row * K + k];
//     }
//
//     for (int idx = 0; idx < nnz_in_row; idx++) {
//         int col = col_idx[row_start + idx];
//         float sum = 0.0f;
//         int k = 0;
//
//         // 向量化处理
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4 *>(&a_reg[k]);
//             float4 b_vec = *reinterpret_cast<const float4 *>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x;
//             sum += a_vec.y * b_vec.y;
//             sum += a_vec.z * b_vec.z;
//             sum += a_vec.w * b_vec.w;
//         }
//
//         // 标量处理剩余部分
//         for (; k < K; ++k) {
//             sum += a_reg[k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 主SDDMM函数 (集成优化技术)
// void sddmm_csr(
//     const float *d_A,    // 设备端稠密矩阵A (MxK)
//     const float *d_B,    // 设备端稠密矩阵B (KxN)
//     CSRMatrix &sparse,   // 稀疏矩阵结构 (CSR格式)
//     int K                // 公共维度
// ) {
//     // 创建CUDA流
//     cudaStream_t streams[NUM_STREAMS];
//     for (int i = 0; i < NUM_STREAMS; i++) {
//         cudaStreamCreate(&streams[i]);
//     }
//
//     // 步骤1: 行非零元素统计 (GPU)
//     cudaEvent_t step1_start, step1_stop;
//     cudaEventCreate(&step1_start);
//     cudaEventCreate(&step1_stop);
//     cudaEventRecord(step1_start, streams[0]);
//
//     int *d_row_nnz;
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//
//     dim3 block(256);
//     dim3 grid((sparse.rows + block.x - 1) / block.x);
//     compute_row_nnz_kernel<<<grid, block, 0, streams[0]>>>(sparse.row_ptr, d_row_nnz, sparse.rows);
//
//     cudaEventRecord(step1_stop, streams[0]);
//     float step1_ms;
//     cudaEventElapsedTime(&step1_ms, step1_start, step1_stop);
//     cudaStreamSynchronize(streams[0]);
//     printf("[步骤1] 行非零元统计耗时: %.3f ms\n", step1_ms);
//
//     // 步骤2: 动态分区 (GPU加速)
//     cudaEvent_t step2_start, step2_stop;
//     cudaEventCreate(&step2_start);
//     cudaEventCreate(&step2_stop);
//     cudaEventRecord(step2_start);
//
//     // 分配设备内存
//     int *d_high_density_rows, *d_low_density_rows;
//     int *d_high_count, *d_low_count;
//     cudaMalloc(&d_high_density_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_low_density_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_count, sizeof(int));
//     cudaMalloc(&d_low_count, sizeof(int));
//     cudaMemsetAsync(d_high_count, 0, sizeof(int), streams[0]);
//     cudaMemsetAsync(d_low_count, 0, sizeof(int), streams[0]);
//
//     // 执行分类内核
//     dim3 classify_block(256);
//     dim3 classify_grid((sparse.rows + classify_block.x - 1) / classify_block.x);
//     classify_rows_kernel<<<classify_grid, classify_block, 0, streams[0]>>>(
//         d_row_nnz, d_high_density_rows, d_low_density_rows,
//         d_high_count, d_low_count, sparse.rows, HIGH_DENSITY_THRESHOLD
//     );
//
//     // 获取计数
//     int h_high_count = 0, h_low_count = 0;
//     cudaMemcpyAsync(&h_high_count, d_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]);
//     cudaMemcpyAsync(&h_low_count, d_low_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]);
//     cudaStreamSynchronize(streams[0]);
//
//     cudaEventRecord(step2_stop);
//     cudaEventSynchronize(step2_stop);
//     float step2_ms;
//     cudaEventElapsedTime(&step2_ms, step2_start, step2_stop);
//     printf("[步骤2] GPU动态分区耗时: %.3f ms (高密度行: %d, 低密度行: %d)\n",
//            step2_ms, h_high_count, h_low_count);
//
//     // 步骤3: 三级负载均衡 (仅高密度区)
//     cudaEvent_t step3_start, step3_stop;
//     cudaEventCreate(&step3_start);
//     cudaEventCreate(&step3_stop);
//     cudaEventRecord(step3_start, streams[1]);
//
//     // 分配任务数组
//     int *d_xlong_rows, *d_long_rows, *d_short_rows;
//     cudaMalloc(&d_xlong_rows, h_high_count * sizeof(int));
//     cudaMalloc(&d_long_rows, h_high_count * sizeof(int));
//     cudaMalloc(&d_short_rows, h_high_count * sizeof(int));
//
//     int *d_task_counts;
//     cudaMalloc(&d_task_counts, 3 * sizeof(int));
//     cudaMemsetAsync(d_task_counts, 0, 3 * sizeof(int), streams[1]);
//
//     if (h_high_count > 0) {
//         dim3 balance_block(256);
//         dim3 balance_grid((h_high_count + balance_block.x - 1) / balance_block.x);
//         balance_workload_kernel<<<balance_grid, balance_block, 0, streams[1]>>>(
//             d_row_nnz, d_high_density_rows, h_high_count,
//             d_xlong_rows, d_long_rows, d_short_rows,
//             d_task_counts, XLONG_THRESHOLD, LONG_THRESHOLD
//         );
//     }
//
//     int h_task_counts[3] = {0};
//     cudaMemcpyAsync(h_task_counts, d_task_counts, 3 * sizeof(int), cudaMemcpyDeviceToHost, streams[1]);
//     cudaStreamSynchronize(streams[1]);
//
//     cudaEventRecord(step3_stop);
//     cudaEventSynchronize(step3_stop);
//     float step3_ms;
//     cudaEventElapsedTime(&step3_ms, step3_start, step3_stop);
//     printf("[步骤3] 负载均衡耗时: %.3f ms (超长: %d, 长: %d, 短: %d)\n",
//            step3_ms, h_task_counts[0], h_task_counts[1], h_task_counts[2]);
//
//     // 步骤4: 执行计算
//     cudaEvent_t step4_start, step4_stop;
//     cudaEventCreate(&step4_start);
//     cudaEventCreate(&step4_stop);
//     cudaEventRecord(step4_start);
//
//     // 高密度区任务处理
//     if (h_high_count > 0) {
//         // 超长任务处理
//         if (h_task_counts[0] > 0) {
//             dim3 block_xl(256);
//             sddmm_xlong_kernel<<<h_task_counts[0], block_xl, 0, streams[1]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_xlong_rows,
//                 sparse.values, sparse.rows, K, sparse.cols, h_task_counts[0]
//             );
//         }
//
//         // 长任务处理
//         if (h_task_counts[1] > 0) {
//             dim3 block_long(128);
//             sddmm_long_kernel<<<h_task_counts[1], block_long, 0, streams[1]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_long_rows,
//                 sparse.values, sparse.rows, K, sparse.cols, h_task_counts[1]
//             );
//         }
//
//         // 短任务处理
//         if (h_task_counts[2] > 0) {
//             dim3 block_short(64);
//             dim3 grid_short((h_task_counts[2] + block_short.x - 1) / block_short.x);
//             sddmm_short_kernel<<<grid_short, block_short, 0, streams[1]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_short_rows,
//                 sparse.values, sparse.rows, K, sparse.cols, h_task_counts[2]
//             );
//         }
//     }
//
//     cudaEventRecord(step4_stop);
//     cudaEventSynchronize(step4_stop);
//     float step4_ms;
//     cudaEventElapsedTime(&step4_ms, step4_start, step4_stop);
//     printf("[步骤4] 高密度区计算耗时: %.3f ms\n", step4_ms);
//
//     // 步骤5: 低密度区处理 (仅高密度区)
//     cudaEvent_t step5_start, step5_stop;
//     cudaEventCreate(&step5_start);
//     cudaEventCreate(&step5_stop);
//     cudaEventRecord(step5_start);
//
//     // 低密度区处理
//     if (h_low_count > 0) {
//         dim3 block_ld(256);
//         dim3 grid_ld((h_low_count + block_ld.x - 1) / block_ld.x);
//         sddmm_low_density_kernel<<<grid_ld, block_ld, 0, streams[2]>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx, d_low_density_rows,
//             sparse.values, sparse.rows, K, sparse.cols, h_low_count
//         );
//     }
//
//     // 等待所有计算完成
//     for (int i = 0; i < NUM_STREAMS; i++) {
//         cudaStreamSynchronize(streams[i]);
//     }
//
//     cudaEventRecord(step5_stop);
//     cudaEventSynchronize(step5_stop);
//     float step5_ms;
//     cudaEventElapsedTime(&step5_ms, step5_start, step5_stop);
//     printf("[步骤5] 低密度区计算耗时: %.3f ms\n", step5_ms);
//
//
//     // 资源释放
//     cudaFree(d_row_nnz);
//     cudaFree(d_high_density_rows);
//     cudaFree(d_low_density_rows);
//     cudaFree(d_high_count);
//     cudaFree(d_low_count);
//     cudaFree(d_task_counts);
//     cudaFree(d_xlong_rows);
//     cudaFree(d_long_rows);
//     cudaFree(d_short_rows);
//
//     for (int i = 0; i < NUM_STREAMS; i++) {
//         cudaStreamDestroy(streams[i]);
//     }
//
//     cudaEventDestroy(step1_start);
//     cudaEventDestroy(step1_stop);
//     cudaEventDestroy(step2_start);
//     cudaEventDestroy(step2_stop);
//     cudaEventDestroy(step3_start);
//     cudaEventDestroy(step3_stop);
//     cudaEventDestroy(step4_start);
//     cudaEventDestroy(step4_stop);
// }
//
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// // 从Matrix Market文件加载COO格式稀疏矩阵
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     // 跳过注释行
//     while (std::getline(file, line) && line[0] == '%');
//
//     // 读取矩阵元数据
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     // 调整向量大小
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     // 读取非零元素
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value; // 忽略值，只关心位置
//         file >> row >> col;
//         if (file.peek() != '\n') {
//             file >> value; // 如果有值则读取
//         }
//         // 转换为0-based索引
//         rows[i] = row - 1;
//         cols[i] = col - 1;
//     }
//     file.close();
// }
//
// // 将COO格式转换为CSR格式
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
//     // 初始化行指针
//     csr_row_ptr.resize(M + 1, 0);
//
//     // 计算每行非零元素数量
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[rows[i]]++;
//     }
//
//     // 前缀和计算行指针
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     // 创建临时行计数器和列索引数组
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     // 填充列索引
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int col = cols[i];
//         int index = csr_row_ptr[row] + row_counter[row];
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// int main(int argc, char **argv) {
//     // cudaEvent_t total_start, total_stop;
//     // cudaEventCreate(&total_start);
//     // cudaEventCreate(&total_stop);
//     // cudaEventRecord(total_start, 0);  // 总开始计时
//
//     if (argc < 2) {
//         std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr)); // 初始化随机种子
//
//     std::string filename = argv[1];
//     int K = 128; // 默认公共维度
//     if (argc > 2) K = std::atoi(argv[2]);
//
//     // 1. 从文件加载稀疏矩阵
//     int M, N, nnz;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//     std::cout << "加载矩阵: " << filename << std::endl;
//     std::cout << "行数: " << M << ", 列数: " << N << ", 非零元数: " << nnz << std::endl;
//
//     // 2. 转换为CSR格式
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx);
//     std::cout << "CSR转换完成" << std::endl;
//
//     // 3. 分配主机内存并初始化稠密矩阵
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     std::generate(h_A.begin(), h_A.end(), []() { return (rand() % 100) / 100.0f; });
//     std::generate(h_B.begin(), h_B.end(), []() { return (rand() % 100) / 100.0f; });
//
//     // 4. 分配设备内存
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//
//     // 创建事件用于测量通信时间
//     cudaEvent_t comm_start, comm_stop;
//     cudaEventCreate(&comm_start);
//     cudaEventCreate(&comm_stop);
//
//     // 测量主机到设备的数据传输时间
//     cudaEventRecord(comm_start);
//
//     // 创建流用于数据传输
//     cudaStream_t data_stream;
//     cudaStreamCreate(&data_stream);
//     cudaMemcpyAsync(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice, data_stream);
//     cudaMemcpyAsync(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice, data_stream);
//     cudaStreamSynchronize(data_stream);
//
//     cudaEventRecord(comm_stop);
//     cudaEventSynchronize(comm_stop);
//     float h2d_time;
//     cudaEventElapsedTime(&h2d_time, comm_start, comm_stop);
//     printf("主机到设备数据传输时间: %.3f ms\n", h2d_time);
//
//     // 5. 设置稀疏矩阵结构
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//
//     // 分配设备内存并复制CSR结构
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//
//     // 测量CSR结构的传输时间
//     cudaEventRecord(comm_start);
//     cudaMemcpyAsync(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice, data_stream);
//     cudaMemcpyAsync(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice, data_stream);
//     cudaStreamSynchronize(data_stream);
//     cudaEventRecord(comm_stop);
//     cudaEventSynchronize(comm_stop);
//
//     float csr_h2d_time;
//     cudaEventElapsedTime(&csr_h2d_time, comm_start, comm_stop);
//     printf("CSR结构主机到设备传输时间: %.3f ms\n", csr_h2d_time);
//
//     float total_h2d_time = h2d_time + csr_h2d_time;
//     printf("总主机到设备传输时间: %.3f ms\n", total_h2d_time);
//
//     cudaStreamDestroy(data_stream);
//
//     // 初始化结果值为0
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 6. 创建计时事件
//     cudaEvent_t start, stop, d2h_start, d2h_stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//     cudaEventCreate(&d2h_start);
//     cudaEventCreate(&d2h_stop);
//
//     // 7. 执行SDDMM
//     std::cout << "开始执行SDDMM..." << std::endl;
//     cudaEventRecord(start);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     // 8. 计算性能
//     float milliseconds = 0;
//     cudaEventElapsedTime(&milliseconds, start, stop);
//     double gflops = (2.0 * nnz * K) / (milliseconds * 1e6);
//     printf("SDDMM计算时间: %.3f ms\n", milliseconds);
//     printf("GFLOPS: %.2f\n", gflops);
//
//     // 测量设备到主机的数据传输时间
//     std::vector<float> h_values_gpu(nnz);
//     cudaEventRecord(d2h_start);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//     cudaEventRecord(d2h_stop);
//     cudaEventSynchronize(d2h_stop);
//
//     float d2h_time;
//     cudaEventElapsedTime(&d2h_time, d2h_start, d2h_stop);
//     printf("设备到主机结果传输时间: %.3f ms\n", d2h_time);
//
//     // 总执行时间 = 计算时间 + 数据传输时间
//     float total_exec_time = total_h2d_time + milliseconds + d2h_time;
//     printf("总执行时间 (计算 + 通信): %.3f ms\n", total_exec_time);
//
//     // cudaEventRecord(total_stop, 0);
//     // cudaEventSynchronize(total_stop);
//     // float total_ms;
//     // cudaEventElapsedTime(&total_ms, total_start, total_stop);
//     // printf("Total time: %.2f ms\n", total_ms);
//
//     // 9. 验证结果
//     std::vector<float> h_values_cpu(nnz);
//     std::cout << "开始CPU参考计算..." << std::endl;
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//     std::cout << "CPU参考计算完成" << std::endl;
//
//     // 10. 结果对比
//     int correct_count = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_gpu[i] - h_values_cpu[i]);
//         max_error = fmax(max_error, diff);
//         if (diff < 1e-4) correct_count++;
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct_count / nnz) << "%" << std::endl;
//
//     // 11. 资源释放
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//     cudaEventDestroy(start);
//     cudaEventDestroy(stop);
//     cudaEventDestroy(comm_start);
//     cudaEventDestroy(comm_stop);
//     cudaEventDestroy(d2h_start);
//     cudaEventDestroy(d2h_stop);
//
//     return 0;
// }
//
// // 三级负载均衡
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 加载矩阵: /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 行数: 12471, 列数: 872622, 非零元数: 22624727
// // CSR转换完成
// // 主机到设备数据传输时间: 41.960 ms
// // CSR结构主机到设备传输时间: 8.438 ms
// // 总主机到设备传输时间: 50.398 ms
// // 开始执行SDDMM...
// // [步骤1] 行非零元统计耗时: 0.000 ms
// // [步骤2] GPU动态分区耗时: 0.058 ms (高密度行: 11012, 低密度行: 1459)
// // [步骤3] 负载均衡耗时: 0.033 ms (超长: 7719, 长: 2423, 短: 870)
// // [步骤4] 高密度区计算耗时: 77.732 ms
// // [步骤5] 低密度区计算耗时: 0.453 ms
// // SDDMM执行完成
// // SDDMM计算时间: 80.789 ms
// // GFLOPS: 71.69
// // 设备到主机结果传输时间: 9.722 ms
// // 总执行时间 (计算 + 通信): 140.909 ms
// // 开始CPU参考计算...
// // CPU参考计算完成
// // 验证结果:
// // 最大绝对误差: 3.43323e-05
// // 正确率: 100%
// //
// // 进程已结束，退出代码为 0
