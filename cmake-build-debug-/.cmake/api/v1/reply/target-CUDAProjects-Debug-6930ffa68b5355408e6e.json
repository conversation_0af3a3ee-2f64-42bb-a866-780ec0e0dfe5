{"artifacts": [{"path": "CUDAProjects"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 24, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=c++11 \"--generate-code=arch=compute_30,code=[compute_30,sm_30]\""}], "language": "CUDA", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 3, 7]}, {"compileCommandFragments": [{"fragment": " -fopenmp -g -std=gnu++11 -fdiagnostics-color=always"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [5]}], "id": "CUDAProjects::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-fopenmp -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-L/usr/local/cuda/targets/x86_64-linux/lib/stubs", "role": "libraryPath"}, {"fragment": "-L/usr/local/cuda/targets/x86_64-linux/lib", "role": "libraryPath"}, {"fragment": "-lcu<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-lcudart_static", "role": "libraries"}, {"fragment": "-lrt", "role": "libraries"}, {"fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "CUDAProjects", "nameOnDisk": "CUDAProjects", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 3, 5, 7]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 2, 4, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "sddmm.cu", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "util.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "kernel.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "sddmm_attn.cu", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "BinaryHash.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "BinaryHash.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Evaluation.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "sph.cu", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}