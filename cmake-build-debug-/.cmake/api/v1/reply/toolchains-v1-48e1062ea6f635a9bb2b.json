{"kind": "toolchains", "toolchains": [{"compiler": {"id": "NVIDIA", "implicit": {"includeDirectories": ["/usr/include/c++/4.8.5", "/usr/include/c++/4.8.5/x86_64-redhat-linux", "/usr/include/c++/4.8.5/backward", "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include", "/usr/local/include", "/usr/include"], "linkDirectories": ["/usr/local/cuda/targets/x86_64-linux/lib/stubs", "/usr/local/cuda/targets/x86_64-linux/lib", "/opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8", "/opt/rh/devtoolset-8/root/usr/lib64", "/lib64", "/usr/lib64", "/usr/local/cuda-10.2/lib64", "/opt/rh/devtoolset-8/root/usr/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc_s", "gcc", "c", "gcc_s", "gcc"]}, "path": "/usr/local/cuda/bin/nvcc", "version": "10.2.89"}, "language": "CUDA", "sourceFileExtensions": ["cu"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/opt/rh/devtoolset-8/root/usr/include/c++/8", "/opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux", "/opt/rh/devtoolset-8/root/usr/include/c++/8/backward", "/opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/include", "/usr/local/include", "/opt/rh/devtoolset-8/root/usr/include", "/usr/include"], "linkDirectories": ["/opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8", "/opt/rh/devtoolset-8/root/usr/lib64", "/lib64", "/usr/lib64", "/usr/local/cuda-10.2/lib64", "/opt/rh/devtoolset-8/root/usr/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc_s", "gcc", "c", "gcc_s", "gcc"]}, "path": "/usr/bin/c++", "version": "8.3.1"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "m", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}