{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.28"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "CUDAProjects", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "CUDAProjects::@6890427a1f51a3e7e1df", "jsonFile": "target-CUDAProjects-Debug-6930ffa68b5355408e6e.json", "name": "CUDAProjects", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-", "source": "/data/data/qjs/CLionProjects/CUDAProjects"}, "version": {"major": 2, "minor": 6}}