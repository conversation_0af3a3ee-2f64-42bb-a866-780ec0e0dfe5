{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "cmake-build-debug-/CMakeFiles/3.28.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"}, {"isGenerated": true, "path": "cmake-build-debug-/CMakeFiles/3.28.3/CMakeCUDACompiler.cmake"}, {"isGenerated": true, "path": "cmake-build-debug-/CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeCUDAInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/NVIDIA-CUDA.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/NVIDIA.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindCUDAToolkit.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindOpenMP.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/cmake/cmake/share/cmake-3.28/Modules/FindPackageMessage.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-", "source": "/data/data/qjs/CLionProjects/CUDAProjects"}, "version": {"major": 1, "minor": 0}}