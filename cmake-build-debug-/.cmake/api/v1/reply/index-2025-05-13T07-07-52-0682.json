{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/opt/cmake/cmake/bin/cmake", "cpack": "/opt/cmake/cmake/bin/cpack", "ctest": "/opt/cmake/cmake/bin/ctest", "root": "/opt/cmake/cmake/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 3, "string": "3.28.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-ca89b6836847a0c47511.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-fd18b9343ef4e1404714.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-a11e1b4991505e522804.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-48e1062ea6f635a9bb2b.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-fd18b9343ef4e1404714.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-a11e1b4991505e522804.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-ca89b6836847a0c47511.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, "toolchains-v1": {"jsonFile": "toolchains-v1-48e1062ea6f635a9bb2b.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}