# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/cmake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /data/data/qjs/CLionProjects/CUDAProjects

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/cmake/cmake/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/cmake/cmake/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named CUDAProjects

# Build rule for target.
CUDAProjects: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CUDAProjects
.PHONY : CUDAProjects

# fast build rule for target.
CUDAProjects/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/build
.PHONY : CUDAProjects/fast

BinaryHash.o: BinaryHash.cpp.o
.PHONY : BinaryHash.o

# target to build an object file
BinaryHash.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o
.PHONY : BinaryHash.cpp.o

BinaryHash.i: BinaryHash.cpp.i
.PHONY : BinaryHash.i

# target to preprocess a source file
BinaryHash.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.i
.PHONY : BinaryHash.cpp.i

BinaryHash.s: BinaryHash.cpp.s
.PHONY : BinaryHash.s

# target to generate assembly for a file
BinaryHash.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.s
.PHONY : BinaryHash.cpp.s

sddmm.o: sddmm.cu.o
.PHONY : sddmm.o

# target to build an object file
sddmm.cu.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sddmm.cu.o
.PHONY : sddmm.cu.o

sddmm.i: sddmm.cu.i
.PHONY : sddmm.i

# target to preprocess a source file
sddmm.cu.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sddmm.cu.i
.PHONY : sddmm.cu.i

sddmm.s: sddmm.cu.s
.PHONY : sddmm.s

# target to generate assembly for a file
sddmm.cu.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sddmm.cu.s
.PHONY : sddmm.cu.s

sddmm_attn.o: sddmm_attn.cu.o
.PHONY : sddmm_attn.o

# target to build an object file
sddmm_attn.cu.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o
.PHONY : sddmm_attn.cu.o

sddmm_attn.i: sddmm_attn.cu.i
.PHONY : sddmm_attn.i

# target to preprocess a source file
sddmm_attn.cu.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.i
.PHONY : sddmm_attn.cu.i

sddmm_attn.s: sddmm_attn.cu.s
.PHONY : sddmm_attn.s

# target to generate assembly for a file
sddmm_attn.cu.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.s
.PHONY : sddmm_attn.cu.s

sph.o: sph.cu.o
.PHONY : sph.o

# target to build an object file
sph.cu.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sph.cu.o
.PHONY : sph.cu.o

sph.i: sph.cu.i
.PHONY : sph.i

# target to preprocess a source file
sph.cu.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sph.cu.i
.PHONY : sph.cu.i

sph.s: sph.cu.s
.PHONY : sph.s

# target to generate assembly for a file
sph.cu.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/CUDAProjects.dir/build.make CMakeFiles/CUDAProjects.dir/sph.cu.s
.PHONY : sph.cu.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... CUDAProjects"
	@echo "... BinaryHash.o"
	@echo "... BinaryHash.i"
	@echo "... BinaryHash.s"
	@echo "... sddmm.o"
	@echo "... sddmm.i"
	@echo "... sddmm.s"
	@echo "... sddmm_attn.o"
	@echo "... sddmm_attn.i"
	@echo "... sddmm_attn.s"
	@echo "... sph.o"
	@echo "... sph.i"
	@echo "... sph.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

