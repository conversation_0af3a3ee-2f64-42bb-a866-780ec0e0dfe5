# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/data/data/qjs/CLionProjects/CUDAProjects/CMakeLists.txt"
  "CMakeFiles/3.28.3/CMakeCUDACompiler.cmake"
  "CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeCUDAInformation.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/CheckLibraryExists.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/NVIDIA-CUDA.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Compiler/NVIDIA.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/FindCUDAToolkit.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/FindOpenMP.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/FindPackageMessage.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/FindThreads.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/Linux.cmake"
  "/opt/cmake/cmake/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/CUDAProjects.dir/DependInfo.cmake"
  )
