
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/data/data/qjs/CLionProjects/CUDAProjects/sddmm.cu" "CMakeFiles/CUDAProjects.dir/sddmm.cu.o" "gcc" "CMakeFiles/CUDAProjects.dir/sddmm.cu.o.d"
  "/data/data/qjs/CLionProjects/CUDAProjects/sddmm_attn.cu" "CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o" "gcc" "CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o.d"
  "/data/data/qjs/CLionProjects/CUDAProjects/sph.cu" "CMakeFiles/CUDAProjects.dir/sph.cu.o" "gcc" "CMakeFiles/CUDAProjects.dir/sph.cu.o.d"
  "/data/data/qjs/CLionProjects/CUDAProjects/BinaryHash.cpp" "CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o" "gcc" "CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
