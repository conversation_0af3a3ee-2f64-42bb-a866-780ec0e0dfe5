# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/cmake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /data/data/qjs/CLionProjects/CUDAProjects

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-

# Include any dependencies generated for this target.
include CMakeFiles/CUDAProjects.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/CUDAProjects.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CUDAProjects.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/CUDAProjects.dir/flags.make

CMakeFiles/CUDAProjects.dir/sddmm.cu.o: CMakeFiles/CUDAProjects.dir/flags.make
CMakeFiles/CUDAProjects.dir/sddmm.cu.o: /data/data/qjs/CLionProjects/CUDAProjects/sddmm.cu
CMakeFiles/CUDAProjects.dir/sddmm.cu.o: CMakeFiles/CUDAProjects.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CUDA object CMakeFiles/CUDAProjects.dir/sddmm.cu.o"
	/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT CMakeFiles/CUDAProjects.dir/sddmm.cu.o -MF CMakeFiles/CUDAProjects.dir/sddmm.cu.o.d -x cu -rdc=true -c /data/data/qjs/CLionProjects/CUDAProjects/sddmm.cu -o CMakeFiles/CUDAProjects.dir/sddmm.cu.o

CMakeFiles/CUDAProjects.dir/sddmm.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CUDA source to CMakeFiles/CUDAProjects.dir/sddmm.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

CMakeFiles/CUDAProjects.dir/sddmm.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CUDA source to assembly CMakeFiles/CUDAProjects.dir/sddmm.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o: CMakeFiles/CUDAProjects.dir/flags.make
CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o: /data/data/qjs/CLionProjects/CUDAProjects/sddmm_attn.cu
CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o: CMakeFiles/CUDAProjects.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CUDA object CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o"
	/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o -MF CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o.d -x cu -rdc=true -c /data/data/qjs/CLionProjects/CUDAProjects/sddmm_attn.cu -o CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o

CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CUDA source to CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CUDA source to assembly CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o: CMakeFiles/CUDAProjects.dir/flags.make
CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o: /data/data/qjs/CLionProjects/CUDAProjects/BinaryHash.cpp
CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o: CMakeFiles/CUDAProjects.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o -MF CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o.d -o CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o -c /data/data/qjs/CLionProjects/CUDAProjects/BinaryHash.cpp

CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /data/data/qjs/CLionProjects/CUDAProjects/BinaryHash.cpp > CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.i

CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /data/data/qjs/CLionProjects/CUDAProjects/BinaryHash.cpp -o CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.s

CMakeFiles/CUDAProjects.dir/sph.cu.o: CMakeFiles/CUDAProjects.dir/flags.make
CMakeFiles/CUDAProjects.dir/sph.cu.o: /data/data/qjs/CLionProjects/CUDAProjects/sph.cu
CMakeFiles/CUDAProjects.dir/sph.cu.o: CMakeFiles/CUDAProjects.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CUDA object CMakeFiles/CUDAProjects.dir/sph.cu.o"
	/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler $(CUDA_DEFINES) $(CUDA_INCLUDES) $(CUDA_FLAGS) -MD -MT CMakeFiles/CUDAProjects.dir/sph.cu.o -MF CMakeFiles/CUDAProjects.dir/sph.cu.o.d -x cu -rdc=true -c /data/data/qjs/CLionProjects/CUDAProjects/sph.cu -o CMakeFiles/CUDAProjects.dir/sph.cu.o

CMakeFiles/CUDAProjects.dir/sph.cu.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CUDA source to CMakeFiles/CUDAProjects.dir/sph.cu.i"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_PREPROCESSED_SOURCE

CMakeFiles/CUDAProjects.dir/sph.cu.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CUDA source to assembly CMakeFiles/CUDAProjects.dir/sph.cu.s"
	$(CMAKE_COMMAND) -E cmake_unimplemented_variable CMAKE_CUDA_CREATE_ASSEMBLY_SOURCE

# Object files for target CUDAProjects
CUDAProjects_OBJECTS = \
"CMakeFiles/CUDAProjects.dir/sddmm.cu.o" \
"CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o" \
"CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o" \
"CMakeFiles/CUDAProjects.dir/sph.cu.o"

# External object files for target CUDAProjects
CUDAProjects_EXTERNAL_OBJECTS =

CMakeFiles/CUDAProjects.dir/cmake_device_link.o: CMakeFiles/CUDAProjects.dir/sddmm.cu.o
CMakeFiles/CUDAProjects.dir/cmake_device_link.o: CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o
CMakeFiles/CUDAProjects.dir/cmake_device_link.o: CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o
CMakeFiles/CUDAProjects.dir/cmake_device_link.o: CMakeFiles/CUDAProjects.dir/sph.cu.o
CMakeFiles/CUDAProjects.dir/cmake_device_link.o: CMakeFiles/CUDAProjects.dir/build.make
CMakeFiles/CUDAProjects.dir/cmake_device_link.o: CMakeFiles/CUDAProjects.dir/dlink.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CUDA device code CMakeFiles/CUDAProjects.dir/cmake_device_link.o"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/CUDAProjects.dir/dlink.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CUDAProjects.dir/build: CMakeFiles/CUDAProjects.dir/cmake_device_link.o
.PHONY : CMakeFiles/CUDAProjects.dir/build

# Object files for target CUDAProjects
CUDAProjects_OBJECTS = \
"CMakeFiles/CUDAProjects.dir/sddmm.cu.o" \
"CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o" \
"CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o" \
"CMakeFiles/CUDAProjects.dir/sph.cu.o"

# External object files for target CUDAProjects
CUDAProjects_EXTERNAL_OBJECTS =

CUDAProjects: CMakeFiles/CUDAProjects.dir/sddmm.cu.o
CUDAProjects: CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o
CUDAProjects: CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o
CUDAProjects: CMakeFiles/CUDAProjects.dir/sph.cu.o
CUDAProjects: CMakeFiles/CUDAProjects.dir/build.make
CUDAProjects: CMakeFiles/CUDAProjects.dir/cmake_device_link.o
CUDAProjects: CMakeFiles/CUDAProjects.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable CUDAProjects"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/CUDAProjects.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CUDAProjects.dir/build: CUDAProjects
.PHONY : CMakeFiles/CUDAProjects.dir/build

CMakeFiles/CUDAProjects.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/CUDAProjects.dir/cmake_clean.cmake
.PHONY : CMakeFiles/CUDAProjects.dir/clean

CMakeFiles/CUDAProjects.dir/depend:
	cd /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug- && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /data/data/qjs/CLionProjects/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug- /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug- /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles/CUDAProjects.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/CUDAProjects.dir/depend

