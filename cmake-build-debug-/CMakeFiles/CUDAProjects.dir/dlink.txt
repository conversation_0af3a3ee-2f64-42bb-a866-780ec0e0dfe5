/usr/local/cuda/bin/nvcc -forward-unknown-to-host-compiler -g "--generate-code=arch=compute_30,code=[compute_30,sm_30]" -Xcompiler=-fPIC -Wno-deprecated-gpu-targets -shared -dlink CMakeFiles/CUDAProjects.dir/sddmm.cu.o CMakeFiles/CUDAProjects.dir/sddmm_attn.cu.o CMakeFiles/CUDAProjects.dir/BinaryHash.cpp.o CMakeFiles/CUDAProjects.dir/sph.cu.o -o CMakeFiles/CUDAProjects.dir/cmake_device_link.o   -L/usr/local/cuda/targets/x86_64-linux/lib/stubs  -L/usr/local/cuda/targets/x86_64-linux/lib  -lcudadevrt -lcudart_static -lrt -lpthread -ldl  -L"/usr/local/cuda-10.2/lib64"
