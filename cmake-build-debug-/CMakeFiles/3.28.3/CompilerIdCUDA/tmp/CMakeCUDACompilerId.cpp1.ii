# 1 "CMakeCUDACompilerId.cu"
# 1 "<built-in>"
# 1 "<command-line>"
# 1 "/usr/include/stdc-predef.h" 1 3 4
# 1 "<command-line>" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 1
# 61 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
#pragma GCC diagnostic push


#pragma GCC diagnostic ignored "-Wunused-function"
# 83 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_config.h" 1
# 206 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_config.h"
# 1 "/usr/include/features.h" 1 3 4
# 375 "/usr/include/features.h" 3 4
# 1 "/usr/include/sys/cdefs.h" 1 3 4
# 392 "/usr/include/sys/cdefs.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 393 "/usr/include/sys/cdefs.h" 2 3 4
# 376 "/usr/include/features.h" 2 3 4
# 399 "/usr/include/features.h" 3 4
# 1 "/usr/include/gnu/stubs.h" 1 3 4
# 10 "/usr/include/gnu/stubs.h" 3 4
# 1 "/usr/include/gnu/stubs-64.h" 1 3 4
# 11 "/usr/include/gnu/stubs.h" 2 3 4
# 400 "/usr/include/features.h" 2 3 4
# 207 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_config.h" 2
# 84 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2







# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 56 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_types.h" 1
# 58 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_types.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 59 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_types.h" 2







enum __attribute__((device_builtin)) cudaRoundMode
{
    cudaRoundNearest,
    cudaRoundZero,
    cudaRoundPosInf,
    cudaRoundMinInf
};
# 57 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 2


# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h" 1
# 58 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 59 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_types.h" 1
# 64 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_types.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 65 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_types.h" 2
# 98 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_types.h"
struct __attribute__((device_builtin)) char1
{
    signed char x;
};

struct __attribute__((device_builtin)) uchar1
{
    unsigned char x;
};


struct __attribute__((device_builtin)) __attribute__((aligned(2))) char2
{
    signed char x, y;
};

struct __attribute__((device_builtin)) __attribute__((aligned(2))) uchar2
{
    unsigned char x, y;
};

struct __attribute__((device_builtin)) char3
{
    signed char x, y, z;
};

struct __attribute__((device_builtin)) uchar3
{
    unsigned char x, y, z;
};

struct __attribute__((device_builtin)) __attribute__((aligned(4))) char4
{
    signed char x, y, z, w;
};

struct __attribute__((device_builtin)) __attribute__((aligned(4))) uchar4
{
    unsigned char x, y, z, w;
};

struct __attribute__((device_builtin)) short1
{
    short x;
};

struct __attribute__((device_builtin)) ushort1
{
    unsigned short x;
};

struct __attribute__((device_builtin)) __attribute__((aligned(4))) short2
{
    short x, y;
};

struct __attribute__((device_builtin)) __attribute__((aligned(4))) ushort2
{
    unsigned short x, y;
};

struct __attribute__((device_builtin)) short3
{
    short x, y, z;
};

struct __attribute__((device_builtin)) ushort3
{
    unsigned short x, y, z;
};

struct __attribute__((device_builtin)) __attribute__((aligned(8))) short4 { short x; short y; short z; short w; };
struct __attribute__((device_builtin)) __attribute__((aligned(8))) ushort4 { unsigned short x; unsigned short y; unsigned short z; unsigned short w; };

struct __attribute__((device_builtin)) int1
{
    int x;
};

struct __attribute__((device_builtin)) uint1
{
    unsigned int x;
};

struct __attribute__((device_builtin)) __attribute__((aligned(8))) int2 { int x; int y; };
struct __attribute__((device_builtin)) __attribute__((aligned(8))) uint2 { unsigned int x; unsigned int y; };

struct __attribute__((device_builtin)) int3
{
    int x, y, z;
};

struct __attribute__((device_builtin)) uint3
{
    unsigned int x, y, z;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) int4
{
    int x, y, z, w;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) uint4
{
    unsigned int x, y, z, w;
};

struct __attribute__((device_builtin)) long1
{
    long int x;
};

struct __attribute__((device_builtin)) ulong1
{
    unsigned long x;
};






struct __attribute__((device_builtin)) __attribute__((aligned(2*sizeof(long int)))) long2
{
    long int x, y;
};

struct __attribute__((device_builtin)) __attribute__((aligned(2*sizeof(unsigned long int)))) ulong2
{
    unsigned long int x, y;
};



struct __attribute__((device_builtin)) long3
{
    long int x, y, z;
};

struct __attribute__((device_builtin)) ulong3
{
    unsigned long int x, y, z;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) long4
{
    long int x, y, z, w;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) ulong4
{
    unsigned long int x, y, z, w;
};

struct __attribute__((device_builtin)) float1
{
    float x;
};
# 274 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_types.h"
struct __attribute__((device_builtin)) __attribute__((aligned(8))) float2 { float x; float y; };




struct __attribute__((device_builtin)) float3
{
    float x, y, z;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) float4
{
    float x, y, z, w;
};

struct __attribute__((device_builtin)) longlong1
{
    long long int x;
};

struct __attribute__((device_builtin)) ulonglong1
{
    unsigned long long int x;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) longlong2
{
    long long int x, y;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) ulonglong2
{
    unsigned long long int x, y;
};

struct __attribute__((device_builtin)) longlong3
{
    long long int x, y, z;
};

struct __attribute__((device_builtin)) ulonglong3
{
    unsigned long long int x, y, z;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) longlong4
{
    long long int x, y, z ,w;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) ulonglong4
{
    unsigned long long int x, y, z, w;
};

struct __attribute__((device_builtin)) double1
{
    double x;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) double2
{
    double x, y;
};

struct __attribute__((device_builtin)) double3
{
    double x, y, z;
};

struct __attribute__((device_builtin)) __attribute__((aligned(16))) double4
{
    double x, y, z, w;
};
# 361 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_types.h"
typedef __attribute__((device_builtin)) struct char1 char1;
typedef __attribute__((device_builtin)) struct uchar1 uchar1;
typedef __attribute__((device_builtin)) struct char2 char2;
typedef __attribute__((device_builtin)) struct uchar2 uchar2;
typedef __attribute__((device_builtin)) struct char3 char3;
typedef __attribute__((device_builtin)) struct uchar3 uchar3;
typedef __attribute__((device_builtin)) struct char4 char4;
typedef __attribute__((device_builtin)) struct uchar4 uchar4;
typedef __attribute__((device_builtin)) struct short1 short1;
typedef __attribute__((device_builtin)) struct ushort1 ushort1;
typedef __attribute__((device_builtin)) struct short2 short2;
typedef __attribute__((device_builtin)) struct ushort2 ushort2;
typedef __attribute__((device_builtin)) struct short3 short3;
typedef __attribute__((device_builtin)) struct ushort3 ushort3;
typedef __attribute__((device_builtin)) struct short4 short4;
typedef __attribute__((device_builtin)) struct ushort4 ushort4;
typedef __attribute__((device_builtin)) struct int1 int1;
typedef __attribute__((device_builtin)) struct uint1 uint1;
typedef __attribute__((device_builtin)) struct int2 int2;
typedef __attribute__((device_builtin)) struct uint2 uint2;
typedef __attribute__((device_builtin)) struct int3 int3;
typedef __attribute__((device_builtin)) struct uint3 uint3;
typedef __attribute__((device_builtin)) struct int4 int4;
typedef __attribute__((device_builtin)) struct uint4 uint4;
typedef __attribute__((device_builtin)) struct long1 long1;
typedef __attribute__((device_builtin)) struct ulong1 ulong1;
typedef __attribute__((device_builtin)) struct long2 long2;
typedef __attribute__((device_builtin)) struct ulong2 ulong2;
typedef __attribute__((device_builtin)) struct long3 long3;
typedef __attribute__((device_builtin)) struct ulong3 ulong3;
typedef __attribute__((device_builtin)) struct long4 long4;
typedef __attribute__((device_builtin)) struct ulong4 ulong4;
typedef __attribute__((device_builtin)) struct float1 float1;
typedef __attribute__((device_builtin)) struct float2 float2;
typedef __attribute__((device_builtin)) struct float3 float3;
typedef __attribute__((device_builtin)) struct float4 float4;
typedef __attribute__((device_builtin)) struct longlong1 longlong1;
typedef __attribute__((device_builtin)) struct ulonglong1 ulonglong1;
typedef __attribute__((device_builtin)) struct longlong2 longlong2;
typedef __attribute__((device_builtin)) struct ulonglong2 ulonglong2;
typedef __attribute__((device_builtin)) struct longlong3 longlong3;
typedef __attribute__((device_builtin)) struct ulonglong3 ulonglong3;
typedef __attribute__((device_builtin)) struct longlong4 longlong4;
typedef __attribute__((device_builtin)) struct ulonglong4 ulonglong4;
typedef __attribute__((device_builtin)) struct double1 double1;
typedef __attribute__((device_builtin)) struct double2 double2;
typedef __attribute__((device_builtin)) struct double3 double3;
typedef __attribute__((device_builtin)) struct double4 double4;







struct __attribute__((device_builtin)) dim3
{
    unsigned int x, y, z;






    __attribute__((host)) __attribute__((device)) dim3(unsigned int vx = 1, unsigned int vy = 1, unsigned int vz = 1) : x(vx), y(vy), z(vz) {}
    __attribute__((host)) __attribute__((device)) dim3(uint3 v) : x(v.x), y(v.y), z(v.z) {}
    __attribute__((host)) __attribute__((device)) operator uint3(void) const { uint3 t; t.x = x; t.y = y; t.z = z; return t; }


};

typedef __attribute__((device_builtin)) struct dim3 dim3;
# 60 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h" 2
# 77 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/limits.h" 1 3 4
# 34 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/limits.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/syslimits.h" 1 3 4






# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/limits.h" 1 3 4
# 168 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/limits.h" 3 4
# 1 "/usr/include/limits.h" 1 3 4
# 144 "/usr/include/limits.h" 3 4
# 1 "/usr/include/bits/posix1_lim.h" 1 3 4
# 160 "/usr/include/bits/posix1_lim.h" 3 4
# 1 "/usr/include/bits/local_lim.h" 1 3 4
# 38 "/usr/include/bits/local_lim.h" 3 4
# 1 "/usr/include/linux/limits.h" 1 3 4
# 39 "/usr/include/bits/local_lim.h" 2 3 4
# 161 "/usr/include/bits/posix1_lim.h" 2 3 4
# 145 "/usr/include/limits.h" 2 3 4



# 1 "/usr/include/bits/posix2_lim.h" 1 3 4
# 149 "/usr/include/limits.h" 2 3 4



# 1 "/usr/include/bits/xopen_lim.h" 1 3 4
# 33 "/usr/include/bits/xopen_lim.h" 3 4
# 1 "/usr/include/bits/stdio_lim.h" 1 3 4
# 34 "/usr/include/bits/xopen_lim.h" 2 3 4
# 153 "/usr/include/limits.h" 2 3 4
# 169 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/limits.h" 2 3 4
# 8 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/syslimits.h" 2 3 4
# 35 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/limits.h" 2 3 4
# 78 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h" 2
# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 1 3 4
# 147 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 3 4
typedef long int ptrdiff_t;
# 212 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 3 4
typedef long unsigned int size_t;
# 79 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h" 2
# 189 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
enum __attribute__((device_builtin)) cudaError
{





    cudaSuccess = 0,





    cudaErrorInvalidValue = 1,





    cudaErrorMemoryAllocation = 2,





    cudaErrorInitializationError = 3,






    cudaErrorCudartUnloading = 4,






    cudaErrorProfilerDisabled = 5,







    cudaErrorProfilerNotInitialized = 6,






    cudaErrorProfilerAlreadyStarted = 7,






     cudaErrorProfilerAlreadyStopped = 8,
# 259 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorInvalidConfiguration = 9,





    cudaErrorInvalidPitchValue = 12,





    cudaErrorInvalidSymbol = 13,







    cudaErrorInvalidHostPointer = 16,







    cudaErrorInvalidDevicePointer = 17,





    cudaErrorInvalidTexture = 18,





    cudaErrorInvalidTextureBinding = 19,






    cudaErrorInvalidChannelDescriptor = 20,





    cudaErrorInvalidMemcpyDirection = 21,
# 322 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorAddressOfConstant = 22,
# 331 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorTextureFetchFailed = 23,
# 340 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorTextureNotBound = 24,
# 349 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorSynchronizationError = 25,





    cudaErrorInvalidFilterSetting = 26,





    cudaErrorInvalidNormSetting = 27,







    cudaErrorMixedDeviceExecution = 28,







    cudaErrorNotYetImplemented = 31,
# 386 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorMemoryValueTooLarge = 32,






    cudaErrorInsufficientDriver = 35,





    cudaErrorInvalidSurface = 37,





    cudaErrorDuplicateVariableName = 43,





    cudaErrorDuplicateTextureName = 44,





    cudaErrorDuplicateSurfaceName = 45,
# 427 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorDevicesUnavailable = 46,
# 440 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorIncompatibleDriverContext = 49,





    cudaErrorMissingConfiguration = 52,
# 455 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorPriorLaunchFailure = 53,






    cudaErrorLaunchMaxDepthExceeded = 65,







    cudaErrorLaunchFileScopedTex = 66,







    cudaErrorLaunchFileScopedSurf = 67,
# 493 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorSyncDepthExceeded = 68,
# 505 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorLaunchPendingCountExceeded = 69,





    cudaErrorInvalidDeviceFunction = 98,





    cudaErrorNoDevice = 100,





    cudaErrorInvalidDevice = 101,




    cudaErrorStartupFailure = 127,




    cudaErrorInvalidKernelImage = 200,
# 543 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorDeviceUninitialized = 201,




    cudaErrorMapBufferObjectFailed = 205,




    cudaErrorUnmapBufferObjectFailed = 206,





    cudaErrorArrayIsMapped = 207,




    cudaErrorAlreadyMapped = 208,







    cudaErrorNoKernelImageForDevice = 209,




    cudaErrorAlreadyAcquired = 210,




    cudaErrorNotMapped = 211,





    cudaErrorNotMappedAsArray = 212,





    cudaErrorNotMappedAsPointer = 213,





    cudaErrorECCUncorrectable = 214,





    cudaErrorUnsupportedLimit = 215,





    cudaErrorDeviceAlreadyInUse = 216,





    cudaErrorPeerAccessUnsupported = 217,





    cudaErrorInvalidPtx = 218,




    cudaErrorInvalidGraphicsContext = 219,





    cudaErrorNvlinkUncorrectable = 220,






    cudaErrorJitCompilerNotFound = 221,




    cudaErrorInvalidSource = 300,




    cudaErrorFileNotFound = 301,




    cudaErrorSharedObjectSymbolNotFound = 302,




    cudaErrorSharedObjectInitFailed = 303,




    cudaErrorOperatingSystem = 304,






    cudaErrorInvalidResourceHandle = 400,





    cudaErrorIllegalState = 401,





    cudaErrorSymbolNotFound = 500,







    cudaErrorNotReady = 600,







    cudaErrorIllegalAddress = 700,
# 711 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorLaunchOutOfResources = 701,
# 722 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorLaunchTimeout = 702,





    cudaErrorLaunchIncompatibleTexturing = 703,






    cudaErrorPeerAccessAlreadyEnabled = 704,






    cudaErrorPeerAccessNotEnabled = 705,
# 755 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorSetOnActiveProcess = 708,






    cudaErrorContextIsDestroyed = 709,






    cudaErrorAssert = 710,






    cudaErrorTooManyPeers = 711,





    cudaErrorHostMemoryAlreadyRegistered = 712,





    cudaErrorHostMemoryNotRegistered = 713,
# 797 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorHardwareStackError = 714,







    cudaErrorIllegalInstruction = 715,
# 814 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorMisalignedAddress = 716,
# 825 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorInvalidAddressSpace = 717,







    cudaErrorInvalidPc = 718,
# 844 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorLaunchFailure = 719,
# 853 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorCooperativeLaunchTooLarge = 720,




    cudaErrorNotPermitted = 800,





    cudaErrorNotSupported = 801,
# 873 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorSystemNotReady = 802,






    cudaErrorSystemDriverMismatch = 803,
# 889 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    cudaErrorCompatNotSupportedOnDevice = 804,




    cudaErrorStreamCaptureUnsupported = 900,





    cudaErrorStreamCaptureInvalidated = 901,





    cudaErrorStreamCaptureMerge = 902,




    cudaErrorStreamCaptureUnmatched = 903,





    cudaErrorStreamCaptureUnjoined = 904,






    cudaErrorStreamCaptureIsolation = 905,





    cudaErrorStreamCaptureImplicit = 906,





    cudaErrorCapturedEvent = 907,






    cudaErrorStreamCaptureWrongThread = 908,




    cudaErrorTimeout = 909,





    cudaErrorGraphExecUpdateFailure = 910,




    cudaErrorUnknown = 999,







    cudaErrorApiFailureBase = 10000
};




enum __attribute__((device_builtin)) cudaChannelFormatKind
{
    cudaChannelFormatKindSigned = 0,
    cudaChannelFormatKindUnsigned = 1,
    cudaChannelFormatKindFloat = 2,
    cudaChannelFormatKindNone = 3
};




struct __attribute__((device_builtin)) cudaChannelFormatDesc
{
    int x;
    int y;
    int z;
    int w;
    enum cudaChannelFormatKind f;
};




typedef struct cudaArray *cudaArray_t;




typedef const struct cudaArray *cudaArray_const_t;

struct cudaArray;




typedef struct cudaMipmappedArray *cudaMipmappedArray_t;




typedef const struct cudaMipmappedArray *cudaMipmappedArray_const_t;

struct cudaMipmappedArray;




enum __attribute__((device_builtin)) cudaMemoryType
{
    cudaMemoryTypeUnregistered = 0,
    cudaMemoryTypeHost = 1,
    cudaMemoryTypeDevice = 2,
    cudaMemoryTypeManaged = 3
};




enum __attribute__((device_builtin)) cudaMemcpyKind
{
    cudaMemcpyHostToHost = 0,
    cudaMemcpyHostToDevice = 1,
    cudaMemcpyDeviceToHost = 2,
    cudaMemcpyDeviceToDevice = 3,
    cudaMemcpyDefault = 4
};






struct __attribute__((device_builtin)) cudaPitchedPtr
{
    void *ptr;
    size_t pitch;
    size_t xsize;
    size_t ysize;
};






struct __attribute__((device_builtin)) cudaExtent
{
    size_t width;
    size_t height;
    size_t depth;
};






struct __attribute__((device_builtin)) cudaPos
{
    size_t x;
    size_t y;
    size_t z;
};




struct __attribute__((device_builtin)) cudaMemcpy3DParms
{
    cudaArray_t srcArray;
    struct cudaPos srcPos;
    struct cudaPitchedPtr srcPtr;

    cudaArray_t dstArray;
    struct cudaPos dstPos;
    struct cudaPitchedPtr dstPtr;

    struct cudaExtent extent;
    enum cudaMemcpyKind kind;
};




struct __attribute__((device_builtin)) cudaMemcpy3DPeerParms
{
    cudaArray_t srcArray;
    struct cudaPos srcPos;
    struct cudaPitchedPtr srcPtr;
    int srcDevice;

    cudaArray_t dstArray;
    struct cudaPos dstPos;
    struct cudaPitchedPtr dstPtr;
    int dstDevice;

    struct cudaExtent extent;
};




struct __attribute__((device_builtin)) cudaMemsetParams {
    void *dst;
    size_t pitch;
    unsigned int value;
    unsigned int elementSize;
    size_t width;
    size_t height;
};
# 1134 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
typedef void ( *cudaHostFn_t)(void *userData);




struct __attribute__((device_builtin)) cudaHostNodeParams {
    cudaHostFn_t fn;
    void* userData;
};




enum __attribute__((device_builtin)) cudaStreamCaptureStatus {
    cudaStreamCaptureStatusNone = 0,
    cudaStreamCaptureStatusActive = 1,
    cudaStreamCaptureStatusInvalidated = 2

};





enum __attribute__((device_builtin)) cudaStreamCaptureMode {
    cudaStreamCaptureModeGlobal = 0,
    cudaStreamCaptureModeThreadLocal = 1,
    cudaStreamCaptureModeRelaxed = 2
};




struct cudaGraphicsResource;




enum __attribute__((device_builtin)) cudaGraphicsRegisterFlags
{
    cudaGraphicsRegisterFlagsNone = 0,
    cudaGraphicsRegisterFlagsReadOnly = 1,
    cudaGraphicsRegisterFlagsWriteDiscard = 2,
    cudaGraphicsRegisterFlagsSurfaceLoadStore = 4,
    cudaGraphicsRegisterFlagsTextureGather = 8
};




enum __attribute__((device_builtin)) cudaGraphicsMapFlags
{
    cudaGraphicsMapFlagsNone = 0,
    cudaGraphicsMapFlagsReadOnly = 1,
    cudaGraphicsMapFlagsWriteDiscard = 2
};




enum __attribute__((device_builtin)) cudaGraphicsCubeFace
{
    cudaGraphicsCubeFacePositiveX = 0x00,
    cudaGraphicsCubeFaceNegativeX = 0x01,
    cudaGraphicsCubeFacePositiveY = 0x02,
    cudaGraphicsCubeFaceNegativeY = 0x03,
    cudaGraphicsCubeFacePositiveZ = 0x04,
    cudaGraphicsCubeFaceNegativeZ = 0x05
};




enum __attribute__((device_builtin)) cudaResourceType
{
    cudaResourceTypeArray = 0x00,
    cudaResourceTypeMipmappedArray = 0x01,
    cudaResourceTypeLinear = 0x02,
    cudaResourceTypePitch2D = 0x03
};




enum __attribute__((device_builtin)) cudaResourceViewFormat
{
    cudaResViewFormatNone = 0x00,
    cudaResViewFormatUnsignedChar1 = 0x01,
    cudaResViewFormatUnsignedChar2 = 0x02,
    cudaResViewFormatUnsignedChar4 = 0x03,
    cudaResViewFormatSignedChar1 = 0x04,
    cudaResViewFormatSignedChar2 = 0x05,
    cudaResViewFormatSignedChar4 = 0x06,
    cudaResViewFormatUnsignedShort1 = 0x07,
    cudaResViewFormatUnsignedShort2 = 0x08,
    cudaResViewFormatUnsignedShort4 = 0x09,
    cudaResViewFormatSignedShort1 = 0x0a,
    cudaResViewFormatSignedShort2 = 0x0b,
    cudaResViewFormatSignedShort4 = 0x0c,
    cudaResViewFormatUnsignedInt1 = 0x0d,
    cudaResViewFormatUnsignedInt2 = 0x0e,
    cudaResViewFormatUnsignedInt4 = 0x0f,
    cudaResViewFormatSignedInt1 = 0x10,
    cudaResViewFormatSignedInt2 = 0x11,
    cudaResViewFormatSignedInt4 = 0x12,
    cudaResViewFormatHalf1 = 0x13,
    cudaResViewFormatHalf2 = 0x14,
    cudaResViewFormatHalf4 = 0x15,
    cudaResViewFormatFloat1 = 0x16,
    cudaResViewFormatFloat2 = 0x17,
    cudaResViewFormatFloat4 = 0x18,
    cudaResViewFormatUnsignedBlockCompressed1 = 0x19,
    cudaResViewFormatUnsignedBlockCompressed2 = 0x1a,
    cudaResViewFormatUnsignedBlockCompressed3 = 0x1b,
    cudaResViewFormatUnsignedBlockCompressed4 = 0x1c,
    cudaResViewFormatSignedBlockCompressed4 = 0x1d,
    cudaResViewFormatUnsignedBlockCompressed5 = 0x1e,
    cudaResViewFormatSignedBlockCompressed5 = 0x1f,
    cudaResViewFormatUnsignedBlockCompressed6H = 0x20,
    cudaResViewFormatSignedBlockCompressed6H = 0x21,
    cudaResViewFormatUnsignedBlockCompressed7 = 0x22
};




struct __attribute__((device_builtin)) cudaResourceDesc {
    enum cudaResourceType resType;

    union {
        struct {
            cudaArray_t array;
        } array;
        struct {
            cudaMipmappedArray_t mipmap;
        } mipmap;
        struct {
            void *devPtr;
            struct cudaChannelFormatDesc desc;
            size_t sizeInBytes;
        } linear;
        struct {
            void *devPtr;
            struct cudaChannelFormatDesc desc;
            size_t width;
            size_t height;
            size_t pitchInBytes;
        } pitch2D;
    } res;
};




struct __attribute__((device_builtin)) cudaResourceViewDesc
{
    enum cudaResourceViewFormat format;
    size_t width;
    size_t height;
    size_t depth;
    unsigned int firstMipmapLevel;
    unsigned int lastMipmapLevel;
    unsigned int firstLayer;
    unsigned int lastLayer;
};




struct __attribute__((device_builtin)) cudaPointerAttributes
{
# 1313 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    __attribute__((deprecated)) enum cudaMemoryType memoryType;





    enum cudaMemoryType type;
# 1330 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    int device;





    void *devicePointer;
# 1345 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    void *hostPointer;






    __attribute__((deprecated)) int isManaged;
};




struct __attribute__((device_builtin)) cudaFuncAttributes
{





   size_t sharedSizeBytes;





   size_t constSizeBytes;




   size_t localSizeBytes;






   int maxThreadsPerBlock;




   int numRegs;






   int ptxVersion;






   int binaryVersion;





   int cacheModeCA;






   int maxDynamicSharedSizeBytes;
# 1424 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
   int preferredShmemCarveout;
};




enum __attribute__((device_builtin)) cudaFuncAttribute
{
    cudaFuncAttributeMaxDynamicSharedMemorySize = 8,
    cudaFuncAttributePreferredSharedMemoryCarveout = 9,
    cudaFuncAttributeMax
};




enum __attribute__((device_builtin)) cudaFuncCache
{
    cudaFuncCachePreferNone = 0,
    cudaFuncCachePreferShared = 1,
    cudaFuncCachePreferL1 = 2,
    cudaFuncCachePreferEqual = 3
};





enum __attribute__((device_builtin)) cudaSharedMemConfig
{
    cudaSharedMemBankSizeDefault = 0,
    cudaSharedMemBankSizeFourByte = 1,
    cudaSharedMemBankSizeEightByte = 2
};




enum __attribute__((device_builtin)) cudaSharedCarveout {
    cudaSharedmemCarveoutDefault = -1,
    cudaSharedmemCarveoutMaxShared = 100,
    cudaSharedmemCarveoutMaxL1 = 0
};




enum __attribute__((device_builtin)) cudaComputeMode
{
    cudaComputeModeDefault = 0,
    cudaComputeModeExclusive = 1,
    cudaComputeModeProhibited = 2,
    cudaComputeModeExclusiveProcess = 3
};




enum __attribute__((device_builtin)) cudaLimit
{
    cudaLimitStackSize = 0x00,
    cudaLimitPrintfFifoSize = 0x01,
    cudaLimitMallocHeapSize = 0x02,
    cudaLimitDevRuntimeSyncDepth = 0x03,
    cudaLimitDevRuntimePendingLaunchCount = 0x04,
    cudaLimitMaxL2FetchGranularity = 0x05
};




enum __attribute__((device_builtin)) cudaMemoryAdvise
{
    cudaMemAdviseSetReadMostly = 1,
    cudaMemAdviseUnsetReadMostly = 2,
    cudaMemAdviseSetPreferredLocation = 3,
    cudaMemAdviseUnsetPreferredLocation = 4,
    cudaMemAdviseSetAccessedBy = 5,
    cudaMemAdviseUnsetAccessedBy = 6
};




enum __attribute__((device_builtin)) cudaMemRangeAttribute
{
    cudaMemRangeAttributeReadMostly = 1,
    cudaMemRangeAttributePreferredLocation = 2,
    cudaMemRangeAttributeAccessedBy = 3,
    cudaMemRangeAttributeLastPrefetchLocation = 4
};




enum __attribute__((device_builtin)) cudaOutputMode
{
    cudaKeyValuePair = 0x00,
    cudaCSV = 0x01
};




enum __attribute__((device_builtin)) cudaDeviceAttr
{
    cudaDevAttrMaxThreadsPerBlock = 1,
    cudaDevAttrMaxBlockDimX = 2,
    cudaDevAttrMaxBlockDimY = 3,
    cudaDevAttrMaxBlockDimZ = 4,
    cudaDevAttrMaxGridDimX = 5,
    cudaDevAttrMaxGridDimY = 6,
    cudaDevAttrMaxGridDimZ = 7,
    cudaDevAttrMaxSharedMemoryPerBlock = 8,
    cudaDevAttrTotalConstantMemory = 9,
    cudaDevAttrWarpSize = 10,
    cudaDevAttrMaxPitch = 11,
    cudaDevAttrMaxRegistersPerBlock = 12,
    cudaDevAttrClockRate = 13,
    cudaDevAttrTextureAlignment = 14,
    cudaDevAttrGpuOverlap = 15,
    cudaDevAttrMultiProcessorCount = 16,
    cudaDevAttrKernelExecTimeout = 17,
    cudaDevAttrIntegrated = 18,
    cudaDevAttrCanMapHostMemory = 19,
    cudaDevAttrComputeMode = 20,
    cudaDevAttrMaxTexture1DWidth = 21,
    cudaDevAttrMaxTexture2DWidth = 22,
    cudaDevAttrMaxTexture2DHeight = 23,
    cudaDevAttrMaxTexture3DWidth = 24,
    cudaDevAttrMaxTexture3DHeight = 25,
    cudaDevAttrMaxTexture3DDepth = 26,
    cudaDevAttrMaxTexture2DLayeredWidth = 27,
    cudaDevAttrMaxTexture2DLayeredHeight = 28,
    cudaDevAttrMaxTexture2DLayeredLayers = 29,
    cudaDevAttrSurfaceAlignment = 30,
    cudaDevAttrConcurrentKernels = 31,
    cudaDevAttrEccEnabled = 32,
    cudaDevAttrPciBusId = 33,
    cudaDevAttrPciDeviceId = 34,
    cudaDevAttrTccDriver = 35,
    cudaDevAttrMemoryClockRate = 36,
    cudaDevAttrGlobalMemoryBusWidth = 37,
    cudaDevAttrL2CacheSize = 38,
    cudaDevAttrMaxThreadsPerMultiProcessor = 39,
    cudaDevAttrAsyncEngineCount = 40,
    cudaDevAttrUnifiedAddressing = 41,
    cudaDevAttrMaxTexture1DLayeredWidth = 42,
    cudaDevAttrMaxTexture1DLayeredLayers = 43,
    cudaDevAttrMaxTexture2DGatherWidth = 45,
    cudaDevAttrMaxTexture2DGatherHeight = 46,
    cudaDevAttrMaxTexture3DWidthAlt = 47,
    cudaDevAttrMaxTexture3DHeightAlt = 48,
    cudaDevAttrMaxTexture3DDepthAlt = 49,
    cudaDevAttrPciDomainId = 50,
    cudaDevAttrTexturePitchAlignment = 51,
    cudaDevAttrMaxTextureCubemapWidth = 52,
    cudaDevAttrMaxTextureCubemapLayeredWidth = 53,
    cudaDevAttrMaxTextureCubemapLayeredLayers = 54,
    cudaDevAttrMaxSurface1DWidth = 55,
    cudaDevAttrMaxSurface2DWidth = 56,
    cudaDevAttrMaxSurface2DHeight = 57,
    cudaDevAttrMaxSurface3DWidth = 58,
    cudaDevAttrMaxSurface3DHeight = 59,
    cudaDevAttrMaxSurface3DDepth = 60,
    cudaDevAttrMaxSurface1DLayeredWidth = 61,
    cudaDevAttrMaxSurface1DLayeredLayers = 62,
    cudaDevAttrMaxSurface2DLayeredWidth = 63,
    cudaDevAttrMaxSurface2DLayeredHeight = 64,
    cudaDevAttrMaxSurface2DLayeredLayers = 65,
    cudaDevAttrMaxSurfaceCubemapWidth = 66,
    cudaDevAttrMaxSurfaceCubemapLayeredWidth = 67,
    cudaDevAttrMaxSurfaceCubemapLayeredLayers = 68,
    cudaDevAttrMaxTexture1DLinearWidth = 69,
    cudaDevAttrMaxTexture2DLinearWidth = 70,
    cudaDevAttrMaxTexture2DLinearHeight = 71,
    cudaDevAttrMaxTexture2DLinearPitch = 72,
    cudaDevAttrMaxTexture2DMipmappedWidth = 73,
    cudaDevAttrMaxTexture2DMipmappedHeight = 74,
    cudaDevAttrComputeCapabilityMajor = 75,
    cudaDevAttrComputeCapabilityMinor = 76,
    cudaDevAttrMaxTexture1DMipmappedWidth = 77,
    cudaDevAttrStreamPrioritiesSupported = 78,
    cudaDevAttrGlobalL1CacheSupported = 79,
    cudaDevAttrLocalL1CacheSupported = 80,
    cudaDevAttrMaxSharedMemoryPerMultiprocessor = 81,
    cudaDevAttrMaxRegistersPerMultiprocessor = 82,
    cudaDevAttrManagedMemory = 83,
    cudaDevAttrIsMultiGpuBoard = 84,
    cudaDevAttrMultiGpuBoardGroupID = 85,
    cudaDevAttrHostNativeAtomicSupported = 86,
    cudaDevAttrSingleToDoublePrecisionPerfRatio = 87,
    cudaDevAttrPageableMemoryAccess = 88,
    cudaDevAttrConcurrentManagedAccess = 89,
    cudaDevAttrComputePreemptionSupported = 90,
    cudaDevAttrCanUseHostPointerForRegisteredMem = 91,
    cudaDevAttrReserved92 = 92,
    cudaDevAttrReserved93 = 93,
    cudaDevAttrReserved94 = 94,
    cudaDevAttrCooperativeLaunch = 95,
    cudaDevAttrCooperativeMultiDeviceLaunch = 96,
    cudaDevAttrMaxSharedMemoryPerBlockOptin = 97,
    cudaDevAttrCanFlushRemoteWrites = 98,
    cudaDevAttrHostRegisterSupported = 99,
    cudaDevAttrPageableMemoryAccessUsesHostPageTables = 100,
    cudaDevAttrDirectManagedMemAccessFromHost = 101
};





enum __attribute__((device_builtin)) cudaDeviceP2PAttr {
    cudaDevP2PAttrPerformanceRank = 1,
    cudaDevP2PAttrAccessSupported = 2,
    cudaDevP2PAttrNativeAtomicSupported = 3,
    cudaDevP2PAttrCudaArrayAccessSupported = 4
};






struct __attribute__((device_builtin)) CUuuid_st {
    char bytes[16];
};
typedef __attribute__((device_builtin)) struct CUuuid_st CUuuid;

typedef __attribute__((device_builtin)) struct CUuuid_st cudaUUID_t;




struct __attribute__((device_builtin)) cudaDeviceProp
{
    char name[256];
    cudaUUID_t uuid;
    char luid[8];
    unsigned int luidDeviceNodeMask;
    size_t totalGlobalMem;
    size_t sharedMemPerBlock;
    int regsPerBlock;
    int warpSize;
    size_t memPitch;
    int maxThreadsPerBlock;
    int maxThreadsDim[3];
    int maxGridSize[3];
    int clockRate;
    size_t totalConstMem;
    int major;
    int minor;
    size_t textureAlignment;
    size_t texturePitchAlignment;
    int deviceOverlap;
    int multiProcessorCount;
    int kernelExecTimeoutEnabled;
    int integrated;
    int canMapHostMemory;
    int computeMode;
    int maxTexture1D;
    int maxTexture1DMipmap;
    int maxTexture1DLinear;
    int maxTexture2D[2];
    int maxTexture2DMipmap[2];
    int maxTexture2DLinear[3];
    int maxTexture2DGather[2];
    int maxTexture3D[3];
    int maxTexture3DAlt[3];
    int maxTextureCubemap;
    int maxTexture1DLayered[2];
    int maxTexture2DLayered[3];
    int maxTextureCubemapLayered[2];
    int maxSurface1D;
    int maxSurface2D[2];
    int maxSurface3D[3];
    int maxSurface1DLayered[2];
    int maxSurface2DLayered[3];
    int maxSurfaceCubemap;
    int maxSurfaceCubemapLayered[2];
    size_t surfaceAlignment;
    int concurrentKernels;
    int ECCEnabled;
    int pciBusID;
    int pciDeviceID;
    int pciDomainID;
    int tccDriver;
    int asyncEngineCount;
    int unifiedAddressing;
    int memoryClockRate;
    int memoryBusWidth;
    int l2CacheSize;
    int maxThreadsPerMultiProcessor;
    int streamPrioritiesSupported;
    int globalL1CacheSupported;
    int localL1CacheSupported;
    size_t sharedMemPerMultiprocessor;
    int regsPerMultiprocessor;
    int managedMemory;
    int isMultiGpuBoard;
    int multiGpuBoardGroupID;
    int hostNativeAtomicSupported;
    int singleToDoublePrecisionPerfRatio;
    int pageableMemoryAccess;
    int concurrentManagedAccess;
    int computePreemptionSupported;
    int canUseHostPointerForRegisteredMem;
    int cooperativeLaunch;
    int cooperativeMultiDeviceLaunch;
    size_t sharedMemPerBlockOptin;
    int pageableMemoryAccessUsesHostPageTables;
    int directManagedMemAccessFromHost;
};
# 1826 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
typedef __attribute__((device_builtin)) struct __attribute__((device_builtin)) cudaIpcEventHandle_st
{
    char reserved[64];
}cudaIpcEventHandle_t;




typedef __attribute__((device_builtin)) struct __attribute__((device_builtin)) cudaIpcMemHandle_st
{
    char reserved[64];
}cudaIpcMemHandle_t;




enum __attribute__((device_builtin)) cudaExternalMemoryHandleType {



    cudaExternalMemoryHandleTypeOpaqueFd = 1,



    cudaExternalMemoryHandleTypeOpaqueWin32 = 2,



    cudaExternalMemoryHandleTypeOpaqueWin32Kmt = 3,



    cudaExternalMemoryHandleTypeD3D12Heap = 4,



    cudaExternalMemoryHandleTypeD3D12Resource = 5,



    cudaExternalMemoryHandleTypeD3D11Resource = 6,



    cudaExternalMemoryHandleTypeD3D11ResourceKmt = 7,



    cudaExternalMemoryHandleTypeNvSciBuf = 8
};
# 1917 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
struct __attribute__((device_builtin)) cudaExternalMemoryHandleDesc {



    enum cudaExternalMemoryHandleType type;
    union {





        int fd;
# 1944 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
        struct {



            void *handle;




            const void *name;
        } win32;




        const void *nvSciBufObject;
    } handle;



    unsigned long long size;



    unsigned int flags;
};




struct __attribute__((device_builtin)) cudaExternalMemoryBufferDesc {



    unsigned long long offset;



    unsigned long long size;



    unsigned int flags;
};




struct __attribute__((device_builtin)) cudaExternalMemoryMipmappedArrayDesc {




    unsigned long long offset;



    struct cudaChannelFormatDesc formatDesc;



    struct cudaExtent extent;




    unsigned int flags;



    unsigned int numLevels;
};




enum __attribute__((device_builtin)) cudaExternalSemaphoreHandleType {



    cudaExternalSemaphoreHandleTypeOpaqueFd = 1,



    cudaExternalSemaphoreHandleTypeOpaqueWin32 = 2,



    cudaExternalSemaphoreHandleTypeOpaqueWin32Kmt = 3,



    cudaExternalSemaphoreHandleTypeD3D12Fence = 4,



    cudaExternalSemaphoreHandleTypeD3D11Fence = 5,



     cudaExternalSemaphoreHandleTypeNvSciSync = 6,



    cudaExternalSemaphoreHandleTypeKeyedMutex = 7,



    cudaExternalSemaphoreHandleTypeKeyedMutexKmt = 8
};




struct __attribute__((device_builtin)) cudaExternalSemaphoreHandleDesc {



    enum cudaExternalSemaphoreHandleType type;
    union {




        int fd;
# 2083 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
        struct {



            void *handle;




            const void *name;
        } win32;



        const void* nvSciSyncObj;
    } handle;



    unsigned int flags;
};




struct __attribute__((device_builtin)) cudaExternalSemaphoreSignalParams {
    struct {



        struct {



            unsigned long long value;
        } fence;
        union {




            void *fence;
            unsigned long long reserved;
        } nvSciSync;



        struct {



            unsigned long long key;
        } keyedMutex;
    } params;
# 2147 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    unsigned int flags;
};




struct __attribute__((device_builtin)) cudaExternalSemaphoreWaitParams {
    struct {



        struct {



            unsigned long long value;
        } fence;
        union {




            void *fence;
            unsigned long long reserved;
        } nvSciSync;



        struct {



            unsigned long long key;



            unsigned int timeoutMs;
        } keyedMutex;
    } params;
# 2196 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
    unsigned int flags;
};
# 2209 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_types.h"
typedef __attribute__((device_builtin)) enum cudaError cudaError_t;




typedef __attribute__((device_builtin)) struct CUstream_st *cudaStream_t;




typedef __attribute__((device_builtin)) struct CUevent_st *cudaEvent_t;




typedef __attribute__((device_builtin)) struct cudaGraphicsResource *cudaGraphicsResource_t;




typedef __attribute__((device_builtin)) enum cudaOutputMode cudaOutputMode_t;




typedef __attribute__((device_builtin)) struct CUexternalMemory_st *cudaExternalMemory_t;




typedef __attribute__((device_builtin)) struct CUexternalSemaphore_st *cudaExternalSemaphore_t;




typedef __attribute__((device_builtin)) struct CUgraph_st *cudaGraph_t;




typedef __attribute__((device_builtin)) struct CUgraphNode_st *cudaGraphNode_t;




enum __attribute__((device_builtin)) cudaCGScope {
    cudaCGScopeInvalid = 0,
    cudaCGScopeGrid = 1,
    cudaCGScopeMultiGrid = 2
};




struct __attribute__((device_builtin)) cudaLaunchParams
{
    void *func;
    dim3 gridDim;
    dim3 blockDim;
    void **args;
    size_t sharedMem;
    cudaStream_t stream;
};




struct __attribute__((device_builtin)) cudaKernelNodeParams {
    void* func;
    dim3 gridDim;
    dim3 blockDim;
    unsigned int sharedMemBytes;
    void **kernelParams;
    void **extra;
};




enum __attribute__((device_builtin)) cudaGraphNodeType {
    cudaGraphNodeTypeKernel = 0x00,
    cudaGraphNodeTypeMemcpy = 0x01,
    cudaGraphNodeTypeMemset = 0x02,
    cudaGraphNodeTypeHost = 0x03,
    cudaGraphNodeTypeGraph = 0x04,
    cudaGraphNodeTypeEmpty = 0x05,
    cudaGraphNodeTypeCount
};




typedef struct CUgraphExec_st* cudaGraphExec_t;




enum __attribute__((device_builtin)) cudaGraphExecUpdateResult {
    cudaGraphExecUpdateSuccess = 0x0,
    cudaGraphExecUpdateError = 0x1,
    cudaGraphExecUpdateErrorTopologyChanged = 0x2,
    cudaGraphExecUpdateErrorNodeTypeChanged = 0x3,
    cudaGraphExecUpdateErrorFunctionChanged = 0x4,
    cudaGraphExecUpdateErrorParametersChanged = 0x5,
    cudaGraphExecUpdateErrorNotSupported = 0x6
};
# 60 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 2


# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/surface_types.h" 1
# 84 "/usr/local/cuda/bin/../targets/x86_64-linux/include/surface_types.h"
enum __attribute__((device_builtin)) cudaSurfaceBoundaryMode
{
    cudaBoundaryModeZero = 0,
    cudaBoundaryModeClamp = 1,
    cudaBoundaryModeTrap = 2
};




enum __attribute__((device_builtin)) cudaSurfaceFormatMode
{
    cudaFormatModeForced = 0,
    cudaFormatModeAuto = 1
};




struct __attribute__((device_builtin)) surfaceReference
{



    struct cudaChannelFormatDesc channelDesc;
};




typedef __attribute__((device_builtin)) unsigned long long cudaSurfaceObject_t;
# 63 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_types.h" 1
# 84 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_types.h"
enum __attribute__((device_builtin)) cudaTextureAddressMode
{
    cudaAddressModeWrap = 0,
    cudaAddressModeClamp = 1,
    cudaAddressModeMirror = 2,
    cudaAddressModeBorder = 3
};




enum __attribute__((device_builtin)) cudaTextureFilterMode
{
    cudaFilterModePoint = 0,
    cudaFilterModeLinear = 1
};




enum __attribute__((device_builtin)) cudaTextureReadMode
{
    cudaReadModeElementType = 0,
    cudaReadModeNormalizedFloat = 1
};




struct __attribute__((device_builtin)) textureReference
{



    int normalized;



    enum cudaTextureFilterMode filterMode;



    enum cudaTextureAddressMode addressMode[3];



    struct cudaChannelFormatDesc channelDesc;



    int sRGB;



    unsigned int maxAnisotropy;



    enum cudaTextureFilterMode mipmapFilterMode;



    float mipmapLevelBias;



    float minMipmapLevelClamp;



    float maxMipmapLevelClamp;
    int __cudaReserved[15];
};




struct __attribute__((device_builtin)) cudaTextureDesc
{



    enum cudaTextureAddressMode addressMode[3];



    enum cudaTextureFilterMode filterMode;



    enum cudaTextureReadMode readMode;



    int sRGB;



    float borderColor[4];



    int normalizedCoords;



    unsigned int maxAnisotropy;



    enum cudaTextureFilterMode mipmapFilterMode;



    float mipmapLevelBias;



    float minMipmapLevelClamp;



    float maxMipmapLevelClamp;
};




typedef __attribute__((device_builtin)) unsigned long long cudaTextureObject_t;
# 64 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 2
# 92 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/library_types.h" 1
# 54 "/usr/local/cuda/bin/../targets/x86_64-linux/include/library_types.h"
typedef enum cudaDataType_t
{
 CUDA_R_16F= 2,
 CUDA_C_16F= 6,
 CUDA_R_32F= 0,
 CUDA_C_32F= 4,
 CUDA_R_64F= 1,
 CUDA_C_64F= 5,
 CUDA_R_8I = 3,
 CUDA_C_8I = 7,
 CUDA_R_8U = 8,
 CUDA_C_8U = 9,
 CUDA_R_32I= 10,
 CUDA_C_32I= 11,
 CUDA_R_32U= 12,
 CUDA_C_32U= 13
} cudaDataType;


typedef enum libraryPropertyType_t
{
 MAJOR_VERSION,
 MINOR_VERSION,
 PATCH_LEVEL
} libraryPropertyType;
# 93 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2


# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/channel_descriptor.h" 1
# 61 "/usr/local/cuda/bin/../targets/x86_64-linux/include/channel_descriptor.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h" 1
# 138 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 139 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 140 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h" 2

# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_device_runtime_api.h" 1
# 142 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h" 2
# 245 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern "C" {
# 280 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceReset(void);
# 301 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaDeviceSynchronize(void);
# 386 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceSetLimit(enum cudaLimit limit, size_t value);
# 420 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaDeviceGetLimit(size_t *pValue, enum cudaLimit limit);
# 453 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaDeviceGetCacheConfig(enum cudaFuncCache *pCacheConfig);
# 490 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaDeviceGetStreamPriorityRange(int *leastPriority, int *greatestPriority);
# 534 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceSetCacheConfig(enum cudaFuncCache cacheConfig);
# 565 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaDeviceGetSharedMemConfig(enum cudaSharedMemConfig *pConfig);
# 609 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceSetSharedMemConfig(enum cudaSharedMemConfig config);
# 636 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceGetByPCIBusId(int *device, const char *pciBusId);
# 666 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceGetPCIBusId(char *pciBusId, int len, int device);
# 713 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaIpcGetEventHandle(cudaIpcEventHandle_t *handle, cudaEvent_t event);
# 753 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaIpcOpenEventHandle(cudaEvent_t *event, cudaIpcEventHandle_t handle);
# 796 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaIpcGetMemHandle(cudaIpcMemHandle_t *handle, void *devPtr);
# 854 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaIpcOpenMemHandle(void **devPtr, cudaIpcMemHandle_t handle, unsigned int flags);
# 889 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaIpcCloseMemHandle(void *devPtr);
# 931 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaThreadExit(void);
# 957 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaThreadSynchronize(void);
# 1006 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaThreadSetLimit(enum cudaLimit limit, size_t value);
# 1039 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaThreadGetLimit(size_t *pValue, enum cudaLimit limit);
# 1075 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaThreadGetCacheConfig(enum cudaFuncCache *pCacheConfig);
# 1122 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaThreadSetCacheConfig(enum cudaFuncCache cacheConfig);
# 1181 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaGetLastError(void);
# 1227 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaPeekAtLastError(void);
# 1243 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) const char* cudaGetErrorName(cudaError_t error);
# 1259 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) const char* cudaGetErrorString(cudaError_t error);
# 1288 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaGetDeviceCount(int *count);
# 1559 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaGetDeviceProperties(struct cudaDeviceProp *prop, int device);
# 1748 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaDeviceGetAttribute(int *value, enum cudaDeviceAttr attr, int device);
# 1797 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceGetNvSciSyncAttributes(void *nvSciSyncAttrList, int device, int flags);
# 1837 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaDeviceGetP2PAttribute(int *value, enum cudaDeviceP2PAttr attr, int srcDevice, int dstDevice);
# 1858 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaChooseDevice(int *device, const struct cudaDeviceProp *prop);
# 1895 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaSetDevice(int device);
# 1916 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaGetDevice(int *device);
# 1947 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaSetValidDevices(int *device_arr, int len);
# 2016 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaSetDeviceFlags( unsigned int flags );
# 2062 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetDeviceFlags( unsigned int *flags );
# 2102 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaStreamCreate(cudaStream_t *pStream);
# 2134 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaStreamCreateWithFlags(cudaStream_t *pStream, unsigned int flags);
# 2180 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaStreamCreateWithPriority(cudaStream_t *pStream, unsigned int flags, int priority);
# 2207 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaStreamGetPriority(cudaStream_t hStream, int *priority);
# 2232 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaStreamGetFlags(cudaStream_t hStream, unsigned int *flags);
# 2263 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaStreamDestroy(cudaStream_t stream);
# 2289 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaStreamWaitEvent(cudaStream_t stream, cudaEvent_t event, unsigned int flags);







typedef void ( *cudaStreamCallback_t)(cudaStream_t stream, cudaError_t status, void *userData);
# 2364 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaStreamAddCallback(cudaStream_t stream,
        cudaStreamCallback_t callback, void *userData, unsigned int flags);
# 2388 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaStreamSynchronize(cudaStream_t stream);
# 2413 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaStreamQuery(cudaStream_t stream);
# 2496 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaStreamAttachMemAsync(cudaStream_t stream, void *devPtr, size_t length = 0, unsigned int flags = 0x04);
# 2532 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaStreamBeginCapture(cudaStream_t stream, enum cudaStreamCaptureMode mode);
# 2583 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaThreadExchangeStreamCaptureMode(enum cudaStreamCaptureMode *mode);
# 2611 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaStreamEndCapture(cudaStream_t stream, cudaGraph_t *pGraph);
# 2649 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaStreamIsCapturing(cudaStream_t stream, enum cudaStreamCaptureStatus *pCaptureStatus);
# 2677 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaStreamGetCaptureInfo(cudaStream_t stream, enum cudaStreamCaptureStatus *pCaptureStatus, unsigned long long *pId);
# 2714 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaEventCreate(cudaEvent_t *event);
# 2751 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaEventCreateWithFlags(cudaEvent_t *event, unsigned int flags);
# 2790 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaEventRecord(cudaEvent_t event, cudaStream_t stream = 0);
# 2821 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaEventQuery(cudaEvent_t event);
# 2851 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaEventSynchronize(cudaEvent_t event);
# 2878 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaEventDestroy(cudaEvent_t event);
# 2921 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaEventElapsedTime(float *ms, cudaEvent_t start, cudaEvent_t end);
# 3098 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaImportExternalMemory(cudaExternalMemory_t *extMem_out, const struct cudaExternalMemoryHandleDesc *memHandleDesc);
# 3152 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaExternalMemoryGetMappedBuffer(void **devPtr, cudaExternalMemory_t extMem, const struct cudaExternalMemoryBufferDesc *bufferDesc);
# 3211 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaExternalMemoryGetMappedMipmappedArray(cudaMipmappedArray_t *mipmap, cudaExternalMemory_t extMem, const struct cudaExternalMemoryMipmappedArrayDesc *mipmapDesc);
# 3234 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDestroyExternalMemory(cudaExternalMemory_t extMem);
# 3365 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaImportExternalSemaphore(cudaExternalSemaphore_t *extSem_out, const struct cudaExternalSemaphoreHandleDesc *semHandleDesc);
# 3430 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaSignalExternalSemaphoresAsync(const cudaExternalSemaphore_t *extSemArray, const struct cudaExternalSemaphoreSignalParams *paramsArray, unsigned int numExtSems, cudaStream_t stream = 0);
# 3504 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaWaitExternalSemaphoresAsync(const cudaExternalSemaphore_t *extSemArray, const struct cudaExternalSemaphoreWaitParams *paramsArray, unsigned int numExtSems, cudaStream_t stream = 0);
# 3526 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDestroyExternalSemaphore(cudaExternalSemaphore_t extSem);
# 3591 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaLaunchKernel(const void *func, dim3 gridDim, dim3 blockDim, void **args, size_t sharedMem, cudaStream_t stream);
# 3648 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaLaunchCooperativeKernel(const void *func, dim3 gridDim, dim3 blockDim, void **args, size_t sharedMem, cudaStream_t stream);
# 3747 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaLaunchCooperativeKernelMultiDevice(struct cudaLaunchParams *launchParamsList, unsigned int numDevices, unsigned int flags = 0);
# 3796 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaFuncSetCacheConfig(const void *func, enum cudaFuncCache cacheConfig);
# 3851 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaFuncSetSharedMemConfig(const void *func, enum cudaSharedMemConfig config);
# 3886 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaFuncGetAttributes(struct cudaFuncAttributes *attr, const void *func);
# 3925 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaFuncSetAttribute(const void *func, enum cudaFuncAttribute attr, int value);
# 3949 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaSetDoubleForDevice(double *d);
# 3973 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaSetDoubleForHost(double *d);
# 4039 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaLaunchHostFunc(cudaStream_t stream, cudaHostFn_t fn, void *userData);
# 4094 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaOccupancyMaxActiveBlocksPerMultiprocessor(int *numBlocks, const void *func, int blockSize, size_t dynamicSMemSize);
# 4138 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaOccupancyMaxActiveBlocksPerMultiprocessorWithFlags(int *numBlocks, const void *func, int blockSize, size_t dynamicSMemSize, unsigned int flags);
# 4258 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaMallocManaged(void **devPtr, size_t size, unsigned int flags = 0x01);
# 4289 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaMalloc(void **devPtr, size_t size);
# 4322 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMallocHost(void **ptr, size_t size);
# 4365 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMallocPitch(void **devPtr, size_t *pitch, size_t width, size_t height);
# 4411 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMallocArray(cudaArray_t *array, const struct cudaChannelFormatDesc *desc, size_t width, size_t height = 0, unsigned int flags = 0);
# 4440 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaFree(void *devPtr);
# 4463 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaFreeHost(void *ptr);
# 4486 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaFreeArray(cudaArray_t array);
# 4509 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaFreeMipmappedArray(cudaMipmappedArray_t mipmappedArray);
# 4575 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaHostAlloc(void **pHost, size_t size, unsigned int flags);
# 4659 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaHostRegister(void *ptr, size_t size, unsigned int flags);
# 4682 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaHostUnregister(void *ptr);
# 4727 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaHostGetDevicePointer(void **pDevice, void *pHost, unsigned int flags);
# 4749 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaHostGetFlags(unsigned int *pFlags, void *pHost);
# 4788 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMalloc3D(struct cudaPitchedPtr* pitchedDevPtr, struct cudaExtent extent);
# 4927 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMalloc3DArray(cudaArray_t *array, const struct cudaChannelFormatDesc* desc, struct cudaExtent extent, unsigned int flags = 0);
# 5066 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMallocMipmappedArray(cudaMipmappedArray_t *mipmappedArray, const struct cudaChannelFormatDesc* desc, struct cudaExtent extent, unsigned int numLevels, unsigned int flags = 0);
# 5095 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetMipmappedArrayLevel(cudaArray_t *levelArray, cudaMipmappedArray_const_t mipmappedArray, unsigned int level);
# 5200 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy3D(const struct cudaMemcpy3DParms *p);
# 5231 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy3DPeer(const struct cudaMemcpy3DPeerParms *p);
# 5349 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaMemcpy3DAsync(const struct cudaMemcpy3DParms *p, cudaStream_t stream = 0);
# 5375 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy3DPeerAsync(const struct cudaMemcpy3DPeerParms *p, cudaStream_t stream = 0);
# 5397 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemGetInfo(size_t *free, size_t *total);
# 5423 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaArrayGetInfo(struct cudaChannelFormatDesc *desc, struct cudaExtent *extent, unsigned int *flags, cudaArray_t array);
# 5466 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy(void *dst, const void *src, size_t count, enum cudaMemcpyKind kind);
# 5501 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpyPeer(void *dst, int dstDevice, const void *src, int srcDevice, size_t count);
# 5549 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy2D(void *dst, size_t dpitch, const void *src, size_t spitch, size_t width, size_t height, enum cudaMemcpyKind kind);
# 5598 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy2DToArray(cudaArray_t dst, size_t wOffset, size_t hOffset, const void *src, size_t spitch, size_t width, size_t height, enum cudaMemcpyKind kind);
# 5647 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy2DFromArray(void *dst, size_t dpitch, cudaArray_const_t src, size_t wOffset, size_t hOffset, size_t width, size_t height, enum cudaMemcpyKind kind);
# 5694 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy2DArrayToArray(cudaArray_t dst, size_t wOffsetDst, size_t hOffsetDst, cudaArray_const_t src, size_t wOffsetSrc, size_t hOffsetSrc, size_t width, size_t height, enum cudaMemcpyKind kind = cudaMemcpyDeviceToDevice);
# 5737 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpyToSymbol(const void *symbol, const void *src, size_t count, size_t offset = 0, enum cudaMemcpyKind kind = cudaMemcpyHostToDevice);
# 5780 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpyFromSymbol(void *dst, const void *symbol, size_t count, size_t offset = 0, enum cudaMemcpyKind kind = cudaMemcpyDeviceToHost);
# 5836 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaMemcpyAsync(void *dst, const void *src, size_t count, enum cudaMemcpyKind kind, cudaStream_t stream = 0);
# 5871 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpyPeerAsync(void *dst, int dstDevice, const void *src, int srcDevice, size_t count, cudaStream_t stream = 0);
# 5933 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaMemcpy2DAsync(void *dst, size_t dpitch, const void *src, size_t spitch, size_t width, size_t height, enum cudaMemcpyKind kind, cudaStream_t stream = 0);
# 5990 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy2DToArrayAsync(cudaArray_t dst, size_t wOffset, size_t hOffset, const void *src, size_t spitch, size_t width, size_t height, enum cudaMemcpyKind kind, cudaStream_t stream = 0);
# 6046 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpy2DFromArrayAsync(void *dst, size_t dpitch, cudaArray_const_t src, size_t wOffset, size_t hOffset, size_t width, size_t height, enum cudaMemcpyKind kind, cudaStream_t stream = 0);
# 6097 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpyToSymbolAsync(const void *symbol, const void *src, size_t count, size_t offset, enum cudaMemcpyKind kind, cudaStream_t stream = 0);
# 6148 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemcpyFromSymbolAsync(void *dst, const void *symbol, size_t count, size_t offset, enum cudaMemcpyKind kind, cudaStream_t stream = 0);
# 6177 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemset(void *devPtr, int value, size_t count);
# 6211 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemset2D(void *devPtr, size_t pitch, int value, size_t width, size_t height);
# 6255 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemset3D(struct cudaPitchedPtr pitchedDevPtr, int value, struct cudaExtent extent);
# 6291 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaMemsetAsync(void *devPtr, int value, size_t count, cudaStream_t stream = 0);
# 6332 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaMemset2DAsync(void *devPtr, size_t pitch, int value, size_t width, size_t height, cudaStream_t stream = 0);
# 6383 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaMemset3DAsync(struct cudaPitchedPtr pitchedDevPtr, int value, struct cudaExtent extent, cudaStream_t stream = 0);
# 6411 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetSymbolAddress(void **devPtr, const void *symbol);
# 6438 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetSymbolSize(size_t *size, const void *symbol);
# 6508 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemPrefetchAsync(const void *devPtr, size_t count, int dstDevice, cudaStream_t stream = 0);
# 6624 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemAdvise(const void *devPtr, size_t count, enum cudaMemoryAdvise advice, int device);
# 6683 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemRangeGetAttribute(void *data, size_t dataSize, enum cudaMemRangeAttribute attribute, const void *devPtr, size_t count);
# 6722 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaMemRangeGetAttributes(void **data, size_t *dataSizes, enum cudaMemRangeAttribute *attributes, size_t numAttributes, const void *devPtr, size_t count);
# 6782 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaMemcpyToArray(cudaArray_t dst, size_t wOffset, size_t hOffset, const void *src, size_t count, enum cudaMemcpyKind kind);
# 6824 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaMemcpyFromArray(void *dst, cudaArray_const_t src, size_t wOffset, size_t hOffset, size_t count, enum cudaMemcpyKind kind);
# 6867 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaMemcpyArrayToArray(cudaArray_t dst, size_t wOffsetDst, size_t hOffsetDst, cudaArray_const_t src, size_t wOffsetSrc, size_t hOffsetSrc, size_t count, enum cudaMemcpyKind kind = cudaMemcpyDeviceToDevice);
# 6918 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaMemcpyToArrayAsync(cudaArray_t dst, size_t wOffset, size_t hOffset, const void *src, size_t count, enum cudaMemcpyKind kind, cudaStream_t stream = 0);
# 6968 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((deprecated)) __attribute__((host)) cudaError_t cudaMemcpyFromArrayAsync(void *dst, cudaArray_const_t src, size_t wOffset, size_t hOffset, size_t count, enum cudaMemcpyKind kind, cudaStream_t stream = 0);
# 7134 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaPointerGetAttributes(struct cudaPointerAttributes *attributes, const void *ptr);
# 7175 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceCanAccessPeer(int *canAccessPeer, int device, int peerDevice);
# 7217 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceEnablePeerAccess(int peerDevice, unsigned int flags);
# 7239 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDeviceDisablePeerAccess(int peerDevice);
# 7302 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphicsUnregisterResource(cudaGraphicsResource_t resource);
# 7337 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphicsResourceSetMapFlags(cudaGraphicsResource_t resource, unsigned int flags);
# 7376 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphicsMapResources(int count, cudaGraphicsResource_t *resources, cudaStream_t stream = 0);
# 7411 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphicsUnmapResources(int count, cudaGraphicsResource_t *resources, cudaStream_t stream = 0);
# 7443 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphicsResourceGetMappedPointer(void **devPtr, size_t *size, cudaGraphicsResource_t resource);
# 7481 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphicsSubResourceGetMappedArray(cudaArray_t *array, cudaGraphicsResource_t resource, unsigned int arrayIndex, unsigned int mipLevel);
# 7510 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphicsResourceGetMappedMipmappedArray(cudaMipmappedArray_t *mipmappedArray, cudaGraphicsResource_t resource);
# 7581 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaBindTexture(size_t *offset, const struct textureReference *texref, const void *devPtr, const struct cudaChannelFormatDesc *desc, size_t size = (2147483647 * 2U + 1U));
# 7640 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaBindTexture2D(size_t *offset, const struct textureReference *texref, const void *devPtr, const struct cudaChannelFormatDesc *desc, size_t width, size_t height, size_t pitch);
# 7678 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaBindTextureToArray(const struct textureReference *texref, cudaArray_const_t array, const struct cudaChannelFormatDesc *desc);
# 7718 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaBindTextureToMipmappedArray(const struct textureReference *texref, cudaMipmappedArray_const_t mipmappedArray, const struct cudaChannelFormatDesc *desc);
# 7744 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaUnbindTexture(const struct textureReference *texref);
# 7773 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetTextureAlignmentOffset(size_t *offset, const struct textureReference *texref);
# 7803 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetTextureReference(const struct textureReference **texref, const void *symbol);
# 7848 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaBindSurfaceToArray(const struct surfaceReference *surfref, cudaArray_const_t array, const struct cudaChannelFormatDesc *desc);
# 7873 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetSurfaceReference(const struct surfaceReference **surfref, const void *symbol);
# 7908 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetChannelDesc(struct cudaChannelFormatDesc *desc, cudaArray_const_t array);
# 7938 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) struct cudaChannelFormatDesc cudaCreateChannelDesc(int x, int y, int z, int w, enum cudaChannelFormatKind f);
# 8153 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaCreateTextureObject(cudaTextureObject_t *pTexObject, const struct cudaResourceDesc *pResDesc, const struct cudaTextureDesc *pTexDesc, const struct cudaResourceViewDesc *pResViewDesc);
# 8172 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDestroyTextureObject(cudaTextureObject_t texObject);
# 8192 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetTextureObjectResourceDesc(struct cudaResourceDesc *pResDesc, cudaTextureObject_t texObject);
# 8212 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetTextureObjectTextureDesc(struct cudaTextureDesc *pTexDesc, cudaTextureObject_t texObject);
# 8233 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetTextureObjectResourceViewDesc(struct cudaResourceViewDesc *pResViewDesc, cudaTextureObject_t texObject);
# 8278 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaCreateSurfaceObject(cudaSurfaceObject_t *pSurfObject, const struct cudaResourceDesc *pResDesc);
# 8297 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDestroySurfaceObject(cudaSurfaceObject_t surfObject);
# 8316 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGetSurfaceObjectResourceDesc(struct cudaResourceDesc *pResDesc, cudaSurfaceObject_t surfObject);
# 8350 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaDriverGetVersion(int *driverVersion);
# 8375 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) __attribute__((cudart_builtin)) cudaError_t cudaRuntimeGetVersion(int *runtimeVersion);
# 8422 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphCreate(cudaGraph_t *pGraph, unsigned int flags);
# 8519 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphAddKernelNode(cudaGraphNode_t *pGraphNode, cudaGraph_t graph, const cudaGraphNode_t *pDependencies, size_t numDependencies, const struct cudaKernelNodeParams *pNodeParams);
# 8552 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphKernelNodeGetParams(cudaGraphNode_t node, struct cudaKernelNodeParams *pNodeParams);
# 8577 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphKernelNodeSetParams(cudaGraphNode_t node, const struct cudaKernelNodeParams *pNodeParams);
# 8621 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphAddMemcpyNode(cudaGraphNode_t *pGraphNode, cudaGraph_t graph, const cudaGraphNode_t *pDependencies, size_t numDependencies, const struct cudaMemcpy3DParms *pCopyParams);
# 8644 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphMemcpyNodeGetParams(cudaGraphNode_t node, struct cudaMemcpy3DParms *pNodeParams);
# 8667 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphMemcpyNodeSetParams(cudaGraphNode_t node, const struct cudaMemcpy3DParms *pNodeParams);
# 8709 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphAddMemsetNode(cudaGraphNode_t *pGraphNode, cudaGraph_t graph, const cudaGraphNode_t *pDependencies, size_t numDependencies, const struct cudaMemsetParams *pMemsetParams);
# 8732 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphMemsetNodeGetParams(cudaGraphNode_t node, struct cudaMemsetParams *pNodeParams);
# 8755 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphMemsetNodeSetParams(cudaGraphNode_t node, const struct cudaMemsetParams *pNodeParams);
# 8796 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphAddHostNode(cudaGraphNode_t *pGraphNode, cudaGraph_t graph, const cudaGraphNode_t *pDependencies, size_t numDependencies, const struct cudaHostNodeParams *pNodeParams);
# 8819 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphHostNodeGetParams(cudaGraphNode_t node, struct cudaHostNodeParams *pNodeParams);
# 8842 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphHostNodeSetParams(cudaGraphNode_t node, const struct cudaHostNodeParams *pNodeParams);
# 8880 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphAddChildGraphNode(cudaGraphNode_t *pGraphNode, cudaGraph_t graph, const cudaGraphNode_t *pDependencies, size_t numDependencies, cudaGraph_t childGraph);
# 8904 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphChildGraphNodeGetGraph(cudaGraphNode_t node, cudaGraph_t *pGraph);
# 8941 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphAddEmptyNode(cudaGraphNode_t *pGraphNode, cudaGraph_t graph, const cudaGraphNode_t *pDependencies, size_t numDependencies);
# 8968 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphClone(cudaGraph_t *pGraphClone, cudaGraph_t originalGraph);
# 8996 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphNodeFindInClone(cudaGraphNode_t *pNode, cudaGraphNode_t originalNode, cudaGraph_t clonedGraph);
# 9027 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphNodeGetType(cudaGraphNode_t node, enum cudaGraphNodeType *pType);
# 9058 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphGetNodes(cudaGraph_t graph, cudaGraphNode_t *nodes, size_t *numNodes);
# 9089 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphGetRootNodes(cudaGraph_t graph, cudaGraphNode_t *pRootNodes, size_t *pNumRootNodes);
# 9123 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphGetEdges(cudaGraph_t graph, cudaGraphNode_t *from, cudaGraphNode_t *to, size_t *numEdges);
# 9154 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphNodeGetDependencies(cudaGraphNode_t node, cudaGraphNode_t *pDependencies, size_t *pNumDependencies);
# 9186 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphNodeGetDependentNodes(cudaGraphNode_t node, cudaGraphNode_t *pDependentNodes, size_t *pNumDependentNodes);
# 9217 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphAddDependencies(cudaGraph_t graph, const cudaGraphNode_t *from, const cudaGraphNode_t *to, size_t numDependencies);
# 9248 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphRemoveDependencies(cudaGraph_t graph, const cudaGraphNode_t *from, const cudaGraphNode_t *to, size_t numDependencies);
# 9274 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphDestroyNode(cudaGraphNode_t node);
# 9310 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphInstantiate(cudaGraphExec_t *pGraphExec, cudaGraph_t graph, cudaGraphNode_t *pErrorNode, char *pLogBuffer, size_t bufferSize);
# 9344 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphExecKernelNodeSetParams(cudaGraphExec_t hGraphExec, cudaGraphNode_t node, const struct cudaKernelNodeParams *pNodeParams);
# 9385 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphExecMemcpyNodeSetParams(cudaGraphExec_t hGraphExec, cudaGraphNode_t node, const struct cudaMemcpy3DParms *pNodeParams);
# 9426 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphExecMemsetNodeSetParams(cudaGraphExec_t hGraphExec, cudaGraphNode_t node, const struct cudaMemsetParams *pNodeParams);
# 9459 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphExecHostNodeSetParams(cudaGraphExec_t hGraphExec, cudaGraphNode_t node, const struct cudaHostNodeParams *pNodeParams);
# 9534 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphExecUpdate(cudaGraphExec_t hGraphExec, cudaGraph_t hGraph, cudaGraphNode_t *hErrorNode_out, enum cudaGraphExecUpdateResult *updateResult_out);
# 9559 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphLaunch(cudaGraphExec_t graphExec, cudaStream_t stream);
# 9580 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphExecDestroy(cudaGraphExec_t graphExec);
# 9600 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
extern __attribute__((host)) cudaError_t cudaGraphDestroy(cudaGraph_t graph);




extern __attribute__((host)) cudaError_t cudaGetExportTable(const void **ppExportTable, const cudaUUID_t *pExportTableId);
# 9850 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime_api.h"
}
# 62 "/usr/local/cuda/bin/../targets/x86_64-linux/include/channel_descriptor.h" 2
# 104 "/usr/local/cuda/bin/../targets/x86_64-linux/include/channel_descriptor.h"
template<class T> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc(void)
{
  return cudaCreateChannelDesc(0, 0, 0, 0, cudaChannelFormatKindNone);
}

static __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDescHalf(void)
{
  int e = (int)sizeof(unsigned short) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindFloat);
}

static __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDescHalf1(void)
{
  int e = (int)sizeof(unsigned short) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindFloat);
}

static __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDescHalf2(void)
{
  int e = (int)sizeof(unsigned short) * 8;

  return cudaCreateChannelDesc(e, e, 0, 0, cudaChannelFormatKindFloat);
}

static __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDescHalf4(void)
{
  int e = (int)sizeof(unsigned short) * 8;

  return cudaCreateChannelDesc(e, e, e, e, cudaChannelFormatKindFloat);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<char>(void)
{
  int e = (int)sizeof(char) * 8;




  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindSigned);

}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<signed char>(void)
{
  int e = (int)sizeof(signed char) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<unsigned char>(void)
{
  int e = (int)sizeof(unsigned char) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<char1>(void)
{
  int e = (int)sizeof(signed char) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<uchar1>(void)
{
  int e = (int)sizeof(unsigned char) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<char2>(void)
{
  int e = (int)sizeof(signed char) * 8;

  return cudaCreateChannelDesc(e, e, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<uchar2>(void)
{
  int e = (int)sizeof(unsigned char) * 8;

  return cudaCreateChannelDesc(e, e, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<char4>(void)
{
  int e = (int)sizeof(signed char) * 8;

  return cudaCreateChannelDesc(e, e, e, e, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<uchar4>(void)
{
  int e = (int)sizeof(unsigned char) * 8;

  return cudaCreateChannelDesc(e, e, e, e, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<short>(void)
{
  int e = (int)sizeof(short) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<unsigned short>(void)
{
  int e = (int)sizeof(unsigned short) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<short1>(void)
{
  int e = (int)sizeof(short) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<ushort1>(void)
{
  int e = (int)sizeof(unsigned short) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<short2>(void)
{
  int e = (int)sizeof(short) * 8;

  return cudaCreateChannelDesc(e, e, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<ushort2>(void)
{
  int e = (int)sizeof(unsigned short) * 8;

  return cudaCreateChannelDesc(e, e, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<short4>(void)
{
  int e = (int)sizeof(short) * 8;

  return cudaCreateChannelDesc(e, e, e, e, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<ushort4>(void)
{
  int e = (int)sizeof(unsigned short) * 8;

  return cudaCreateChannelDesc(e, e, e, e, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<int>(void)
{
  int e = (int)sizeof(int) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<unsigned int>(void)
{
  int e = (int)sizeof(unsigned int) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<int1>(void)
{
  int e = (int)sizeof(int) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<uint1>(void)
{
  int e = (int)sizeof(unsigned int) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<int2>(void)
{
  int e = (int)sizeof(int) * 8;

  return cudaCreateChannelDesc(e, e, 0, 0, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<uint2>(void)
{
  int e = (int)sizeof(unsigned int) * 8;

  return cudaCreateChannelDesc(e, e, 0, 0, cudaChannelFormatKindUnsigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<int4>(void)
{
  int e = (int)sizeof(int) * 8;

  return cudaCreateChannelDesc(e, e, e, e, cudaChannelFormatKindSigned);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<uint4>(void)
{
  int e = (int)sizeof(unsigned int) * 8;

  return cudaCreateChannelDesc(e, e, e, e, cudaChannelFormatKindUnsigned);
}
# 376 "/usr/local/cuda/bin/../targets/x86_64-linux/include/channel_descriptor.h"
template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<float>(void)
{
  int e = (int)sizeof(float) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindFloat);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<float1>(void)
{
  int e = (int)sizeof(float) * 8;

  return cudaCreateChannelDesc(e, 0, 0, 0, cudaChannelFormatKindFloat);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<float2>(void)
{
  int e = (int)sizeof(float) * 8;

  return cudaCreateChannelDesc(e, e, 0, 0, cudaChannelFormatKindFloat);
}

template<> __inline__ __attribute__((host)) cudaChannelFormatDesc cudaCreateChannelDesc<float4>(void)
{
  int e = (int)sizeof(float) * 8;

  return cudaCreateChannelDesc(e, e, e, e, cudaChannelFormatKindFloat);
}
# 96 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2

# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_functions.h" 1
# 53 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 54 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 55 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_functions.h" 2
# 79 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_functions.h"
static __inline__ __attribute__((host)) struct cudaPitchedPtr make_cudaPitchedPtr(void *d, size_t p, size_t xsz, size_t ysz)
{
  struct cudaPitchedPtr s;

  s.ptr = d;
  s.pitch = p;
  s.xsize = xsz;
  s.ysize = ysz;

  return s;
}
# 106 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_functions.h"
static __inline__ __attribute__((host)) struct cudaPos make_cudaPos(size_t x, size_t y, size_t z)
{
  struct cudaPos p;

  p.x = x;
  p.y = y;
  p.z = z;

  return p;
}
# 132 "/usr/local/cuda/bin/../targets/x86_64-linux/include/driver_functions.h"
static __inline__ __attribute__((host)) struct cudaExtent make_cudaExtent(size_t w, size_t h, size_t d)
{
  struct cudaExtent e;

  e.width = w;
  e.height = h;
  e.depth = d;

  return e;
}
# 98 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2


# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 101 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_functions.h" 1
# 73 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_functions.h"
static __inline__ __attribute__((host)) __attribute__((device)) char1 make_char1(signed char x);

static __inline__ __attribute__((host)) __attribute__((device)) uchar1 make_uchar1(unsigned char x);

static __inline__ __attribute__((host)) __attribute__((device)) char2 make_char2(signed char x, signed char y);

static __inline__ __attribute__((host)) __attribute__((device)) uchar2 make_uchar2(unsigned char x, unsigned char y);

static __inline__ __attribute__((host)) __attribute__((device)) char3 make_char3(signed char x, signed char y, signed char z);

static __inline__ __attribute__((host)) __attribute__((device)) uchar3 make_uchar3(unsigned char x, unsigned char y, unsigned char z);

static __inline__ __attribute__((host)) __attribute__((device)) char4 make_char4(signed char x, signed char y, signed char z, signed char w);

static __inline__ __attribute__((host)) __attribute__((device)) uchar4 make_uchar4(unsigned char x, unsigned char y, unsigned char z, unsigned char w);

static __inline__ __attribute__((host)) __attribute__((device)) short1 make_short1(short x);

static __inline__ __attribute__((host)) __attribute__((device)) ushort1 make_ushort1(unsigned short x);

static __inline__ __attribute__((host)) __attribute__((device)) short2 make_short2(short x, short y);

static __inline__ __attribute__((host)) __attribute__((device)) ushort2 make_ushort2(unsigned short x, unsigned short y);

static __inline__ __attribute__((host)) __attribute__((device)) short3 make_short3(short x,short y, short z);

static __inline__ __attribute__((host)) __attribute__((device)) ushort3 make_ushort3(unsigned short x, unsigned short y, unsigned short z);

static __inline__ __attribute__((host)) __attribute__((device)) short4 make_short4(short x, short y, short z, short w);

static __inline__ __attribute__((host)) __attribute__((device)) ushort4 make_ushort4(unsigned short x, unsigned short y, unsigned short z, unsigned short w);

static __inline__ __attribute__((host)) __attribute__((device)) int1 make_int1(int x);

static __inline__ __attribute__((host)) __attribute__((device)) uint1 make_uint1(unsigned int x);

static __inline__ __attribute__((host)) __attribute__((device)) int2 make_int2(int x, int y);

static __inline__ __attribute__((host)) __attribute__((device)) uint2 make_uint2(unsigned int x, unsigned int y);

static __inline__ __attribute__((host)) __attribute__((device)) int3 make_int3(int x, int y, int z);

static __inline__ __attribute__((host)) __attribute__((device)) uint3 make_uint3(unsigned int x, unsigned int y, unsigned int z);

static __inline__ __attribute__((host)) __attribute__((device)) int4 make_int4(int x, int y, int z, int w);

static __inline__ __attribute__((host)) __attribute__((device)) uint4 make_uint4(unsigned int x, unsigned int y, unsigned int z, unsigned int w);

static __inline__ __attribute__((host)) __attribute__((device)) long1 make_long1(long int x);

static __inline__ __attribute__((host)) __attribute__((device)) ulong1 make_ulong1(unsigned long int x);

static __inline__ __attribute__((host)) __attribute__((device)) long2 make_long2(long int x, long int y);

static __inline__ __attribute__((host)) __attribute__((device)) ulong2 make_ulong2(unsigned long int x, unsigned long int y);

static __inline__ __attribute__((host)) __attribute__((device)) long3 make_long3(long int x, long int y, long int z);

static __inline__ __attribute__((host)) __attribute__((device)) ulong3 make_ulong3(unsigned long int x, unsigned long int y, unsigned long int z);

static __inline__ __attribute__((host)) __attribute__((device)) long4 make_long4(long int x, long int y, long int z, long int w);

static __inline__ __attribute__((host)) __attribute__((device)) ulong4 make_ulong4(unsigned long int x, unsigned long int y, unsigned long int z, unsigned long int w);

static __inline__ __attribute__((host)) __attribute__((device)) float1 make_float1(float x);

static __inline__ __attribute__((host)) __attribute__((device)) float2 make_float2(float x, float y);

static __inline__ __attribute__((host)) __attribute__((device)) float3 make_float3(float x, float y, float z);

static __inline__ __attribute__((host)) __attribute__((device)) float4 make_float4(float x, float y, float z, float w);

static __inline__ __attribute__((host)) __attribute__((device)) longlong1 make_longlong1(long long int x);

static __inline__ __attribute__((host)) __attribute__((device)) ulonglong1 make_ulonglong1(unsigned long long int x);

static __inline__ __attribute__((host)) __attribute__((device)) longlong2 make_longlong2(long long int x, long long int y);

static __inline__ __attribute__((host)) __attribute__((device)) ulonglong2 make_ulonglong2(unsigned long long int x, unsigned long long int y);

static __inline__ __attribute__((host)) __attribute__((device)) longlong3 make_longlong3(long long int x, long long int y, long long int z);

static __inline__ __attribute__((host)) __attribute__((device)) ulonglong3 make_ulonglong3(unsigned long long int x, unsigned long long int y, unsigned long long int z);

static __inline__ __attribute__((host)) __attribute__((device)) longlong4 make_longlong4(long long int x, long long int y, long long int z, long long int w);

static __inline__ __attribute__((host)) __attribute__((device)) ulonglong4 make_ulonglong4(unsigned long long int x, unsigned long long int y, unsigned long long int z, unsigned long long int w);

static __inline__ __attribute__((host)) __attribute__((device)) double1 make_double1(double x);

static __inline__ __attribute__((host)) __attribute__((device)) double2 make_double2(double x, double y);

static __inline__ __attribute__((host)) __attribute__((device)) double3 make_double3(double x, double y, double z);

static __inline__ __attribute__((host)) __attribute__((device)) double4 make_double4(double x, double y, double z, double w);




# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_functions.hpp" 1
# 73 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_functions.hpp"
static __inline__ __attribute__((host)) __attribute__((device)) char1 make_char1(signed char x)
{
  char1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) uchar1 make_uchar1(unsigned char x)
{
  uchar1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) char2 make_char2(signed char x, signed char y)
{
  char2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) uchar2 make_uchar2(unsigned char x, unsigned char y)
{
  uchar2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) char3 make_char3(signed char x, signed char y, signed char z)
{
  char3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) uchar3 make_uchar3(unsigned char x, unsigned char y, unsigned char z)
{
  uchar3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) char4 make_char4(signed char x, signed char y, signed char z, signed char w)
{
  char4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) uchar4 make_uchar4(unsigned char x, unsigned char y, unsigned char z, unsigned char w)
{
  uchar4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) short1 make_short1(short x)
{
  short1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ushort1 make_ushort1(unsigned short x)
{
  ushort1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) short2 make_short2(short x, short y)
{
  short2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ushort2 make_ushort2(unsigned short x, unsigned short y)
{
  ushort2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) short3 make_short3(short x,short y, short z)
{
  short3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ushort3 make_ushort3(unsigned short x, unsigned short y, unsigned short z)
{
  ushort3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) short4 make_short4(short x, short y, short z, short w)
{
  short4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ushort4 make_ushort4(unsigned short x, unsigned short y, unsigned short z, unsigned short w)
{
  ushort4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) int1 make_int1(int x)
{
  int1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) uint1 make_uint1(unsigned int x)
{
  uint1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) int2 make_int2(int x, int y)
{
  int2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) uint2 make_uint2(unsigned int x, unsigned int y)
{
  uint2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) int3 make_int3(int x, int y, int z)
{
  int3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) uint3 make_uint3(unsigned int x, unsigned int y, unsigned int z)
{
  uint3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) int4 make_int4(int x, int y, int z, int w)
{
  int4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) uint4 make_uint4(unsigned int x, unsigned int y, unsigned int z, unsigned int w)
{
  uint4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) long1 make_long1(long int x)
{
  long1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ulong1 make_ulong1(unsigned long int x)
{
  ulong1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) long2 make_long2(long int x, long int y)
{
  long2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ulong2 make_ulong2(unsigned long int x, unsigned long int y)
{
  ulong2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) long3 make_long3(long int x, long int y, long int z)
{
  long3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ulong3 make_ulong3(unsigned long int x, unsigned long int y, unsigned long int z)
{
  ulong3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) long4 make_long4(long int x, long int y, long int z, long int w)
{
  long4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ulong4 make_ulong4(unsigned long int x, unsigned long int y, unsigned long int z, unsigned long int w)
{
  ulong4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) float1 make_float1(float x)
{
  float1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) float2 make_float2(float x, float y)
{
  float2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) float3 make_float3(float x, float y, float z)
{
  float3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) float4 make_float4(float x, float y, float z, float w)
{
  float4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) longlong1 make_longlong1(long long int x)
{
  longlong1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ulonglong1 make_ulonglong1(unsigned long long int x)
{
  ulonglong1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) longlong2 make_longlong2(long long int x, long long int y)
{
  longlong2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ulonglong2 make_ulonglong2(unsigned long long int x, unsigned long long int y)
{
  ulonglong2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) longlong3 make_longlong3(long long int x, long long int y, long long int z)
{
  longlong3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ulonglong3 make_ulonglong3(unsigned long long int x, unsigned long long int y, unsigned long long int z)
{
  ulonglong3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) longlong4 make_longlong4(long long int x, long long int y, long long int z, long long int w)
{
  longlong4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) ulonglong4 make_ulonglong4(unsigned long long int x, unsigned long long int y, unsigned long long int z, unsigned long long int w)
{
  ulonglong4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) double1 make_double1(double x)
{
  double1 t; t.x = x; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) double2 make_double2(double x, double y)
{
  double2 t; t.x = x; t.y = y; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) double3 make_double3(double x, double y, double z)
{
  double3 t; t.x = x; t.y = y; t.z = z; return t;
}

static __inline__ __attribute__((host)) __attribute__((device)) double4 make_double4(double x, double y, double z, double w)
{
  double4 t; t.x = x; t.y = y; t.z = z; t.w = w; return t;
}
# 173 "/usr/local/cuda/bin/../targets/x86_64-linux/include/vector_functions.h" 2
# 102 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2
# 115 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 1
# 71 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 72 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 73 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2




# 1 "/usr/include/string.h" 1 3 4
# 27 "/usr/include/string.h" 3 4
extern "C" {




# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 1 3 4
# 33 "/usr/include/string.h" 2 3 4









extern void *memcpy (void *__restrict __dest, const void *__restrict __src,
       size_t __n) throw () __attribute__ ((__nonnull__ (1, 2)));


extern void *memmove (void *__dest, const void *__src, size_t __n)
     throw () __attribute__ ((__nonnull__ (1, 2)));






extern void *memccpy (void *__restrict __dest, const void *__restrict __src,
        int __c, size_t __n)
     throw () __attribute__ ((__nonnull__ (1, 2)));





extern void *memset (void *__s, int __c, size_t __n) throw () __attribute__ ((__nonnull__ (1)));


extern int memcmp (const void *__s1, const void *__s2, size_t __n)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));



extern "C++"
{
extern void *memchr (void *__s, int __c, size_t __n)
      throw () __asm ("memchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
extern const void *memchr (const void *__s, int __c, size_t __n)
      throw () __asm ("memchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
# 90 "/usr/include/string.h" 3 4
}










extern "C++" void *rawmemchr (void *__s, int __c)
     throw () __asm ("rawmemchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
extern "C++" const void *rawmemchr (const void *__s, int __c)
     throw () __asm ("rawmemchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));







extern "C++" void *memrchr (void *__s, int __c, size_t __n)
      throw () __asm ("memrchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
extern "C++" const void *memrchr (const void *__s, int __c, size_t __n)
      throw () __asm ("memrchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));









extern char *strcpy (char *__restrict __dest, const char *__restrict __src)
     throw () __attribute__ ((__nonnull__ (1, 2)));

extern char *strncpy (char *__restrict __dest,
        const char *__restrict __src, size_t __n)
     throw () __attribute__ ((__nonnull__ (1, 2)));


extern char *strcat (char *__restrict __dest, const char *__restrict __src)
     throw () __attribute__ ((__nonnull__ (1, 2)));

extern char *strncat (char *__restrict __dest, const char *__restrict __src,
        size_t __n) throw () __attribute__ ((__nonnull__ (1, 2)));


extern int strcmp (const char *__s1, const char *__s2)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));

extern int strncmp (const char *__s1, const char *__s2, size_t __n)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));


extern int strcoll (const char *__s1, const char *__s2)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));

extern size_t strxfrm (char *__restrict __dest,
         const char *__restrict __src, size_t __n)
     throw () __attribute__ ((__nonnull__ (2)));






# 1 "/usr/include/xlocale.h" 1 3 4
# 27 "/usr/include/xlocale.h" 3 4
typedef struct __locale_struct
{

  struct __locale_data *__locales[13];


  const unsigned short int *__ctype_b;
  const int *__ctype_tolower;
  const int *__ctype_toupper;


  const char *__names[13];
} *__locale_t;


typedef __locale_t locale_t;
# 160 "/usr/include/string.h" 2 3 4


extern int strcoll_l (const char *__s1, const char *__s2, __locale_t __l)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2, 3)));

extern size_t strxfrm_l (char *__dest, const char *__src, size_t __n,
    __locale_t __l) throw () __attribute__ ((__nonnull__ (2, 4)));





extern char *strdup (const char *__s)
     throw () __attribute__ ((__malloc__)) __attribute__ ((__nonnull__ (1)));






extern char *strndup (const char *__string, size_t __n)
     throw () __attribute__ ((__malloc__)) __attribute__ ((__nonnull__ (1)));
# 207 "/usr/include/string.h" 3 4



extern "C++"
{
extern char *strchr (char *__s, int __c)
     throw () __asm ("strchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
extern const char *strchr (const char *__s, int __c)
     throw () __asm ("strchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
# 230 "/usr/include/string.h" 3 4
}






extern "C++"
{
extern char *strrchr (char *__s, int __c)
     throw () __asm ("strrchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
extern const char *strrchr (const char *__s, int __c)
     throw () __asm ("strrchr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
# 257 "/usr/include/string.h" 3 4
}










extern "C++" char *strchrnul (char *__s, int __c)
     throw () __asm ("strchrnul") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
extern "C++" const char *strchrnul (const char *__s, int __c)
     throw () __asm ("strchrnul") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));









extern size_t strcspn (const char *__s, const char *__reject)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));


extern size_t strspn (const char *__s, const char *__accept)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));


extern "C++"
{
extern char *strpbrk (char *__s, const char *__accept)
     throw () __asm ("strpbrk") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));
extern const char *strpbrk (const char *__s, const char *__accept)
     throw () __asm ("strpbrk") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));
# 309 "/usr/include/string.h" 3 4
}






extern "C++"
{
extern char *strstr (char *__haystack, const char *__needle)
     throw () __asm ("strstr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));
extern const char *strstr (const char *__haystack, const char *__needle)
     throw () __asm ("strstr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));
# 336 "/usr/include/string.h" 3 4
}







extern char *strtok (char *__restrict __s, const char *__restrict __delim)
     throw () __attribute__ ((__nonnull__ (2)));




extern char *__strtok_r (char *__restrict __s,
    const char *__restrict __delim,
    char **__restrict __save_ptr)
     throw () __attribute__ ((__nonnull__ (2, 3)));

extern char *strtok_r (char *__restrict __s, const char *__restrict __delim,
         char **__restrict __save_ptr)
     throw () __attribute__ ((__nonnull__ (2, 3)));





extern "C++" char *strcasestr (char *__haystack, const char *__needle)
     throw () __asm ("strcasestr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));
extern "C++" const char *strcasestr (const char *__haystack,
         const char *__needle)
     throw () __asm ("strcasestr") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));
# 378 "/usr/include/string.h" 3 4
extern void *memmem (const void *__haystack, size_t __haystacklen,
       const void *__needle, size_t __needlelen)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 3)));



extern void *__mempcpy (void *__restrict __dest,
   const void *__restrict __src, size_t __n)
     throw () __attribute__ ((__nonnull__ (1, 2)));
extern void *mempcpy (void *__restrict __dest,
        const void *__restrict __src, size_t __n)
     throw () __attribute__ ((__nonnull__ (1, 2)));





extern size_t strlen (const char *__s)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));





extern size_t strnlen (const char *__string, size_t __maxlen)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));





extern char *strerror (int __errnum) throw ();

# 434 "/usr/include/string.h" 3 4
extern char *strerror_r (int __errnum, char *__buf, size_t __buflen)
     throw () __attribute__ ((__nonnull__ (2))) ;





extern char *strerror_l (int __errnum, __locale_t __l) throw ();





extern void __bzero (void *__s, size_t __n) throw () __attribute__ ((__nonnull__ (1)));



extern void bcopy (const void *__src, void *__dest, size_t __n)
     throw () __attribute__ ((__nonnull__ (1, 2)));


extern void bzero (void *__s, size_t __n) throw () __attribute__ ((__nonnull__ (1)));


extern int bcmp (const void *__s1, const void *__s2, size_t __n)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));



extern "C++"
{
extern char *index (char *__s, int __c)
     throw () __asm ("index") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
extern const char *index (const char *__s, int __c)
     throw () __asm ("index") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
# 483 "/usr/include/string.h" 3 4
}







extern "C++"
{
extern char *rindex (char *__s, int __c)
     throw () __asm ("rindex") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
extern const char *rindex (const char *__s, int __c)
     throw () __asm ("rindex") __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1)));
# 511 "/usr/include/string.h" 3 4
}







extern int ffs (int __i) throw () __attribute__ ((__const__));




extern int ffsl (long int __l) throw () __attribute__ ((__const__));

__extension__ extern int ffsll (long long int __ll)
     throw () __attribute__ ((__const__));




extern int strcasecmp (const char *__s1, const char *__s2)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));


extern int strncasecmp (const char *__s1, const char *__s2, size_t __n)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));





extern int strcasecmp_l (const char *__s1, const char *__s2,
    __locale_t __loc)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2, 3)));

extern int strncasecmp_l (const char *__s1, const char *__s2,
     size_t __n, __locale_t __loc)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2, 4)));





extern char *strsep (char **__restrict __stringp,
       const char *__restrict __delim)
     throw () __attribute__ ((__nonnull__ (1, 2)));




extern char *strsignal (int __sig) throw ();


extern char *__stpcpy (char *__restrict __dest, const char *__restrict __src)
     throw () __attribute__ ((__nonnull__ (1, 2)));
extern char *stpcpy (char *__restrict __dest, const char *__restrict __src)
     throw () __attribute__ ((__nonnull__ (1, 2)));



extern char *__stpncpy (char *__restrict __dest,
   const char *__restrict __src, size_t __n)
     throw () __attribute__ ((__nonnull__ (1, 2)));
extern char *stpncpy (char *__restrict __dest,
        const char *__restrict __src, size_t __n)
     throw () __attribute__ ((__nonnull__ (1, 2)));




extern int strverscmp (const char *__s1, const char *__s2)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1, 2)));


extern char *strfry (char *__string) throw () __attribute__ ((__nonnull__ (1)));


extern void *memfrob (void *__s, size_t __n) throw () __attribute__ ((__nonnull__ (1)));







extern "C++" char *basename (char *__filename)
     throw () __asm ("basename") __attribute__ ((__nonnull__ (1)));
extern "C++" const char *basename (const char *__filename)
     throw () __asm ("basename") __attribute__ ((__nonnull__ (1)));
# 642 "/usr/include/string.h" 3 4
}
# 78 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2
# 1 "/usr/include/time.h" 1 3 4
# 29 "/usr/include/time.h" 3 4
extern "C" {







# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 1 3 4
# 38 "/usr/include/time.h" 2 3 4



# 1 "/usr/include/bits/time.h" 1 3 4
# 26 "/usr/include/bits/time.h" 3 4
# 1 "/usr/include/bits/types.h" 1 3 4
# 27 "/usr/include/bits/types.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 28 "/usr/include/bits/types.h" 2 3 4


typedef unsigned char __u_char;
typedef unsigned short int __u_short;
typedef unsigned int __u_int;
typedef unsigned long int __u_long;


typedef signed char __int8_t;
typedef unsigned char __uint8_t;
typedef signed short int __int16_t;
typedef unsigned short int __uint16_t;
typedef signed int __int32_t;
typedef unsigned int __uint32_t;

typedef signed long int __int64_t;
typedef unsigned long int __uint64_t;







typedef long int __quad_t;
typedef unsigned long int __u_quad_t;
# 130 "/usr/include/bits/types.h" 3 4
# 1 "/usr/include/bits/typesizes.h" 1 3 4
# 131 "/usr/include/bits/types.h" 2 3 4


typedef unsigned long int __dev_t;
typedef unsigned int __uid_t;
typedef unsigned int __gid_t;
typedef unsigned long int __ino_t;
typedef unsigned long int __ino64_t;
typedef unsigned int __mode_t;
typedef unsigned long int __nlink_t;
typedef long int __off_t;
typedef long int __off64_t;
typedef int __pid_t;
typedef struct { int __val[2]; } __fsid_t;
typedef long int __clock_t;
typedef unsigned long int __rlim_t;
typedef unsigned long int __rlim64_t;
typedef unsigned int __id_t;
typedef long int __time_t;
typedef unsigned int __useconds_t;
typedef long int __suseconds_t;

typedef int __daddr_t;
typedef int __key_t;


typedef int __clockid_t;


typedef void * __timer_t;


typedef long int __blksize_t;




typedef long int __blkcnt_t;
typedef long int __blkcnt64_t;


typedef unsigned long int __fsblkcnt_t;
typedef unsigned long int __fsblkcnt64_t;


typedef unsigned long int __fsfilcnt_t;
typedef unsigned long int __fsfilcnt64_t;


typedef long int __fsword_t;

typedef long int __ssize_t;


typedef long int __syscall_slong_t;

typedef unsigned long int __syscall_ulong_t;



typedef __off64_t __loff_t;
typedef __quad_t *__qaddr_t;
typedef char *__caddr_t;


typedef long int __intptr_t;


typedef unsigned int __socklen_t;
# 27 "/usr/include/bits/time.h" 2 3 4



struct timeval
  {
    __time_t tv_sec;
    __suseconds_t tv_usec;
  };
# 88 "/usr/include/bits/time.h" 3 4
# 1 "/usr/include/bits/timex.h" 1 3 4
# 25 "/usr/include/bits/timex.h" 3 4
struct timex
{
  unsigned int modes;
  __syscall_slong_t offset;
  __syscall_slong_t freq;
  __syscall_slong_t maxerror;
  __syscall_slong_t esterror;
  int status;
  __syscall_slong_t constant;
  __syscall_slong_t precision;
  __syscall_slong_t tolerance;
  struct timeval time;
  __syscall_slong_t tick;
  __syscall_slong_t ppsfreq;
  __syscall_slong_t jitter;
  int shift;
  __syscall_slong_t stabil;
  __syscall_slong_t jitcnt;
  __syscall_slong_t calcnt;
  __syscall_slong_t errcnt;
  __syscall_slong_t stbcnt;

  int tai;


  int :32; int :32; int :32; int :32;
  int :32; int :32; int :32; int :32;
  int :32; int :32; int :32;
};
# 89 "/usr/include/bits/time.h" 2 3 4

extern "C" {


extern int clock_adjtime (__clockid_t __clock_id, struct timex *__utx) throw ();

}
# 42 "/usr/include/time.h" 2 3 4
# 57 "/usr/include/time.h" 3 4


typedef __clock_t clock_t;



# 73 "/usr/include/time.h" 3 4


typedef __time_t time_t;



# 91 "/usr/include/time.h" 3 4
typedef __clockid_t clockid_t;
# 103 "/usr/include/time.h" 3 4
typedef __timer_t timer_t;
# 120 "/usr/include/time.h" 3 4
struct timespec
  {
    __time_t tv_sec;
    __syscall_slong_t tv_nsec;
  };








struct tm
{
  int tm_sec;
  int tm_min;
  int tm_hour;
  int tm_mday;
  int tm_mon;
  int tm_year;
  int tm_wday;
  int tm_yday;
  int tm_isdst;


  long int tm_gmtoff;
  const char *tm_zone;




};








struct itimerspec
  {
    struct timespec it_interval;
    struct timespec it_value;
  };


struct sigevent;





typedef __pid_t pid_t;
# 186 "/usr/include/time.h" 3 4



extern clock_t clock (void) throw ();


extern time_t time (time_t *__timer) throw ();


extern double difftime (time_t __time1, time_t __time0)
     throw () __attribute__ ((__const__));


extern time_t mktime (struct tm *__tp) throw ();





extern size_t strftime (char *__restrict __s, size_t __maxsize,
   const char *__restrict __format,
   const struct tm *__restrict __tp) throw ();





extern char *strptime (const char *__restrict __s,
         const char *__restrict __fmt, struct tm *__tp)
     throw ();







extern size_t strftime_l (char *__restrict __s, size_t __maxsize,
     const char *__restrict __format,
     const struct tm *__restrict __tp,
     __locale_t __loc) throw ();



extern char *strptime_l (const char *__restrict __s,
    const char *__restrict __fmt, struct tm *__tp,
    __locale_t __loc) throw ();






extern struct tm *gmtime (const time_t *__timer) throw ();



extern struct tm *localtime (const time_t *__timer) throw ();





extern struct tm *gmtime_r (const time_t *__restrict __timer,
       struct tm *__restrict __tp) throw ();



extern struct tm *localtime_r (const time_t *__restrict __timer,
          struct tm *__restrict __tp) throw ();





extern char *asctime (const struct tm *__tp) throw ();


extern char *ctime (const time_t *__timer) throw ();







extern char *asctime_r (const struct tm *__restrict __tp,
   char *__restrict __buf) throw ();


extern char *ctime_r (const time_t *__restrict __timer,
        char *__restrict __buf) throw ();




extern char *__tzname[2];
extern int __daylight;
extern long int __timezone;




extern char *tzname[2];



extern void tzset (void) throw ();



extern int daylight;
extern long int timezone;





extern int stime (const time_t *__when) throw ();
# 319 "/usr/include/time.h" 3 4
extern time_t timegm (struct tm *__tp) throw ();


extern time_t timelocal (struct tm *__tp) throw ();


extern int dysize (int __year) throw () __attribute__ ((__const__));
# 334 "/usr/include/time.h" 3 4
extern int nanosleep (const struct timespec *__requested_time,
        struct timespec *__remaining);



extern int clock_getres (clockid_t __clock_id, struct timespec *__res) throw ();


extern int clock_gettime (clockid_t __clock_id, struct timespec *__tp) throw ();


extern int clock_settime (clockid_t __clock_id, const struct timespec *__tp)
     throw ();






extern int clock_nanosleep (clockid_t __clock_id, int __flags,
       const struct timespec *__req,
       struct timespec *__rem);


extern int clock_getcpuclockid (pid_t __pid, clockid_t *__clock_id) throw ();




extern int timer_create (clockid_t __clock_id,
    struct sigevent *__restrict __evp,
    timer_t *__restrict __timerid) throw ();


extern int timer_delete (timer_t __timerid) throw ();


extern int timer_settime (timer_t __timerid, int __flags,
     const struct itimerspec *__restrict __value,
     struct itimerspec *__restrict __ovalue) throw ();


extern int timer_gettime (timer_t __timerid, struct itimerspec *__value)
     throw ();


extern int timer_getoverrun (timer_t __timerid) throw ();





extern int timespec_get (struct timespec *__ts, int __base)
     throw () __attribute__ ((__nonnull__ (1)));
# 403 "/usr/include/time.h" 3 4
extern int getdate_err;
# 412 "/usr/include/time.h" 3 4
extern struct tm *getdate (const char *__string);
# 426 "/usr/include/time.h" 3 4
extern int getdate_r (const char *__restrict __string,
        struct tm *__restrict __resbufp);


}
# 79 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2

extern "C"
{

extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) clock_t clock(void)



throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) void* memset(void*, int, size_t) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) void* memcpy(void*, const void*, size_t) throw ();

}
# 103 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h"
# 1 "/usr/include/c++/4.8.2/new" 1 3
# 37 "/usr/include/c++/4.8.2/new" 3
       
# 38 "/usr/include/c++/4.8.2/new" 3

# 1 "/usr/include/c++/4.8.2/x86_64-redhat-linux/bits/c++config.h" 1 3


# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 4 "/usr/include/c++/4.8.2/x86_64-redhat-linux/bits/c++config.h" 2 3
# 1855 "/usr/include/c++/4.8.2/x86_64-redhat-linux/bits/c++config.h" 3
namespace std
{
  typedef long unsigned int size_t;
  typedef long int ptrdiff_t;




}
# 2097 "/usr/include/c++/4.8.2/x86_64-redhat-linux/bits/c++config.h" 3
# 1 "/usr/include/c++/4.8.2/x86_64-redhat-linux/bits/os_defines.h" 1 3
# 2098 "/usr/include/c++/4.8.2/x86_64-redhat-linux/bits/c++config.h" 2 3


# 1 "/usr/include/c++/4.8.2/x86_64-redhat-linux/bits/cpu_defines.h" 1 3
# 2101 "/usr/include/c++/4.8.2/x86_64-redhat-linux/bits/c++config.h" 2 3
# 40 "/usr/include/c++/4.8.2/new" 2 3
# 1 "/usr/include/c++/4.8.2/exception" 1 3
# 33 "/usr/include/c++/4.8.2/exception" 3
       
# 34 "/usr/include/c++/4.8.2/exception" 3

#pragma GCC visibility push(default)


# 1 "/usr/include/c++/4.8.2/bits/atomic_lockfree_defines.h" 1 3
# 33 "/usr/include/c++/4.8.2/bits/atomic_lockfree_defines.h" 3
       
# 34 "/usr/include/c++/4.8.2/bits/atomic_lockfree_defines.h" 3
# 39 "/usr/include/c++/4.8.2/exception" 2 3

extern "C++" {

namespace std
{
# 60 "/usr/include/c++/4.8.2/exception" 3
  class exception
  {
  public:
    exception() throw() { }
    virtual ~exception() throw();



    virtual const char* what() const throw();
  };



  class bad_exception : public exception
  {
  public:
    bad_exception() throw() { }



    virtual ~bad_exception() throw();


    virtual const char* what() const throw();
  };


  typedef void (*terminate_handler) ();


  typedef void (*unexpected_handler) ();


  terminate_handler set_terminate(terminate_handler) throw();



  void terminate() throw() __attribute__ ((__noreturn__));


  unexpected_handler set_unexpected(unexpected_handler) throw();



  void unexpected() __attribute__ ((__noreturn__));
# 117 "/usr/include/c++/4.8.2/exception" 3
  bool uncaught_exception() throw() __attribute__ ((__pure__));


}

namespace __gnu_cxx
{

# 142 "/usr/include/c++/4.8.2/exception" 3
  void __verbose_terminate_handler();


}

}

#pragma GCC visibility pop
# 41 "/usr/include/c++/4.8.2/new" 2 3

#pragma GCC visibility push(default)

extern "C++" {

namespace std
{






  class bad_alloc : public exception
  {
  public:
    bad_alloc() throw() { }



    virtual ~bad_alloc() throw();


    virtual const char* what() const throw();
  };

  struct nothrow_t { };

  extern const nothrow_t nothrow;



  typedef void (*new_handler)();



  new_handler set_new_handler(new_handler) throw();
}
# 91 "/usr/include/c++/4.8.2/new" 3
void* operator new(std::size_t) throw(std::bad_alloc)
  __attribute__((__externally_visible__));
void* operator new[](std::size_t) throw(std::bad_alloc)
  __attribute__((__externally_visible__));
void operator delete(void*) throw()
  __attribute__((__externally_visible__));
void operator delete[](void*) throw()
  __attribute__((__externally_visible__));
void* operator new(std::size_t, const std::nothrow_t&) throw()
  __attribute__((__externally_visible__));
void* operator new[](std::size_t, const std::nothrow_t&) throw()
  __attribute__((__externally_visible__));
void operator delete(void*, const std::nothrow_t&) throw()
  __attribute__((__externally_visible__));
void operator delete[](void*, const std::nothrow_t&) throw()
  __attribute__((__externally_visible__));


inline void* operator new(std::size_t, void* __p) throw()
{ return __p; }
inline void* operator new[](std::size_t, void* __p) throw()
{ return __p; }


inline void operator delete (void*, void*) throw() { }
inline void operator delete[](void*, void*) throw() { }

}

#pragma GCC visibility pop
# 104 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2
# 117 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void* operator new(std:: size_t, void*) throw();
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void* operator new[](std:: size_t, void*) throw();
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void operator delete(void*, void*) throw();
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void operator delete[](void*, void*) throw();







# 1 "/usr/include/stdio.h" 1 3 4
# 29 "/usr/include/stdio.h" 3 4
extern "C" {



# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 1 3 4
# 34 "/usr/include/stdio.h" 2 3 4
# 44 "/usr/include/stdio.h" 3 4
struct _IO_FILE;



typedef struct _IO_FILE FILE;





# 64 "/usr/include/stdio.h" 3 4
typedef struct _IO_FILE __FILE;
# 74 "/usr/include/stdio.h" 3 4
# 1 "/usr/include/libio.h" 1 3 4
# 32 "/usr/include/libio.h" 3 4
# 1 "/usr/include/_G_config.h" 1 3 4
# 15 "/usr/include/_G_config.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 1 3 4
# 16 "/usr/include/_G_config.h" 2 3 4




# 1 "/usr/include/wchar.h" 1 3 4
# 82 "/usr/include/wchar.h" 3 4
typedef struct
{
  int __count;
  union
  {

    unsigned int __wch;



    char __wchb[4];
  } __value;
} __mbstate_t;
# 21 "/usr/include/_G_config.h" 2 3 4
typedef struct
{
  __off_t __pos;
  __mbstate_t __state;
} _G_fpos_t;
typedef struct
{
  __off64_t __pos;
  __mbstate_t __state;
} _G_fpos64_t;
# 33 "/usr/include/libio.h" 2 3 4
# 50 "/usr/include/libio.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stdarg.h" 1 3 4
# 40 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stdarg.h" 3 4
typedef __builtin_va_list __gnuc_va_list;
# 51 "/usr/include/libio.h" 2 3 4
# 145 "/usr/include/libio.h" 3 4
struct _IO_jump_t; struct _IO_FILE;
# 155 "/usr/include/libio.h" 3 4
typedef void _IO_lock_t;





struct _IO_marker {
  struct _IO_marker *_next;
  struct _IO_FILE *_sbuf;



  int _pos;
# 178 "/usr/include/libio.h" 3 4
};


enum __codecvt_result
{
  __codecvt_ok,
  __codecvt_partial,
  __codecvt_error,
  __codecvt_noconv
};
# 246 "/usr/include/libio.h" 3 4
struct _IO_FILE {
  int _flags;




  char* _IO_read_ptr;
  char* _IO_read_end;
  char* _IO_read_base;
  char* _IO_write_base;
  char* _IO_write_ptr;
  char* _IO_write_end;
  char* _IO_buf_base;
  char* _IO_buf_end;

  char *_IO_save_base;
  char *_IO_backup_base;
  char *_IO_save_end;

  struct _IO_marker *_markers;

  struct _IO_FILE *_chain;

  int _fileno;



  int _flags2;

  __off_t _old_offset;



  unsigned short _cur_column;
  signed char _vtable_offset;
  char _shortbuf[1];



  _IO_lock_t *_lock;
# 294 "/usr/include/libio.h" 3 4
  __off64_t _offset;
# 303 "/usr/include/libio.h" 3 4
  void *__pad1;
  void *__pad2;
  void *__pad3;
  void *__pad4;
  size_t __pad5;

  int _mode;

  char _unused2[15 * sizeof (int) - 4 * sizeof (void *) - sizeof (size_t)];

};





struct _IO_FILE_plus;

extern struct _IO_FILE_plus _IO_2_1_stdin_;
extern struct _IO_FILE_plus _IO_2_1_stdout_;
extern struct _IO_FILE_plus _IO_2_1_stderr_;
# 339 "/usr/include/libio.h" 3 4
typedef __ssize_t __io_read_fn (void *__cookie, char *__buf, size_t __nbytes);







typedef __ssize_t __io_write_fn (void *__cookie, const char *__buf,
     size_t __n);







typedef int __io_seek_fn (void *__cookie, __off64_t *__pos, int __w);


typedef int __io_close_fn (void *__cookie);




typedef __io_read_fn cookie_read_function_t;
typedef __io_write_fn cookie_write_function_t;
typedef __io_seek_fn cookie_seek_function_t;
typedef __io_close_fn cookie_close_function_t;


typedef struct
{
  __io_read_fn *read;
  __io_write_fn *write;
  __io_seek_fn *seek;
  __io_close_fn *close;
} _IO_cookie_io_functions_t;
typedef _IO_cookie_io_functions_t cookie_io_functions_t;

struct _IO_cookie_file;


extern void _IO_cookie_init (struct _IO_cookie_file *__cfile, int __read_write,
        void *__cookie, _IO_cookie_io_functions_t __fns);




extern "C" {


extern int __underflow (_IO_FILE *);
extern int __uflow (_IO_FILE *);
extern int __overflow (_IO_FILE *, int);
# 435 "/usr/include/libio.h" 3 4
extern int _IO_getc (_IO_FILE *__fp);
extern int _IO_putc (int __c, _IO_FILE *__fp);
extern int _IO_feof (_IO_FILE *__fp) throw ();
extern int _IO_ferror (_IO_FILE *__fp) throw ();

extern int _IO_peekc_locked (_IO_FILE *__fp);





extern void _IO_flockfile (_IO_FILE *) throw ();
extern void _IO_funlockfile (_IO_FILE *) throw ();
extern int _IO_ftrylockfile (_IO_FILE *) throw ();
# 465 "/usr/include/libio.h" 3 4
extern int _IO_vfscanf (_IO_FILE * __restrict, const char * __restrict,
   __gnuc_va_list, int *__restrict);
extern int _IO_vfprintf (_IO_FILE *__restrict, const char *__restrict,
    __gnuc_va_list);
extern __ssize_t _IO_padn (_IO_FILE *, int, __ssize_t);
extern size_t _IO_sgetn (_IO_FILE *, void *, size_t);

extern __off64_t _IO_seekoff (_IO_FILE *, __off64_t, int, int);
extern __off64_t _IO_seekpos (_IO_FILE *, __off64_t, int);

extern void _IO_free_backup_area (_IO_FILE *) throw ();
# 527 "/usr/include/libio.h" 3 4
}
# 75 "/usr/include/stdio.h" 2 3 4




typedef __gnuc_va_list va_list;
# 90 "/usr/include/stdio.h" 3 4
typedef __off_t off_t;






typedef __off64_t off64_t;




typedef __ssize_t ssize_t;







typedef _G_fpos_t fpos_t;





typedef _G_fpos64_t fpos64_t;
# 164 "/usr/include/stdio.h" 3 4
# 1 "/usr/include/bits/stdio_lim.h" 1 3 4
# 165 "/usr/include/stdio.h" 2 3 4



extern struct _IO_FILE *stdin;
extern struct _IO_FILE *stdout;
extern struct _IO_FILE *stderr;







extern int remove (const char *__filename) throw ();

extern int rename (const char *__old, const char *__new) throw ();




extern int renameat (int __oldfd, const char *__old, int __newfd,
       const char *__new) throw ();








extern FILE *tmpfile (void) ;
# 205 "/usr/include/stdio.h" 3 4
extern FILE *tmpfile64 (void) ;



extern char *tmpnam (char *__s) throw () ;





extern char *tmpnam_r (char *__s) throw () ;
# 227 "/usr/include/stdio.h" 3 4
extern char *tempnam (const char *__dir, const char *__pfx)
     throw () __attribute__ ((__malloc__)) ;








extern int fclose (FILE *__stream);




extern int fflush (FILE *__stream);

# 252 "/usr/include/stdio.h" 3 4
extern int fflush_unlocked (FILE *__stream);
# 262 "/usr/include/stdio.h" 3 4
extern int fcloseall (void);









extern FILE *fopen (const char *__restrict __filename,
      const char *__restrict __modes) ;




extern FILE *freopen (const char *__restrict __filename,
        const char *__restrict __modes,
        FILE *__restrict __stream) ;
# 295 "/usr/include/stdio.h" 3 4


extern FILE *fopen64 (const char *__restrict __filename,
        const char *__restrict __modes) ;
extern FILE *freopen64 (const char *__restrict __filename,
   const char *__restrict __modes,
   FILE *__restrict __stream) ;




extern FILE *fdopen (int __fd, const char *__modes) throw () ;





extern FILE *fopencookie (void *__restrict __magic_cookie,
     const char *__restrict __modes,
     _IO_cookie_io_functions_t __io_funcs) throw () ;




extern FILE *fmemopen (void *__s, size_t __len, const char *__modes)
  throw () ;




extern FILE *open_memstream (char **__bufloc, size_t *__sizeloc) throw () ;






extern void setbuf (FILE *__restrict __stream, char *__restrict __buf) throw ();



extern int setvbuf (FILE *__restrict __stream, char *__restrict __buf,
      int __modes, size_t __n) throw ();





extern void setbuffer (FILE *__restrict __stream, char *__restrict __buf,
         size_t __size) throw ();


extern void setlinebuf (FILE *__stream) throw ();








extern int fprintf (FILE *__restrict __stream,
      const char *__restrict __format, ...);




extern int printf (const char *__restrict __format, ...);

extern int sprintf (char *__restrict __s,
      const char *__restrict __format, ...) throw ();





extern int vfprintf (FILE *__restrict __s, const char *__restrict __format,
       __gnuc_va_list __arg);




extern int vprintf (const char *__restrict __format, __gnuc_va_list __arg);

extern int vsprintf (char *__restrict __s, const char *__restrict __format,
       __gnuc_va_list __arg) throw ();





extern int snprintf (char *__restrict __s, size_t __maxlen,
       const char *__restrict __format, ...)
     throw () __attribute__ ((__format__ (__printf__, 3, 4)));

extern int vsnprintf (char *__restrict __s, size_t __maxlen,
        const char *__restrict __format, __gnuc_va_list __arg)
     throw () __attribute__ ((__format__ (__printf__, 3, 0)));






extern int vasprintf (char **__restrict __ptr, const char *__restrict __f,
        __gnuc_va_list __arg)
     throw () __attribute__ ((__format__ (__printf__, 2, 0))) ;
extern int __asprintf (char **__restrict __ptr,
         const char *__restrict __fmt, ...)
     throw () __attribute__ ((__format__ (__printf__, 2, 3))) ;
extern int asprintf (char **__restrict __ptr,
       const char *__restrict __fmt, ...)
     throw () __attribute__ ((__format__ (__printf__, 2, 3))) ;




extern int vdprintf (int __fd, const char *__restrict __fmt,
       __gnuc_va_list __arg)
     __attribute__ ((__format__ (__printf__, 2, 0)));
extern int dprintf (int __fd, const char *__restrict __fmt, ...)
     __attribute__ ((__format__ (__printf__, 2, 3)));








extern int fscanf (FILE *__restrict __stream,
     const char *__restrict __format, ...) ;




extern int scanf (const char *__restrict __format, ...) ;

extern int sscanf (const char *__restrict __s,
     const char *__restrict __format, ...) throw ();
# 463 "/usr/include/stdio.h" 3 4








extern int vfscanf (FILE *__restrict __s, const char *__restrict __format,
      __gnuc_va_list __arg)
     __attribute__ ((__format__ (__scanf__, 2, 0))) ;





extern int vscanf (const char *__restrict __format, __gnuc_va_list __arg)
     __attribute__ ((__format__ (__scanf__, 1, 0))) ;


extern int vsscanf (const char *__restrict __s,
      const char *__restrict __format, __gnuc_va_list __arg)
     throw () __attribute__ ((__format__ (__scanf__, 2, 0)));
# 522 "/usr/include/stdio.h" 3 4









extern int fgetc (FILE *__stream);
extern int getc (FILE *__stream);





extern int getchar (void);

# 550 "/usr/include/stdio.h" 3 4
extern int getc_unlocked (FILE *__stream);
extern int getchar_unlocked (void);
# 561 "/usr/include/stdio.h" 3 4
extern int fgetc_unlocked (FILE *__stream);











extern int fputc (int __c, FILE *__stream);
extern int putc (int __c, FILE *__stream);





extern int putchar (int __c);

# 594 "/usr/include/stdio.h" 3 4
extern int fputc_unlocked (int __c, FILE *__stream);







extern int putc_unlocked (int __c, FILE *__stream);
extern int putchar_unlocked (int __c);






extern int getw (FILE *__stream);


extern int putw (int __w, FILE *__stream);








extern char *fgets (char *__restrict __s, int __n, FILE *__restrict __stream)
     ;
# 638 "/usr/include/stdio.h" 3 4
extern char *gets (char *__s) __attribute__ ((__deprecated__));


# 649 "/usr/include/stdio.h" 3 4
extern char *fgets_unlocked (char *__restrict __s, int __n,
        FILE *__restrict __stream) ;
# 665 "/usr/include/stdio.h" 3 4
extern __ssize_t __getdelim (char **__restrict __lineptr,
          size_t *__restrict __n, int __delimiter,
          FILE *__restrict __stream) ;
extern __ssize_t getdelim (char **__restrict __lineptr,
        size_t *__restrict __n, int __delimiter,
        FILE *__restrict __stream) ;







extern __ssize_t getline (char **__restrict __lineptr,
       size_t *__restrict __n,
       FILE *__restrict __stream) ;








extern int fputs (const char *__restrict __s, FILE *__restrict __stream);





extern int puts (const char *__s);






extern int ungetc (int __c, FILE *__stream);






extern size_t fread (void *__restrict __ptr, size_t __size,
       size_t __n, FILE *__restrict __stream) ;




extern size_t fwrite (const void *__restrict __ptr, size_t __size,
        size_t __n, FILE *__restrict __s);

# 726 "/usr/include/stdio.h" 3 4
extern int fputs_unlocked (const char *__restrict __s,
      FILE *__restrict __stream);
# 737 "/usr/include/stdio.h" 3 4
extern size_t fread_unlocked (void *__restrict __ptr, size_t __size,
         size_t __n, FILE *__restrict __stream) ;
extern size_t fwrite_unlocked (const void *__restrict __ptr, size_t __size,
          size_t __n, FILE *__restrict __stream);








extern int fseek (FILE *__stream, long int __off, int __whence);




extern long int ftell (FILE *__stream) ;




extern void rewind (FILE *__stream);

# 773 "/usr/include/stdio.h" 3 4
extern int fseeko (FILE *__stream, __off_t __off, int __whence);




extern __off_t ftello (FILE *__stream) ;
# 792 "/usr/include/stdio.h" 3 4






extern int fgetpos (FILE *__restrict __stream, fpos_t *__restrict __pos);




extern int fsetpos (FILE *__stream, const fpos_t *__pos);
# 815 "/usr/include/stdio.h" 3 4



extern int fseeko64 (FILE *__stream, __off64_t __off, int __whence);
extern __off64_t ftello64 (FILE *__stream) ;
extern int fgetpos64 (FILE *__restrict __stream, fpos64_t *__restrict __pos);
extern int fsetpos64 (FILE *__stream, const fpos64_t *__pos);




extern void clearerr (FILE *__stream) throw ();

extern int feof (FILE *__stream) throw () ;

extern int ferror (FILE *__stream) throw () ;




extern void clearerr_unlocked (FILE *__stream) throw ();
extern int feof_unlocked (FILE *__stream) throw () ;
extern int ferror_unlocked (FILE *__stream) throw () ;








extern void perror (const char *__s);






# 1 "/usr/include/bits/sys_errlist.h" 1 3 4
# 26 "/usr/include/bits/sys_errlist.h" 3 4
extern int sys_nerr;
extern const char *const sys_errlist[];


extern int _sys_nerr;
extern const char *const _sys_errlist[];
# 854 "/usr/include/stdio.h" 2 3 4




extern int fileno (FILE *__stream) throw () ;




extern int fileno_unlocked (FILE *__stream) throw () ;
# 873 "/usr/include/stdio.h" 3 4
extern FILE *popen (const char *__command, const char *__modes) ;





extern int pclose (FILE *__stream);





extern char *ctermid (char *__s) throw ();





extern char *cuserid (char *__s);




struct obstack;


extern int obstack_printf (struct obstack *__restrict __obstack,
      const char *__restrict __format, ...)
     throw () __attribute__ ((__format__ (__printf__, 2, 3)));
extern int obstack_vprintf (struct obstack *__restrict __obstack,
       const char *__restrict __format,
       __gnuc_va_list __args)
     throw () __attribute__ ((__format__ (__printf__, 2, 0)));







extern void flockfile (FILE *__stream) throw ();



extern int ftrylockfile (FILE *__stream) throw () ;


extern void funlockfile (FILE *__stream) throw ();
# 943 "/usr/include/stdio.h" 3 4
}
# 129 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2
# 1 "/usr/include/stdlib.h" 1 3 4
# 32 "/usr/include/stdlib.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 1 3 4
# 33 "/usr/include/stdlib.h" 2 3 4

extern "C" {






# 1 "/usr/include/bits/waitflags.h" 1 3 4
# 42 "/usr/include/stdlib.h" 2 3 4
# 1 "/usr/include/bits/waitstatus.h" 1 3 4
# 64 "/usr/include/bits/waitstatus.h" 3 4
# 1 "/usr/include/endian.h" 1 3 4
# 36 "/usr/include/endian.h" 3 4
# 1 "/usr/include/bits/endian.h" 1 3 4
# 37 "/usr/include/endian.h" 2 3 4
# 60 "/usr/include/endian.h" 3 4
# 1 "/usr/include/bits/byteswap.h" 1 3 4
# 28 "/usr/include/bits/byteswap.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 29 "/usr/include/bits/byteswap.h" 2 3 4






# 1 "/usr/include/bits/byteswap-16.h" 1 3 4
# 36 "/usr/include/bits/byteswap.h" 2 3 4
# 44 "/usr/include/bits/byteswap.h" 3 4
static __inline unsigned int
__bswap_32 (unsigned int __bsx)
{
  return __builtin_bswap32 (__bsx);
}
# 108 "/usr/include/bits/byteswap.h" 3 4
static __inline __uint64_t
__bswap_64 (__uint64_t __bsx)
{
  return __builtin_bswap64 (__bsx);
}
# 61 "/usr/include/endian.h" 2 3 4
# 65 "/usr/include/bits/waitstatus.h" 2 3 4

union wait
  {
    int w_status;
    struct
      {

 unsigned int __w_termsig:7;
 unsigned int __w_coredump:1;
 unsigned int __w_retcode:8;
 unsigned int:16;







      } __wait_terminated;
    struct
      {

 unsigned int __w_stopval:8;
 unsigned int __w_stopsig:8;
 unsigned int:16;






      } __wait_stopped;
  };
# 43 "/usr/include/stdlib.h" 2 3 4
# 95 "/usr/include/stdlib.h" 3 4


typedef struct
  {
    int quot;
    int rem;
  } div_t;



typedef struct
  {
    long int quot;
    long int rem;
  } ldiv_t;







__extension__ typedef struct
  {
    long long int quot;
    long long int rem;
  } lldiv_t;


# 139 "/usr/include/stdlib.h" 3 4
extern size_t __ctype_get_mb_cur_max (void) throw () ;




extern double atof (const char *__nptr)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1))) ;

extern int atoi (const char *__nptr)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1))) ;

extern long int atol (const char *__nptr)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1))) ;





__extension__ extern long long int atoll (const char *__nptr)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1))) ;





extern double strtod (const char *__restrict __nptr,
        char **__restrict __endptr)
     throw () __attribute__ ((__nonnull__ (1)));





extern float strtof (const char *__restrict __nptr,
       char **__restrict __endptr) throw () __attribute__ ((__nonnull__ (1)));

extern long double strtold (const char *__restrict __nptr,
       char **__restrict __endptr)
     throw () __attribute__ ((__nonnull__ (1)));





extern long int strtol (const char *__restrict __nptr,
   char **__restrict __endptr, int __base)
     throw () __attribute__ ((__nonnull__ (1)));

extern unsigned long int strtoul (const char *__restrict __nptr,
      char **__restrict __endptr, int __base)
     throw () __attribute__ ((__nonnull__ (1)));




__extension__
extern long long int strtoq (const char *__restrict __nptr,
        char **__restrict __endptr, int __base)
     throw () __attribute__ ((__nonnull__ (1)));

__extension__
extern unsigned long long int strtouq (const char *__restrict __nptr,
           char **__restrict __endptr, int __base)
     throw () __attribute__ ((__nonnull__ (1)));





__extension__
extern long long int strtoll (const char *__restrict __nptr,
         char **__restrict __endptr, int __base)
     throw () __attribute__ ((__nonnull__ (1)));

__extension__
extern unsigned long long int strtoull (const char *__restrict __nptr,
     char **__restrict __endptr, int __base)
     throw () __attribute__ ((__nonnull__ (1)));

# 239 "/usr/include/stdlib.h" 3 4
extern long int strtol_l (const char *__restrict __nptr,
     char **__restrict __endptr, int __base,
     __locale_t __loc) throw () __attribute__ ((__nonnull__ (1, 4)));

extern unsigned long int strtoul_l (const char *__restrict __nptr,
        char **__restrict __endptr,
        int __base, __locale_t __loc)
     throw () __attribute__ ((__nonnull__ (1, 4)));

__extension__
extern long long int strtoll_l (const char *__restrict __nptr,
    char **__restrict __endptr, int __base,
    __locale_t __loc)
     throw () __attribute__ ((__nonnull__ (1, 4)));

__extension__
extern unsigned long long int strtoull_l (const char *__restrict __nptr,
       char **__restrict __endptr,
       int __base, __locale_t __loc)
     throw () __attribute__ ((__nonnull__ (1, 4)));

extern double strtod_l (const char *__restrict __nptr,
   char **__restrict __endptr, __locale_t __loc)
     throw () __attribute__ ((__nonnull__ (1, 3)));

extern float strtof_l (const char *__restrict __nptr,
         char **__restrict __endptr, __locale_t __loc)
     throw () __attribute__ ((__nonnull__ (1, 3)));

extern long double strtold_l (const char *__restrict __nptr,
         char **__restrict __endptr,
         __locale_t __loc)
     throw () __attribute__ ((__nonnull__ (1, 3)));
# 305 "/usr/include/stdlib.h" 3 4
extern char *l64a (long int __n) throw () ;


extern long int a64l (const char *__s)
     throw () __attribute__ ((__pure__)) __attribute__ ((__nonnull__ (1))) ;




# 1 "/usr/include/sys/types.h" 1 3 4
# 27 "/usr/include/sys/types.h" 3 4
extern "C" {





typedef __u_char u_char;
typedef __u_short u_short;
typedef __u_int u_int;
typedef __u_long u_long;
typedef __quad_t quad_t;
typedef __u_quad_t u_quad_t;
typedef __fsid_t fsid_t;




typedef __loff_t loff_t;



typedef __ino_t ino_t;






typedef __ino64_t ino64_t;




typedef __dev_t dev_t;




typedef __gid_t gid_t;




typedef __mode_t mode_t;




typedef __nlink_t nlink_t;




typedef __uid_t uid_t;
# 104 "/usr/include/sys/types.h" 3 4
typedef __id_t id_t;
# 115 "/usr/include/sys/types.h" 3 4
typedef __daddr_t daddr_t;
typedef __caddr_t caddr_t;





typedef __key_t key_t;
# 136 "/usr/include/sys/types.h" 3 4
typedef __useconds_t useconds_t;



typedef __suseconds_t suseconds_t;





# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 1 3 4
# 147 "/usr/include/sys/types.h" 2 3 4



typedef unsigned long int ulong;
typedef unsigned short int ushort;
typedef unsigned int uint;
# 194 "/usr/include/sys/types.h" 3 4
typedef int int8_t __attribute__ ((__mode__ (__QI__)));
typedef int int16_t __attribute__ ((__mode__ (__HI__)));
typedef int int32_t __attribute__ ((__mode__ (__SI__)));
typedef int int64_t __attribute__ ((__mode__ (__DI__)));


typedef unsigned int u_int8_t __attribute__ ((__mode__ (__QI__)));
typedef unsigned int u_int16_t __attribute__ ((__mode__ (__HI__)));
typedef unsigned int u_int32_t __attribute__ ((__mode__ (__SI__)));
typedef unsigned int u_int64_t __attribute__ ((__mode__ (__DI__)));

typedef int register_t __attribute__ ((__mode__ (__word__)));
# 219 "/usr/include/sys/types.h" 3 4
# 1 "/usr/include/sys/select.h" 1 3 4
# 30 "/usr/include/sys/select.h" 3 4
# 1 "/usr/include/bits/select.h" 1 3 4
# 22 "/usr/include/bits/select.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 23 "/usr/include/bits/select.h" 2 3 4
# 31 "/usr/include/sys/select.h" 2 3 4


# 1 "/usr/include/bits/sigset.h" 1 3 4
# 23 "/usr/include/bits/sigset.h" 3 4
typedef int __sig_atomic_t;




typedef struct
  {
    unsigned long int __val[(1024 / (8 * sizeof (unsigned long int)))];
  } __sigset_t;
# 34 "/usr/include/sys/select.h" 2 3 4



typedef __sigset_t sigset_t;







# 1 "/usr/include/bits/time.h" 1 3 4
# 46 "/usr/include/sys/select.h" 2 3 4
# 54 "/usr/include/sys/select.h" 3 4
typedef long int __fd_mask;
# 64 "/usr/include/sys/select.h" 3 4
typedef struct
  {



    __fd_mask fds_bits[1024 / (8 * (int) sizeof (__fd_mask))];





  } fd_set;






typedef __fd_mask fd_mask;
# 96 "/usr/include/sys/select.h" 3 4
extern "C" {
# 106 "/usr/include/sys/select.h" 3 4
extern int select (int __nfds, fd_set *__restrict __readfds,
     fd_set *__restrict __writefds,
     fd_set *__restrict __exceptfds,
     struct timeval *__restrict __timeout);
# 118 "/usr/include/sys/select.h" 3 4
extern int pselect (int __nfds, fd_set *__restrict __readfds,
      fd_set *__restrict __writefds,
      fd_set *__restrict __exceptfds,
      const struct timespec *__restrict __timeout,
      const __sigset_t *__restrict __sigmask);
# 131 "/usr/include/sys/select.h" 3 4
}
# 220 "/usr/include/sys/types.h" 2 3 4


# 1 "/usr/include/sys/sysmacros.h" 1 3 4
# 29 "/usr/include/sys/sysmacros.h" 3 4
extern "C" {

__extension__
extern unsigned int gnu_dev_major (unsigned long long int __dev)
     throw () __attribute__ ((__const__));
__extension__
extern unsigned int gnu_dev_minor (unsigned long long int __dev)
     throw () __attribute__ ((__const__));
__extension__
extern unsigned long long int gnu_dev_makedev (unsigned int __major,
            unsigned int __minor)
     throw () __attribute__ ((__const__));
# 63 "/usr/include/sys/sysmacros.h" 3 4
}
# 223 "/usr/include/sys/types.h" 2 3 4





typedef __blksize_t blksize_t;






typedef __blkcnt_t blkcnt_t;



typedef __fsblkcnt_t fsblkcnt_t;



typedef __fsfilcnt_t fsfilcnt_t;
# 262 "/usr/include/sys/types.h" 3 4
typedef __blkcnt64_t blkcnt64_t;
typedef __fsblkcnt64_t fsblkcnt64_t;
typedef __fsfilcnt64_t fsfilcnt64_t;





# 1 "/usr/include/bits/pthreadtypes.h" 1 3 4
# 21 "/usr/include/bits/pthreadtypes.h" 3 4
# 1 "/usr/include/bits/wordsize.h" 1 3 4
# 22 "/usr/include/bits/pthreadtypes.h" 2 3 4
# 60 "/usr/include/bits/pthreadtypes.h" 3 4
typedef unsigned long int pthread_t;


union pthread_attr_t
{
  char __size[56];
  long int __align;
};

typedef union pthread_attr_t pthread_attr_t;





typedef struct __pthread_internal_list
{
  struct __pthread_internal_list *__prev;
  struct __pthread_internal_list *__next;
} __pthread_list_t;
# 90 "/usr/include/bits/pthreadtypes.h" 3 4
typedef union
{
  struct __pthread_mutex_s
  {
    int __lock;
    unsigned int __count;
    int __owner;

    unsigned int __nusers;



    int __kind;

    short __spins;
    short __elision;
    __pthread_list_t __list;
# 125 "/usr/include/bits/pthreadtypes.h" 3 4
  } __data;
  char __size[40];
  long int __align;
} pthread_mutex_t;

typedef union
{
  char __size[4];
  int __align;
} pthread_mutexattr_t;




typedef union
{
  struct
  {
    int __lock;
    unsigned int __futex;
    __extension__ unsigned long long int __total_seq;
    __extension__ unsigned long long int __wakeup_seq;
    __extension__ unsigned long long int __woken_seq;
    void *__mutex;
    unsigned int __nwaiters;
    unsigned int __broadcast_seq;
  } __data;
  char __size[48];
  __extension__ long long int __align;
} pthread_cond_t;

typedef union
{
  char __size[4];
  int __align;
} pthread_condattr_t;



typedef unsigned int pthread_key_t;



typedef int pthread_once_t;





typedef union
{

  struct
  {
    int __lock;
    unsigned int __nr_readers;
    unsigned int __readers_wakeup;
    unsigned int __writer_wakeup;
    unsigned int __nr_readers_queued;
    unsigned int __nr_writers_queued;
    int __writer;
    int __shared;
    unsigned long int __pad1;
    unsigned long int __pad2;


    unsigned int __flags;

  } __data;
# 212 "/usr/include/bits/pthreadtypes.h" 3 4
  char __size[56];
  long int __align;
} pthread_rwlock_t;

typedef union
{
  char __size[8];
  long int __align;
} pthread_rwlockattr_t;





typedef volatile int pthread_spinlock_t;




typedef union
{
  char __size[32];
  long int __align;
} pthread_barrier_t;

typedef union
{
  char __size[4];
  int __align;
} pthread_barrierattr_t;
# 271 "/usr/include/sys/types.h" 2 3 4


}
# 315 "/usr/include/stdlib.h" 2 3 4






extern long int random (void) throw ();


extern void srandom (unsigned int __seed) throw ();





extern char *initstate (unsigned int __seed, char *__statebuf,
   size_t __statelen) throw () __attribute__ ((__nonnull__ (2)));



extern char *setstate (char *__statebuf) throw () __attribute__ ((__nonnull__ (1)));







struct random_data
  {
    int32_t *fptr;
    int32_t *rptr;
    int32_t *state;
    int rand_type;
    int rand_deg;
    int rand_sep;
    int32_t *end_ptr;
  };

extern int random_r (struct random_data *__restrict __buf,
       int32_t *__restrict __result) throw () __attribute__ ((__nonnull__ (1, 2)));

extern int srandom_r (unsigned int __seed, struct random_data *__buf)
     throw () __attribute__ ((__nonnull__ (2)));

extern int initstate_r (unsigned int __seed, char *__restrict __statebuf,
   size_t __statelen,
   struct random_data *__restrict __buf)
     throw () __attribute__ ((__nonnull__ (2, 4)));

extern int setstate_r (char *__restrict __statebuf,
         struct random_data *__restrict __buf)
     throw () __attribute__ ((__nonnull__ (1, 2)));






extern int rand (void) throw ();

extern void srand (unsigned int __seed) throw ();




extern int rand_r (unsigned int *__seed) throw ();







extern double drand48 (void) throw ();
extern double erand48 (unsigned short int __xsubi[3]) throw () __attribute__ ((__nonnull__ (1)));


extern long int lrand48 (void) throw ();
extern long int nrand48 (unsigned short int __xsubi[3])
     throw () __attribute__ ((__nonnull__ (1)));


extern long int mrand48 (void) throw ();
extern long int jrand48 (unsigned short int __xsubi[3])
     throw () __attribute__ ((__nonnull__ (1)));


extern void srand48 (long int __seedval) throw ();
extern unsigned short int *seed48 (unsigned short int __seed16v[3])
     throw () __attribute__ ((__nonnull__ (1)));
extern void lcong48 (unsigned short int __param[7]) throw () __attribute__ ((__nonnull__ (1)));





struct drand48_data
  {
    unsigned short int __x[3];
    unsigned short int __old_x[3];
    unsigned short int __c;
    unsigned short int __init;
    unsigned long long int __a;
  };


extern int drand48_r (struct drand48_data *__restrict __buffer,
        double *__restrict __result) throw () __attribute__ ((__nonnull__ (1, 2)));
extern int erand48_r (unsigned short int __xsubi[3],
        struct drand48_data *__restrict __buffer,
        double *__restrict __result) throw () __attribute__ ((__nonnull__ (1, 2)));


extern int lrand48_r (struct drand48_data *__restrict __buffer,
        long int *__restrict __result)
     throw () __attribute__ ((__nonnull__ (1, 2)));
extern int nrand48_r (unsigned short int __xsubi[3],
        struct drand48_data *__restrict __buffer,
        long int *__restrict __result)
     throw () __attribute__ ((__nonnull__ (1, 2)));


extern int mrand48_r (struct drand48_data *__restrict __buffer,
        long int *__restrict __result)
     throw () __attribute__ ((__nonnull__ (1, 2)));
extern int jrand48_r (unsigned short int __xsubi[3],
        struct drand48_data *__restrict __buffer,
        long int *__restrict __result)
     throw () __attribute__ ((__nonnull__ (1, 2)));


extern int srand48_r (long int __seedval, struct drand48_data *__buffer)
     throw () __attribute__ ((__nonnull__ (2)));

extern int seed48_r (unsigned short int __seed16v[3],
       struct drand48_data *__buffer) throw () __attribute__ ((__nonnull__ (1, 2)));

extern int lcong48_r (unsigned short int __param[7],
        struct drand48_data *__buffer)
     throw () __attribute__ ((__nonnull__ (1, 2)));









extern void *malloc (size_t __size) throw () __attribute__ ((__malloc__)) ;

extern void *calloc (size_t __nmemb, size_t __size)
     throw () __attribute__ ((__malloc__)) ;










extern void *realloc (void *__ptr, size_t __size)
     throw () __attribute__ ((__warn_unused_result__));

extern void free (void *__ptr) throw ();




extern void cfree (void *__ptr) throw ();



# 1 "/usr/include/alloca.h" 1 3 4
# 24 "/usr/include/alloca.h" 3 4
# 1 "/usr/lib/gcc/x86_64-redhat-linux/4.8.5/include/stddef.h" 1 3 4
# 25 "/usr/include/alloca.h" 2 3 4

extern "C" {





extern void *alloca (size_t __size) throw ();





}
# 492 "/usr/include/stdlib.h" 2 3 4





extern void *valloc (size_t __size) throw () __attribute__ ((__malloc__)) ;




extern int posix_memalign (void **__memptr, size_t __alignment, size_t __size)
     throw () __attribute__ ((__nonnull__ (1))) ;




extern void *aligned_alloc (size_t __alignment, size_t __size)
     throw () __attribute__ ((__malloc__, __alloc_size__ (2)));




extern void abort (void) throw () __attribute__ ((__noreturn__));



extern int atexit (void (*__func) (void)) throw () __attribute__ ((__nonnull__ (1)));




extern "C++" int at_quick_exit (void (*__func) (void))
     throw () __asm ("at_quick_exit") __attribute__ ((__nonnull__ (1)));









extern int on_exit (void (*__func) (int __status, void *__arg), void *__arg)
     throw () __attribute__ ((__nonnull__ (1)));






extern void exit (int __status) throw () __attribute__ ((__noreturn__));





extern void quick_exit (int __status) throw () __attribute__ ((__noreturn__));







extern void _Exit (int __status) throw () __attribute__ ((__noreturn__));






extern char *getenv (const char *__name) throw () __attribute__ ((__nonnull__ (1))) ;





extern char *secure_getenv (const char *__name)
     throw () __attribute__ ((__nonnull__ (1))) ;






extern int putenv (char *__string) throw () __attribute__ ((__nonnull__ (1)));





extern int setenv (const char *__name, const char *__value, int __replace)
     throw () __attribute__ ((__nonnull__ (2)));


extern int unsetenv (const char *__name) throw () __attribute__ ((__nonnull__ (1)));






extern int clearenv (void) throw ();
# 605 "/usr/include/stdlib.h" 3 4
extern char *mktemp (char *__template) throw () __attribute__ ((__nonnull__ (1)));
# 619 "/usr/include/stdlib.h" 3 4
extern int mkstemp (char *__template) __attribute__ ((__nonnull__ (1))) ;
# 629 "/usr/include/stdlib.h" 3 4
extern int mkstemp64 (char *__template) __attribute__ ((__nonnull__ (1))) ;
# 641 "/usr/include/stdlib.h" 3 4
extern int mkstemps (char *__template, int __suffixlen) __attribute__ ((__nonnull__ (1))) ;
# 651 "/usr/include/stdlib.h" 3 4
extern int mkstemps64 (char *__template, int __suffixlen)
     __attribute__ ((__nonnull__ (1))) ;
# 662 "/usr/include/stdlib.h" 3 4
extern char *mkdtemp (char *__template) throw () __attribute__ ((__nonnull__ (1))) ;
# 673 "/usr/include/stdlib.h" 3 4
extern int mkostemp (char *__template, int __flags) __attribute__ ((__nonnull__ (1))) ;
# 683 "/usr/include/stdlib.h" 3 4
extern int mkostemp64 (char *__template, int __flags) __attribute__ ((__nonnull__ (1))) ;
# 693 "/usr/include/stdlib.h" 3 4
extern int mkostemps (char *__template, int __suffixlen, int __flags)
     __attribute__ ((__nonnull__ (1))) ;
# 705 "/usr/include/stdlib.h" 3 4
extern int mkostemps64 (char *__template, int __suffixlen, int __flags)
     __attribute__ ((__nonnull__ (1))) ;









extern int system (const char *__command) ;






extern char *canonicalize_file_name (const char *__name)
     throw () __attribute__ ((__nonnull__ (1))) ;
# 733 "/usr/include/stdlib.h" 3 4
extern char *realpath (const char *__restrict __name,
         char *__restrict __resolved) throw () ;






typedef int (*__compar_fn_t) (const void *, const void *);


typedef __compar_fn_t comparison_fn_t;



typedef int (*__compar_d_fn_t) (const void *, const void *, void *);





extern void *bsearch (const void *__key, const void *__base,
        size_t __nmemb, size_t __size, __compar_fn_t __compar)
     __attribute__ ((__nonnull__ (1, 2, 5))) ;



extern void qsort (void *__base, size_t __nmemb, size_t __size,
     __compar_fn_t __compar) __attribute__ ((__nonnull__ (1, 4)));

extern void qsort_r (void *__base, size_t __nmemb, size_t __size,
       __compar_d_fn_t __compar, void *__arg)
  __attribute__ ((__nonnull__ (1, 4)));




extern int abs (int __x) throw () __attribute__ ((__const__)) ;
extern long int labs (long int __x) throw () __attribute__ ((__const__)) ;



__extension__ extern long long int llabs (long long int __x)
     throw () __attribute__ ((__const__)) ;







extern div_t div (int __numer, int __denom)
     throw () __attribute__ ((__const__)) ;
extern ldiv_t ldiv (long int __numer, long int __denom)
     throw () __attribute__ ((__const__)) ;




__extension__ extern lldiv_t lldiv (long long int __numer,
        long long int __denom)
     throw () __attribute__ ((__const__)) ;

# 807 "/usr/include/stdlib.h" 3 4
extern char *ecvt (double __value, int __ndigit, int *__restrict __decpt,
     int *__restrict __sign) throw () __attribute__ ((__nonnull__ (3, 4))) ;




extern char *fcvt (double __value, int __ndigit, int *__restrict __decpt,
     int *__restrict __sign) throw () __attribute__ ((__nonnull__ (3, 4))) ;




extern char *gcvt (double __value, int __ndigit, char *__buf)
     throw () __attribute__ ((__nonnull__ (3))) ;




extern char *qecvt (long double __value, int __ndigit,
      int *__restrict __decpt, int *__restrict __sign)
     throw () __attribute__ ((__nonnull__ (3, 4))) ;
extern char *qfcvt (long double __value, int __ndigit,
      int *__restrict __decpt, int *__restrict __sign)
     throw () __attribute__ ((__nonnull__ (3, 4))) ;
extern char *qgcvt (long double __value, int __ndigit, char *__buf)
     throw () __attribute__ ((__nonnull__ (3))) ;




extern int ecvt_r (double __value, int __ndigit, int *__restrict __decpt,
     int *__restrict __sign, char *__restrict __buf,
     size_t __len) throw () __attribute__ ((__nonnull__ (3, 4, 5)));
extern int fcvt_r (double __value, int __ndigit, int *__restrict __decpt,
     int *__restrict __sign, char *__restrict __buf,
     size_t __len) throw () __attribute__ ((__nonnull__ (3, 4, 5)));

extern int qecvt_r (long double __value, int __ndigit,
      int *__restrict __decpt, int *__restrict __sign,
      char *__restrict __buf, size_t __len)
     throw () __attribute__ ((__nonnull__ (3, 4, 5)));
extern int qfcvt_r (long double __value, int __ndigit,
      int *__restrict __decpt, int *__restrict __sign,
      char *__restrict __buf, size_t __len)
     throw () __attribute__ ((__nonnull__ (3, 4, 5)));







extern int mblen (const char *__s, size_t __n) throw () ;


extern int mbtowc (wchar_t *__restrict __pwc,
     const char *__restrict __s, size_t __n) throw () ;


extern int wctomb (char *__s, wchar_t __wchar) throw () ;



extern size_t mbstowcs (wchar_t *__restrict __pwcs,
   const char *__restrict __s, size_t __n) throw ();

extern size_t wcstombs (char *__restrict __s,
   const wchar_t *__restrict __pwcs, size_t __n)
     throw ();








extern int rpmatch (const char *__response) throw () __attribute__ ((__nonnull__ (1))) ;
# 895 "/usr/include/stdlib.h" 3 4
extern int getsubopt (char **__restrict __optionp,
        char *const *__restrict __tokens,
        char **__restrict __valuep)
     throw () __attribute__ ((__nonnull__ (1, 2, 3))) ;





extern void setkey (const char *__key) throw () __attribute__ ((__nonnull__ (1)));







extern int posix_openpt (int __oflag) ;







extern int grantpt (int __fd) throw ();



extern int unlockpt (int __fd) throw ();




extern char *ptsname (int __fd) throw () ;






extern int ptsname_r (int __fd, char *__buf, size_t __buflen)
     throw () __attribute__ ((__nonnull__ (2)));


extern int getpt (void);






extern int getloadavg (double __loadavg[], int __nelem)
     throw () __attribute__ ((__nonnull__ (1)));


# 1 "/usr/include/bits/stdlib-float.h" 1 3 4
# 952 "/usr/include/stdlib.h" 2 3 4
# 964 "/usr/include/stdlib.h" 3 4
}
# 130 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2





extern "C"
{
extern







__attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) int printf(const char*, ...);



extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void* malloc(size_t) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void free(void*) throw ();

}





# 1 "/usr/include/assert.h" 1 3 4
# 65 "/usr/include/assert.h" 3 4
extern "C" {


extern void __assert_fail (const char *__assertion, const char *__file,
      unsigned int __line, const char *__function)
     throw () __attribute__ ((__noreturn__));


extern void __assert_perror_fail (int __errnum, const char *__file,
      unsigned int __line, const char *__function)
     throw () __attribute__ ((__noreturn__));




extern void __assert (const char *__assertion, const char *__file, int __line)
     throw () __attribute__ ((__noreturn__));


}
# 159 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2


extern "C"
{
# 189 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void __assert_fail(
  const char *, const char *, unsigned int, const char *)
  throw ();




}
# 240 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void* operator new(std:: size_t) throw(std:: bad_alloc);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void* operator new[](std:: size_t) throw(std:: bad_alloc);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void operator delete(void*) throw();
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void operator delete[](void*) throw();
# 267 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h" 1
# 99 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 100 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 101 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h" 2







extern "C"
{
# 192 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) int abs(int) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) long int labs(long int) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) long long int llabs(long long int) throw ();
# 244 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double fabs(double x) throw ();
# 285 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float fabsf(float x) throw ();



extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int min(int, int);

extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) unsigned int umin(unsigned int, unsigned int);
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long long int llmin(long long int, long long int);
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) unsigned long long int ullmin(unsigned long long int, unsigned long long int);
# 314 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float fminf(float x, float y) throw ();
# 334 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double fmin(double x, double y) throw ();






extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int max(int, int);

extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) unsigned int umax(unsigned int, unsigned int);
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long long int llmax(long long int, long long int);
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) unsigned long long int ullmax(unsigned long long int, unsigned long long int);
# 366 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float fmaxf(float x, float y) throw ();
# 386 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double fmax(double, double) throw ();
# 430 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double sin(double x) throw ();
# 463 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double cos(double x) throw ();
# 482 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) void sincos(double x, double *sptr, double *cptr) throw ();
# 498 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) void sincosf(float x, float *sptr, float *cptr) throw ();
# 543 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double tan(double x) throw ();
# 612 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double sqrt(double x) throw ();
# 684 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double rsqrt(double x);
# 754 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float rsqrtf(float x);
# 810 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double log2(double x) throw ();
# 835 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double exp2(double x) throw ();
# 860 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float exp2f(float x) throw ();
# 887 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double exp10(double x) throw ();
# 910 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float exp10f(float x) throw ();
# 956 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double expm1(double x) throw ();
# 1001 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float expm1f(float x) throw ();
# 1056 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float log2f(float x) throw ();
# 1110 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double log10(double x) throw ();
# 1181 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double log(double x) throw ();
# 1275 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double log1p(double x) throw ();
# 1372 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float log1pf(float x) throw ();
# 1436 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double floor(double x) throw ();
# 1475 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double exp(double x) throw ();
# 1506 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double cosh(double x) throw ();
# 1536 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double sinh(double x) throw ();
# 1566 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double tanh(double x) throw ();
# 1601 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double acosh(double x) throw ();
# 1639 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float acoshf(float x) throw ();
# 1655 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double asinh(double x) throw ();
# 1671 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float asinhf(float x) throw ();
# 1725 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double atanh(double x) throw ();
# 1779 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float atanhf(float x) throw ();
# 1838 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double ldexp(double x, int exp) throw ();
# 1894 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float ldexpf(float x, int exp) throw ();
# 1946 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double logb(double x) throw ();
# 2001 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float logbf(float x) throw ();
# 2031 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int ilogb(double x) throw ();
# 2061 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int ilogbf(float x) throw ();
# 2137 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double scalbn(double x, int n) throw ();
# 2213 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float scalbnf(float x, int n) throw ();
# 2289 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double scalbln(double x, long int n) throw ();
# 2365 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float scalblnf(float x, long int n) throw ();
# 2443 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double frexp(double x, int *nptr) throw ();
# 2518 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float frexpf(float x, int *nptr) throw ();
# 2532 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double round(double x) throw ();
# 2549 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float roundf(float x) throw ();
# 2567 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long int lround(double x) throw ();
# 2585 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long int lroundf(float x) throw ();
# 2603 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long long int llround(double x) throw ();
# 2621 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long long int llroundf(float x) throw ();
# 2657 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double rint(double x) throw ();
# 2673 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float rintf(float x) throw ();
# 2690 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long int lrint(double x) throw ();
# 2707 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long int lrintf(float x) throw ();
# 2724 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long long int llrint(double x) throw ();
# 2741 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) long long int llrintf(float x) throw ();
# 2794 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double nearbyint(double x) throw ();
# 2847 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float nearbyintf(float x) throw ();
# 2909 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double ceil(double x) throw ();
# 2921 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double trunc(double x) throw ();
# 2936 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float truncf(float x) throw ();
# 2962 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double fdim(double x, double y) throw ();
# 2988 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float fdimf(float x, float y) throw ();
# 3024 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double atan2(double y, double x) throw ();
# 3055 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double atan(double x) throw ();
# 3078 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double acos(double x) throw ();
# 3110 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double asin(double x) throw ();
# 3156 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double hypot(double x, double y) throw ();
# 3208 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double rhypot(double x, double y) throw ();
# 3254 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float hypotf(float x, float y) throw ();
# 3306 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float rhypotf(float x, float y) throw ();
# 3350 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double norm3d(double a, double b, double c) throw ();
# 3401 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double rnorm3d(double a, double b, double c) throw ();
# 3450 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double norm4d(double a, double b, double c, double d) throw ();
# 3506 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double rnorm4d(double a, double b, double c, double d) throw ();
# 3551 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double norm(int dim, double const * t) throw ();
# 3602 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double rnorm(int dim, double const * t) throw ();
# 3654 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float rnormf(int dim, float const * a) throw ();
# 3698 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float normf(int dim, float const * a) throw ();
# 3743 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float norm3df(float a, float b, float c) throw ();
# 3794 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float rnorm3df(float a, float b, float c) throw ();
# 3843 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float norm4df(float a, float b, float c, float d) throw ();
# 3899 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float rnorm4df(float a, float b, float c, float d) throw ();
# 3986 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double cbrt(double x) throw ();
# 4072 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float cbrtf(float x) throw ();
# 4127 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double rcbrt(double x);
# 4177 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float rcbrtf(float x);
# 4237 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double sinpi(double x);
# 4297 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float sinpif(float x);
# 4349 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double cospi(double x);
# 4401 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float cospif(float x);
# 4431 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) void sincospi(double x, double *sptr, double *cptr);
# 4461 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) void sincospif(float x, float *sptr, float *cptr);
# 4773 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double pow(double x, double y) throw ();
# 4829 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double modf(double x, double *iptr) throw ();
# 4888 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double fmod(double x, double y) throw ();
# 4974 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double remainder(double x, double y) throw ();
# 5064 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float remainderf(float x, float y) throw ();
# 5118 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double remquo(double x, double y, int *quo) throw ();
# 5172 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float remquof(float x, float y, int *quo) throw ();
# 5213 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double j0(double x) throw ();
# 5255 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float j0f(float x) throw ();
# 5316 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double j1(double x) throw ();
# 5377 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float j1f(float x) throw ();
# 5420 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double jn(int n, double x) throw ();
# 5463 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float jnf(int n, float x) throw ();
# 5515 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double y0(double x) throw ();
# 5567 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float y0f(float x) throw ();
# 5619 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double y1(double x) throw ();
# 5671 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float y1f(float x) throw ();
# 5724 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double yn(int n, double x) throw ();
# 5777 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float ynf(int n, float x) throw ();
# 5804 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double cyl_bessel_i0(double x) throw ();
# 5830 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float cyl_bessel_i0f(float x) throw ();
# 5857 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double cyl_bessel_i1(double x) throw ();
# 5883 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float cyl_bessel_i1f(float x) throw ();
# 5966 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double erf(double x) throw ();
# 6048 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float erff(float x) throw ();
# 6112 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double erfinv(double y);
# 6169 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float erfinvf(float y);
# 6208 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double erfc(double x) throw ();
# 6246 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float erfcf(float x) throw ();
# 6374 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double lgamma(double x) throw ();
# 6437 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double erfcinv(double y);
# 6493 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float erfcinvf(float y);
# 6551 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double normcdfinv(double y);
# 6609 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float normcdfinvf(float y);
# 6652 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double normcdf(double y);
# 6695 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float normcdff(float y);
# 6770 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double erfcx(double x);
# 6845 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float erfcxf(float x);
# 6979 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float lgammaf(float x) throw ();
# 7088 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double tgamma(double x) throw ();
# 7197 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float tgammaf(float x) throw ();
# 7210 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double copysign(double x, double y) throw ();
# 7223 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float copysignf(float x, float y) throw ();
# 7260 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double nextafter(double x, double y) throw ();
# 7297 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float nextafterf(float x, float y) throw ();
# 7313 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double nan(const char *tagp) throw ();
# 7329 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float nanf(const char *tagp) throw ();






extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __isinff(float) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __isnanf(float) throw ();
# 7347 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __finite(double) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __finitef(float) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __signbit(double) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __isnan(double) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __isinf(double) throw ();


extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __signbitf(float) throw ();
# 7513 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) double fma(double x, double y, double z) throw ();
# 7671 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float fmaf(float x, float y, float z) throw ();
# 7682 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __signbitl(long double) throw ();





extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __finitel(long double) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __isinfl(long double) throw ();
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) int __isnanl(long double) throw ();
# 7740 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float acosf(float x) throw ();
# 7780 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float asinf(float x) throw ();
# 7820 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float atanf(float x) throw ();
# 7853 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float atan2f(float y, float x) throw ();
# 7877 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float cosf(float x) throw ();
# 7919 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float sinf(float x) throw ();
# 7961 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float tanf(float x) throw ();
# 7985 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float coshf(float x) throw ();
# 8026 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float sinhf(float x) throw ();
# 8056 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float tanhf(float x) throw ();
# 8107 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float logf(float x) throw ();
# 8157 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float expf(float x) throw ();
# 8208 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float log10f(float x) throw ();
# 8263 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float modff(float x, float *iptr) throw ();
# 8571 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float powf(float x, float y) throw ();
# 8640 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float sqrtf(float x) throw ();
# 8699 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float ceilf(float x) throw ();
# 8760 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float floorf(float x) throw ();
# 8819 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((device_builtin)) float fmodf(float x, float y) throw ();
# 8834 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
}


# 1 "/usr/include/math.h" 1 3 4
# 29 "/usr/include/math.h" 3 4
extern "C" {



# 1 "/usr/include/bits/huge_val.h" 1 3 4
# 34 "/usr/include/math.h" 2 3 4

# 1 "/usr/include/bits/huge_valf.h" 1 3 4
# 36 "/usr/include/math.h" 2 3 4
# 1 "/usr/include/bits/huge_vall.h" 1 3 4
# 37 "/usr/include/math.h" 2 3 4


# 1 "/usr/include/bits/inf.h" 1 3 4
# 40 "/usr/include/math.h" 2 3 4


# 1 "/usr/include/bits/nan.h" 1 3 4
# 43 "/usr/include/math.h" 2 3 4



# 1 "/usr/include/bits/mathdef.h" 1 3 4
# 28 "/usr/include/bits/mathdef.h" 3 4
typedef float float_t;
typedef double double_t;
# 47 "/usr/include/math.h" 2 3 4
# 70 "/usr/include/math.h" 3 4
# 1 "/usr/include/bits/mathcalls.h" 1 3 4
# 52 "/usr/include/bits/mathcalls.h" 3 4


extern double acos (double __x) throw (); extern double __acos (double __x) throw ();

extern double asin (double __x) throw (); extern double __asin (double __x) throw ();

extern double atan (double __x) throw (); extern double __atan (double __x) throw ();

extern double atan2 (double __y, double __x) throw (); extern double __atan2 (double __y, double __x) throw ();


extern double cos (double __x) throw (); extern double __cos (double __x) throw ();

extern double sin (double __x) throw (); extern double __sin (double __x) throw ();

extern double tan (double __x) throw (); extern double __tan (double __x) throw ();




extern double cosh (double __x) throw (); extern double __cosh (double __x) throw ();

extern double sinh (double __x) throw (); extern double __sinh (double __x) throw ();

extern double tanh (double __x) throw (); extern double __tanh (double __x) throw ();




extern void sincos (double __x, double *__sinx, double *__cosx) throw (); extern void __sincos (double __x, double *__sinx, double *__cosx) throw ()
                                                           ;





extern double acosh (double __x) throw (); extern double __acosh (double __x) throw ();

extern double asinh (double __x) throw (); extern double __asinh (double __x) throw ();

extern double atanh (double __x) throw (); extern double __atanh (double __x) throw ();







extern double exp (double __x) throw (); extern double __exp (double __x) throw ();


extern double frexp (double __x, int *__exponent) throw (); extern double __frexp (double __x, int *__exponent) throw ();


extern double ldexp (double __x, int __exponent) throw (); extern double __ldexp (double __x, int __exponent) throw ();


extern double log (double __x) throw (); extern double __log (double __x) throw ();


extern double log10 (double __x) throw (); extern double __log10 (double __x) throw ();


extern double modf (double __x, double *__iptr) throw (); extern double __modf (double __x, double *__iptr) throw ()
     __attribute__ ((__nonnull__ (2)));




extern double exp10 (double __x) throw (); extern double __exp10 (double __x) throw ();

extern double pow10 (double __x) throw (); extern double __pow10 (double __x) throw ();





extern double expm1 (double __x) throw (); extern double __expm1 (double __x) throw ();


extern double log1p (double __x) throw (); extern double __log1p (double __x) throw ();


extern double logb (double __x) throw (); extern double __logb (double __x) throw ();






extern double exp2 (double __x) throw (); extern double __exp2 (double __x) throw ();


extern double log2 (double __x) throw (); extern double __log2 (double __x) throw ();








extern double pow (double __x, double __y) throw (); extern double __pow (double __x, double __y) throw ();


extern double sqrt (double __x) throw (); extern double __sqrt (double __x) throw ();





extern double hypot (double __x, double __y) throw (); extern double __hypot (double __x, double __y) throw ();






extern double cbrt (double __x) throw (); extern double __cbrt (double __x) throw ();








extern double ceil (double __x) throw () __attribute__ ((__const__)); extern double __ceil (double __x) throw () __attribute__ ((__const__));


extern double fabs (double __x) throw () __attribute__ ((__const__)); extern double __fabs (double __x) throw () __attribute__ ((__const__));


extern double floor (double __x) throw () __attribute__ ((__const__)); extern double __floor (double __x) throw () __attribute__ ((__const__));


extern double fmod (double __x, double __y) throw (); extern double __fmod (double __x, double __y) throw ();




extern int __isinf (double __value) throw () __attribute__ ((__const__));


extern int __finite (double __value) throw () __attribute__ ((__const__));





extern int isinf (double __value) throw () __attribute__ ((__const__));


extern int finite (double __value) throw () __attribute__ ((__const__));


extern double drem (double __x, double __y) throw (); extern double __drem (double __x, double __y) throw ();



extern double significand (double __x) throw (); extern double __significand (double __x) throw ();





extern double copysign (double __x, double __y) throw () __attribute__ ((__const__)); extern double __copysign (double __x, double __y) throw () __attribute__ ((__const__));






extern double nan (const char *__tagb) throw () __attribute__ ((__const__)); extern double __nan (const char *__tagb) throw () __attribute__ ((__const__));





extern int __isnan (double __value) throw () __attribute__ ((__const__));



extern int isnan (double __value) throw () __attribute__ ((__const__));


extern double j0 (double) throw (); extern double __j0 (double) throw ();
extern double j1 (double) throw (); extern double __j1 (double) throw ();
extern double jn (int, double) throw (); extern double __jn (int, double) throw ();
extern double y0 (double) throw (); extern double __y0 (double) throw ();
extern double y1 (double) throw (); extern double __y1 (double) throw ();
extern double yn (int, double) throw (); extern double __yn (int, double) throw ();






extern double erf (double) throw (); extern double __erf (double) throw ();
extern double erfc (double) throw (); extern double __erfc (double) throw ();
extern double lgamma (double) throw (); extern double __lgamma (double) throw ();






extern double tgamma (double) throw (); extern double __tgamma (double) throw ();





extern double gamma (double) throw (); extern double __gamma (double) throw ();






extern double lgamma_r (double, int *__signgamp) throw (); extern double __lgamma_r (double, int *__signgamp) throw ();







extern double rint (double __x) throw (); extern double __rint (double __x) throw ();


extern double nextafter (double __x, double __y) throw () __attribute__ ((__const__)); extern double __nextafter (double __x, double __y) throw () __attribute__ ((__const__));

extern double nexttoward (double __x, long double __y) throw () __attribute__ ((__const__)); extern double __nexttoward (double __x, long double __y) throw () __attribute__ ((__const__));



extern double remainder (double __x, double __y) throw (); extern double __remainder (double __x, double __y) throw ();



extern double scalbn (double __x, int __n) throw (); extern double __scalbn (double __x, int __n) throw ();



extern int ilogb (double __x) throw (); extern int __ilogb (double __x) throw ();




extern double scalbln (double __x, long int __n) throw (); extern double __scalbln (double __x, long int __n) throw ();



extern double nearbyint (double __x) throw (); extern double __nearbyint (double __x) throw ();



extern double round (double __x) throw () __attribute__ ((__const__)); extern double __round (double __x) throw () __attribute__ ((__const__));



extern double trunc (double __x) throw () __attribute__ ((__const__)); extern double __trunc (double __x) throw () __attribute__ ((__const__));




extern double remquo (double __x, double __y, int *__quo) throw (); extern double __remquo (double __x, double __y, int *__quo) throw ();






extern long int lrint (double __x) throw (); extern long int __lrint (double __x) throw ();
extern long long int llrint (double __x) throw (); extern long long int __llrint (double __x) throw ();



extern long int lround (double __x) throw (); extern long int __lround (double __x) throw ();
extern long long int llround (double __x) throw (); extern long long int __llround (double __x) throw ();



extern double fdim (double __x, double __y) throw (); extern double __fdim (double __x, double __y) throw ();


extern double fmax (double __x, double __y) throw () __attribute__ ((__const__)); extern double __fmax (double __x, double __y) throw () __attribute__ ((__const__));


extern double fmin (double __x, double __y) throw () __attribute__ ((__const__)); extern double __fmin (double __x, double __y) throw () __attribute__ ((__const__));



extern int __fpclassify (double __value) throw ()
     __attribute__ ((__const__));


extern int __signbit (double __value) throw ()
     __attribute__ ((__const__));



extern double fma (double __x, double __y, double __z) throw (); extern double __fma (double __x, double __y, double __z) throw ();








extern double scalb (double __x, double __n) throw (); extern double __scalb (double __x, double __n) throw ();
# 71 "/usr/include/math.h" 2 3 4
# 89 "/usr/include/math.h" 3 4
# 1 "/usr/include/bits/mathcalls.h" 1 3 4
# 52 "/usr/include/bits/mathcalls.h" 3 4


extern float acosf (float __x) throw (); extern float __acosf (float __x) throw ();

extern float asinf (float __x) throw (); extern float __asinf (float __x) throw ();

extern float atanf (float __x) throw (); extern float __atanf (float __x) throw ();

extern float atan2f (float __y, float __x) throw (); extern float __atan2f (float __y, float __x) throw ();


extern float cosf (float __x) throw (); extern float __cosf (float __x) throw ();

extern float sinf (float __x) throw (); extern float __sinf (float __x) throw ();

extern float tanf (float __x) throw (); extern float __tanf (float __x) throw ();




extern float coshf (float __x) throw (); extern float __coshf (float __x) throw ();

extern float sinhf (float __x) throw (); extern float __sinhf (float __x) throw ();

extern float tanhf (float __x) throw (); extern float __tanhf (float __x) throw ();




extern void sincosf (float __x, float *__sinx, float *__cosx) throw (); extern void __sincosf (float __x, float *__sinx, float *__cosx) throw ()
                                                           ;





extern float acoshf (float __x) throw (); extern float __acoshf (float __x) throw ();

extern float asinhf (float __x) throw (); extern float __asinhf (float __x) throw ();

extern float atanhf (float __x) throw (); extern float __atanhf (float __x) throw ();







extern float expf (float __x) throw (); extern float __expf (float __x) throw ();


extern float frexpf (float __x, int *__exponent) throw (); extern float __frexpf (float __x, int *__exponent) throw ();


extern float ldexpf (float __x, int __exponent) throw (); extern float __ldexpf (float __x, int __exponent) throw ();


extern float logf (float __x) throw (); extern float __logf (float __x) throw ();


extern float log10f (float __x) throw (); extern float __log10f (float __x) throw ();


extern float modff (float __x, float *__iptr) throw (); extern float __modff (float __x, float *__iptr) throw ()
     __attribute__ ((__nonnull__ (2)));




extern float exp10f (float __x) throw (); extern float __exp10f (float __x) throw ();

extern float pow10f (float __x) throw (); extern float __pow10f (float __x) throw ();





extern float expm1f (float __x) throw (); extern float __expm1f (float __x) throw ();


extern float log1pf (float __x) throw (); extern float __log1pf (float __x) throw ();


extern float logbf (float __x) throw (); extern float __logbf (float __x) throw ();






extern float exp2f (float __x) throw (); extern float __exp2f (float __x) throw ();


extern float log2f (float __x) throw (); extern float __log2f (float __x) throw ();








extern float powf (float __x, float __y) throw (); extern float __powf (float __x, float __y) throw ();


extern float sqrtf (float __x) throw (); extern float __sqrtf (float __x) throw ();





extern float hypotf (float __x, float __y) throw (); extern float __hypotf (float __x, float __y) throw ();






extern float cbrtf (float __x) throw (); extern float __cbrtf (float __x) throw ();








extern float ceilf (float __x) throw () __attribute__ ((__const__)); extern float __ceilf (float __x) throw () __attribute__ ((__const__));


extern float fabsf (float __x) throw () __attribute__ ((__const__)); extern float __fabsf (float __x) throw () __attribute__ ((__const__));


extern float floorf (float __x) throw () __attribute__ ((__const__)); extern float __floorf (float __x) throw () __attribute__ ((__const__));


extern float fmodf (float __x, float __y) throw (); extern float __fmodf (float __x, float __y) throw ();




extern int __isinff (float __value) throw () __attribute__ ((__const__));


extern int __finitef (float __value) throw () __attribute__ ((__const__));





extern int isinff (float __value) throw () __attribute__ ((__const__));


extern int finitef (float __value) throw () __attribute__ ((__const__));


extern float dremf (float __x, float __y) throw (); extern float __dremf (float __x, float __y) throw ();



extern float significandf (float __x) throw (); extern float __significandf (float __x) throw ();





extern float copysignf (float __x, float __y) throw () __attribute__ ((__const__)); extern float __copysignf (float __x, float __y) throw () __attribute__ ((__const__));






extern float nanf (const char *__tagb) throw () __attribute__ ((__const__)); extern float __nanf (const char *__tagb) throw () __attribute__ ((__const__));





extern int __isnanf (float __value) throw () __attribute__ ((__const__));



extern int isnanf (float __value) throw () __attribute__ ((__const__));


extern float j0f (float) throw (); extern float __j0f (float) throw ();
extern float j1f (float) throw (); extern float __j1f (float) throw ();
extern float jnf (int, float) throw (); extern float __jnf (int, float) throw ();
extern float y0f (float) throw (); extern float __y0f (float) throw ();
extern float y1f (float) throw (); extern float __y1f (float) throw ();
extern float ynf (int, float) throw (); extern float __ynf (int, float) throw ();






extern float erff (float) throw (); extern float __erff (float) throw ();
extern float erfcf (float) throw (); extern float __erfcf (float) throw ();
extern float lgammaf (float) throw (); extern float __lgammaf (float) throw ();






extern float tgammaf (float) throw (); extern float __tgammaf (float) throw ();





extern float gammaf (float) throw (); extern float __gammaf (float) throw ();






extern float lgammaf_r (float, int *__signgamp) throw (); extern float __lgammaf_r (float, int *__signgamp) throw ();







extern float rintf (float __x) throw (); extern float __rintf (float __x) throw ();


extern float nextafterf (float __x, float __y) throw () __attribute__ ((__const__)); extern float __nextafterf (float __x, float __y) throw () __attribute__ ((__const__));

extern float nexttowardf (float __x, long double __y) throw () __attribute__ ((__const__)); extern float __nexttowardf (float __x, long double __y) throw () __attribute__ ((__const__));



extern float remainderf (float __x, float __y) throw (); extern float __remainderf (float __x, float __y) throw ();



extern float scalbnf (float __x, int __n) throw (); extern float __scalbnf (float __x, int __n) throw ();



extern int ilogbf (float __x) throw (); extern int __ilogbf (float __x) throw ();




extern float scalblnf (float __x, long int __n) throw (); extern float __scalblnf (float __x, long int __n) throw ();



extern float nearbyintf (float __x) throw (); extern float __nearbyintf (float __x) throw ();



extern float roundf (float __x) throw () __attribute__ ((__const__)); extern float __roundf (float __x) throw () __attribute__ ((__const__));



extern float truncf (float __x) throw () __attribute__ ((__const__)); extern float __truncf (float __x) throw () __attribute__ ((__const__));




extern float remquof (float __x, float __y, int *__quo) throw (); extern float __remquof (float __x, float __y, int *__quo) throw ();






extern long int lrintf (float __x) throw (); extern long int __lrintf (float __x) throw ();
extern long long int llrintf (float __x) throw (); extern long long int __llrintf (float __x) throw ();



extern long int lroundf (float __x) throw (); extern long int __lroundf (float __x) throw ();
extern long long int llroundf (float __x) throw (); extern long long int __llroundf (float __x) throw ();



extern float fdimf (float __x, float __y) throw (); extern float __fdimf (float __x, float __y) throw ();


extern float fmaxf (float __x, float __y) throw () __attribute__ ((__const__)); extern float __fmaxf (float __x, float __y) throw () __attribute__ ((__const__));


extern float fminf (float __x, float __y) throw () __attribute__ ((__const__)); extern float __fminf (float __x, float __y) throw () __attribute__ ((__const__));



extern int __fpclassifyf (float __value) throw ()
     __attribute__ ((__const__));


extern int __signbitf (float __value) throw ()
     __attribute__ ((__const__));



extern float fmaf (float __x, float __y, float __z) throw (); extern float __fmaf (float __x, float __y, float __z) throw ();








extern float scalbf (float __x, float __n) throw (); extern float __scalbf (float __x, float __n) throw ();
# 90 "/usr/include/math.h" 2 3 4
# 133 "/usr/include/math.h" 3 4
# 1 "/usr/include/bits/mathcalls.h" 1 3 4
# 52 "/usr/include/bits/mathcalls.h" 3 4


extern long double acosl (long double __x) throw (); extern long double __acosl (long double __x) throw ();

extern long double asinl (long double __x) throw (); extern long double __asinl (long double __x) throw ();

extern long double atanl (long double __x) throw (); extern long double __atanl (long double __x) throw ();

extern long double atan2l (long double __y, long double __x) throw (); extern long double __atan2l (long double __y, long double __x) throw ();


extern long double cosl (long double __x) throw (); extern long double __cosl (long double __x) throw ();

extern long double sinl (long double __x) throw (); extern long double __sinl (long double __x) throw ();

extern long double tanl (long double __x) throw (); extern long double __tanl (long double __x) throw ();




extern long double coshl (long double __x) throw (); extern long double __coshl (long double __x) throw ();

extern long double sinhl (long double __x) throw (); extern long double __sinhl (long double __x) throw ();

extern long double tanhl (long double __x) throw (); extern long double __tanhl (long double __x) throw ();




extern void sincosl (long double __x, long double *__sinx, long double *__cosx) throw (); extern void __sincosl (long double __x, long double *__sinx, long double *__cosx) throw ()
                                                           ;





extern long double acoshl (long double __x) throw (); extern long double __acoshl (long double __x) throw ();

extern long double asinhl (long double __x) throw (); extern long double __asinhl (long double __x) throw ();

extern long double atanhl (long double __x) throw (); extern long double __atanhl (long double __x) throw ();







extern long double expl (long double __x) throw (); extern long double __expl (long double __x) throw ();


extern long double frexpl (long double __x, int *__exponent) throw (); extern long double __frexpl (long double __x, int *__exponent) throw ();


extern long double ldexpl (long double __x, int __exponent) throw (); extern long double __ldexpl (long double __x, int __exponent) throw ();


extern long double logl (long double __x) throw (); extern long double __logl (long double __x) throw ();


extern long double log10l (long double __x) throw (); extern long double __log10l (long double __x) throw ();


extern long double modfl (long double __x, long double *__iptr) throw (); extern long double __modfl (long double __x, long double *__iptr) throw ()
     __attribute__ ((__nonnull__ (2)));




extern long double exp10l (long double __x) throw (); extern long double __exp10l (long double __x) throw ();

extern long double pow10l (long double __x) throw (); extern long double __pow10l (long double __x) throw ();





extern long double expm1l (long double __x) throw (); extern long double __expm1l (long double __x) throw ();


extern long double log1pl (long double __x) throw (); extern long double __log1pl (long double __x) throw ();


extern long double logbl (long double __x) throw (); extern long double __logbl (long double __x) throw ();






extern long double exp2l (long double __x) throw (); extern long double __exp2l (long double __x) throw ();


extern long double log2l (long double __x) throw (); extern long double __log2l (long double __x) throw ();








extern long double powl (long double __x, long double __y) throw (); extern long double __powl (long double __x, long double __y) throw ();


extern long double sqrtl (long double __x) throw (); extern long double __sqrtl (long double __x) throw ();





extern long double hypotl (long double __x, long double __y) throw (); extern long double __hypotl (long double __x, long double __y) throw ();






extern long double cbrtl (long double __x) throw (); extern long double __cbrtl (long double __x) throw ();








extern long double ceill (long double __x) throw () __attribute__ ((__const__)); extern long double __ceill (long double __x) throw () __attribute__ ((__const__));


extern long double fabsl (long double __x) throw () __attribute__ ((__const__)); extern long double __fabsl (long double __x) throw () __attribute__ ((__const__));


extern long double floorl (long double __x) throw () __attribute__ ((__const__)); extern long double __floorl (long double __x) throw () __attribute__ ((__const__));


extern long double fmodl (long double __x, long double __y) throw (); extern long double __fmodl (long double __x, long double __y) throw ();




extern int __isinfl (long double __value) throw () __attribute__ ((__const__));


extern int __finitel (long double __value) throw () __attribute__ ((__const__));





extern int isinfl (long double __value) throw () __attribute__ ((__const__));


extern int finitel (long double __value) throw () __attribute__ ((__const__));


extern long double dreml (long double __x, long double __y) throw (); extern long double __dreml (long double __x, long double __y) throw ();



extern long double significandl (long double __x) throw (); extern long double __significandl (long double __x) throw ();





extern long double copysignl (long double __x, long double __y) throw () __attribute__ ((__const__)); extern long double __copysignl (long double __x, long double __y) throw () __attribute__ ((__const__));






extern long double nanl (const char *__tagb) throw () __attribute__ ((__const__)); extern long double __nanl (const char *__tagb) throw () __attribute__ ((__const__));





extern int __isnanl (long double __value) throw () __attribute__ ((__const__));



extern int isnanl (long double __value) throw () __attribute__ ((__const__));


extern long double j0l (long double) throw (); extern long double __j0l (long double) throw ();
extern long double j1l (long double) throw (); extern long double __j1l (long double) throw ();
extern long double jnl (int, long double) throw (); extern long double __jnl (int, long double) throw ();
extern long double y0l (long double) throw (); extern long double __y0l (long double) throw ();
extern long double y1l (long double) throw (); extern long double __y1l (long double) throw ();
extern long double ynl (int, long double) throw (); extern long double __ynl (int, long double) throw ();






extern long double erfl (long double) throw (); extern long double __erfl (long double) throw ();
extern long double erfcl (long double) throw (); extern long double __erfcl (long double) throw ();
extern long double lgammal (long double) throw (); extern long double __lgammal (long double) throw ();






extern long double tgammal (long double) throw (); extern long double __tgammal (long double) throw ();





extern long double gammal (long double) throw (); extern long double __gammal (long double) throw ();






extern long double lgammal_r (long double, int *__signgamp) throw (); extern long double __lgammal_r (long double, int *__signgamp) throw ();







extern long double rintl (long double __x) throw (); extern long double __rintl (long double __x) throw ();


extern long double nextafterl (long double __x, long double __y) throw () __attribute__ ((__const__)); extern long double __nextafterl (long double __x, long double __y) throw () __attribute__ ((__const__));

extern long double nexttowardl (long double __x, long double __y) throw () __attribute__ ((__const__)); extern long double __nexttowardl (long double __x, long double __y) throw () __attribute__ ((__const__));



extern long double remainderl (long double __x, long double __y) throw (); extern long double __remainderl (long double __x, long double __y) throw ();



extern long double scalbnl (long double __x, int __n) throw (); extern long double __scalbnl (long double __x, int __n) throw ();



extern int ilogbl (long double __x) throw (); extern int __ilogbl (long double __x) throw ();




extern long double scalblnl (long double __x, long int __n) throw (); extern long double __scalblnl (long double __x, long int __n) throw ();



extern long double nearbyintl (long double __x) throw (); extern long double __nearbyintl (long double __x) throw ();



extern long double roundl (long double __x) throw () __attribute__ ((__const__)); extern long double __roundl (long double __x) throw () __attribute__ ((__const__));



extern long double truncl (long double __x) throw () __attribute__ ((__const__)); extern long double __truncl (long double __x) throw () __attribute__ ((__const__));




extern long double remquol (long double __x, long double __y, int *__quo) throw (); extern long double __remquol (long double __x, long double __y, int *__quo) throw ();






extern long int lrintl (long double __x) throw (); extern long int __lrintl (long double __x) throw ();
extern long long int llrintl (long double __x) throw (); extern long long int __llrintl (long double __x) throw ();



extern long int lroundl (long double __x) throw (); extern long int __lroundl (long double __x) throw ();
extern long long int llroundl (long double __x) throw (); extern long long int __llroundl (long double __x) throw ();



extern long double fdiml (long double __x, long double __y) throw (); extern long double __fdiml (long double __x, long double __y) throw ();


extern long double fmaxl (long double __x, long double __y) throw () __attribute__ ((__const__)); extern long double __fmaxl (long double __x, long double __y) throw () __attribute__ ((__const__));


extern long double fminl (long double __x, long double __y) throw () __attribute__ ((__const__)); extern long double __fminl (long double __x, long double __y) throw () __attribute__ ((__const__));



extern int __fpclassifyl (long double __value) throw ()
     __attribute__ ((__const__));


extern int __signbitl (long double __value) throw ()
     __attribute__ ((__const__));



extern long double fmal (long double __x, long double __y, long double __z) throw (); extern long double __fmal (long double __x, long double __y, long double __z) throw ();








extern long double scalbl (long double __x, long double __n) throw (); extern long double __scalbl (long double __x, long double __n) throw ();
# 134 "/usr/include/math.h" 2 3 4
# 149 "/usr/include/math.h" 3 4
extern int signgam;
# 190 "/usr/include/math.h" 3 4
enum
  {
    FP_NAN =

      0,
    FP_INFINITE =

      1,
    FP_ZERO =

      2,
    FP_SUBNORMAL =

      3,
    FP_NORMAL =

      4
  };
# 288 "/usr/include/math.h" 3 4
typedef enum
{
  _IEEE_ = -1,
  _SVID_,
  _XOPEN_,
  _POSIX_,
  _ISOC_
} _LIB_VERSION_TYPE;




extern _LIB_VERSION_TYPE _LIB_VERSION;
# 311 "/usr/include/math.h" 3 4
struct __exception



  {
    int type;
    char *name;
    double arg1;
    double arg2;
    double retval;
  };


extern int matherr (struct __exception *__exc) throw ();
# 475 "/usr/include/math.h" 3 4
}
# 8838 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h" 2



# 1 "/usr/include/c++/4.8.2/cmath" 1 3
# 39 "/usr/include/c++/4.8.2/cmath" 3
       
# 40 "/usr/include/c++/4.8.2/cmath" 3


# 1 "/usr/include/c++/4.8.2/bits/cpp_type_traits.h" 1 3
# 35 "/usr/include/c++/4.8.2/bits/cpp_type_traits.h" 3
       
# 36 "/usr/include/c++/4.8.2/bits/cpp_type_traits.h" 3
# 68 "/usr/include/c++/4.8.2/bits/cpp_type_traits.h" 3
namespace __gnu_cxx __attribute__ ((__visibility__ ("default")))
{


  template<typename _Iterator, typename _Container>
    class __normal_iterator;


}

namespace std __attribute__ ((__visibility__ ("default")))
{


  struct __true_type { };
  struct __false_type { };

  template<bool>
    struct __truth_type
    { typedef __false_type __type; };

  template<>
    struct __truth_type<true>
    { typedef __true_type __type; };



  template<class _Sp, class _Tp>
    struct __traitor
    {
      enum { __value = bool(_Sp::__value) || bool(_Tp::__value) };
      typedef typename __truth_type<__value>::__type __type;
    };


  template<typename, typename>
    struct __are_same
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };

  template<typename _Tp>
    struct __are_same<_Tp, _Tp>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };


  template<typename _Tp>
    struct __is_void
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };

  template<>
    struct __is_void<void>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };




  template<typename _Tp>
    struct __is_integer
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };




  template<>
    struct __is_integer<bool>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<char>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<signed char>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<unsigned char>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };


  template<>
    struct __is_integer<wchar_t>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };
# 198 "/usr/include/c++/4.8.2/bits/cpp_type_traits.h" 3
  template<>
    struct __is_integer<short>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<unsigned short>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<int>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<unsigned int>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<long>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<unsigned long>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<long long>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_integer<unsigned long long>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };




  template<typename _Tp>
    struct __is_floating
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };


  template<>
    struct __is_floating<float>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_floating<double>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_floating<long double>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };




  template<typename _Tp>
    struct __is_pointer
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };

  template<typename _Tp>
    struct __is_pointer<_Tp*>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };




  template<typename _Tp>
    struct __is_normal_iterator
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };

  template<typename _Iterator, typename _Container>
    struct __is_normal_iterator< __gnu_cxx::__normal_iterator<_Iterator,
             _Container> >
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };




  template<typename _Tp>
    struct __is_arithmetic
    : public __traitor<__is_integer<_Tp>, __is_floating<_Tp> >
    { };




  template<typename _Tp>
    struct __is_fundamental
    : public __traitor<__is_void<_Tp>, __is_arithmetic<_Tp> >
    { };




  template<typename _Tp>
    struct __is_scalar
    : public __traitor<__is_arithmetic<_Tp>, __is_pointer<_Tp> >
    { };




  template<typename _Tp>
    struct __is_char
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };

  template<>
    struct __is_char<char>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };


  template<>
    struct __is_char<wchar_t>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };


  template<typename _Tp>
    struct __is_byte
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };

  template<>
    struct __is_byte<char>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_byte<signed char>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };

  template<>
    struct __is_byte<unsigned char>
    {
      enum { __value = 1 };
      typedef __true_type __type;
    };




  template<typename _Tp>
    struct __is_move_iterator
    {
      enum { __value = 0 };
      typedef __false_type __type;
    };
# 421 "/usr/include/c++/4.8.2/bits/cpp_type_traits.h" 3

}
# 43 "/usr/include/c++/4.8.2/cmath" 2 3
# 1 "/usr/include/c++/4.8.2/ext/type_traits.h" 1 3
# 32 "/usr/include/c++/4.8.2/ext/type_traits.h" 3
       
# 33 "/usr/include/c++/4.8.2/ext/type_traits.h" 3




namespace __gnu_cxx __attribute__ ((__visibility__ ("default")))
{



  template<bool, typename>
    struct __enable_if
    { };

  template<typename _Tp>
    struct __enable_if<true, _Tp>
    { typedef _Tp __type; };



  template<bool _Cond, typename _Iftrue, typename _Iffalse>
    struct __conditional_type
    { typedef _Iftrue __type; };

  template<typename _Iftrue, typename _Iffalse>
    struct __conditional_type<false, _Iftrue, _Iffalse>
    { typedef _Iffalse __type; };



  template<typename _Tp>
    struct __add_unsigned
    {
    private:
      typedef __enable_if<std::__is_integer<_Tp>::__value, _Tp> __if_type;

    public:
      typedef typename __if_type::__type __type;
    };

  template<>
    struct __add_unsigned<char>
    { typedef unsigned char __type; };

  template<>
    struct __add_unsigned<signed char>
    { typedef unsigned char __type; };

  template<>
    struct __add_unsigned<short>
    { typedef unsigned short __type; };

  template<>
    struct __add_unsigned<int>
    { typedef unsigned int __type; };

  template<>
    struct __add_unsigned<long>
    { typedef unsigned long __type; };

  template<>
    struct __add_unsigned<long long>
    { typedef unsigned long long __type; };


  template<>
    struct __add_unsigned<bool>;

  template<>
    struct __add_unsigned<wchar_t>;



  template<typename _Tp>
    struct __remove_unsigned
    {
    private:
      typedef __enable_if<std::__is_integer<_Tp>::__value, _Tp> __if_type;

    public:
      typedef typename __if_type::__type __type;
    };

  template<>
    struct __remove_unsigned<char>
    { typedef signed char __type; };

  template<>
    struct __remove_unsigned<unsigned char>
    { typedef signed char __type; };

  template<>
    struct __remove_unsigned<unsigned short>
    { typedef short __type; };

  template<>
    struct __remove_unsigned<unsigned int>
    { typedef int __type; };

  template<>
    struct __remove_unsigned<unsigned long>
    { typedef long __type; };

  template<>
    struct __remove_unsigned<unsigned long long>
    { typedef long long __type; };


  template<>
    struct __remove_unsigned<bool>;

  template<>
    struct __remove_unsigned<wchar_t>;



  template<typename _Type>
    inline bool
    __is_null_pointer(_Type* __ptr)
    { return __ptr == 0; }

  template<typename _Type>
    inline bool
    __is_null_pointer(_Type)
    { return false; }



  template<typename _Tp, bool = std::__is_integer<_Tp>::__value>
    struct __promote
    { typedef double __type; };




  template<typename _Tp>
    struct __promote<_Tp, false>
    { };

  template<>
    struct __promote<long double>
    { typedef long double __type; };

  template<>
    struct __promote<double>
    { typedef double __type; };

  template<>
    struct __promote<float>
    { typedef float __type; };

  template<typename _Tp, typename _Up,
           typename _Tp2 = typename __promote<_Tp>::__type,
           typename _Up2 = typename __promote<_Up>::__type>
    struct __promote_2
    {
      typedef __typeof__(_Tp2() + _Up2()) __type;
    };

  template<typename _Tp, typename _Up, typename _Vp,
           typename _Tp2 = typename __promote<_Tp>::__type,
           typename _Up2 = typename __promote<_Up>::__type,
           typename _Vp2 = typename __promote<_Vp>::__type>
    struct __promote_3
    {
      typedef __typeof__(_Tp2() + _Up2() + _Vp2()) __type;
    };

  template<typename _Tp, typename _Up, typename _Vp, typename _Wp,
           typename _Tp2 = typename __promote<_Tp>::__type,
           typename _Up2 = typename __promote<_Up>::__type,
           typename _Vp2 = typename __promote<_Vp>::__type,
           typename _Wp2 = typename __promote<_Wp>::__type>
    struct __promote_4
    {
      typedef __typeof__(_Tp2() + _Up2() + _Vp2() + _Wp2()) __type;
    };


}
# 44 "/usr/include/c++/4.8.2/cmath" 2 3
# 75 "/usr/include/c++/4.8.2/cmath" 3
namespace std __attribute__ ((__visibility__ ("default")))
{



  inline double
  abs(double __x)
  { return __builtin_fabs(__x); }



  inline float
  abs(float __x)
  { return __builtin_fabsf(__x); }

  inline long double
  abs(long double __x)
  { return __builtin_fabsl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    abs(_Tp __x)
    { return __builtin_fabs(__x); }

  using ::acos;


  inline float
  acos(float __x)
  { return __builtin_acosf(__x); }

  inline long double
  acos(long double __x)
  { return __builtin_acosl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    acos(_Tp __x)
    { return __builtin_acos(__x); }

  using ::asin;


  inline float
  asin(float __x)
  { return __builtin_asinf(__x); }

  inline long double
  asin(long double __x)
  { return __builtin_asinl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    asin(_Tp __x)
    { return __builtin_asin(__x); }

  using ::atan;


  inline float
  atan(float __x)
  { return __builtin_atanf(__x); }

  inline long double
  atan(long double __x)
  { return __builtin_atanl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    atan(_Tp __x)
    { return __builtin_atan(__x); }

  using ::atan2;


  inline float
  atan2(float __y, float __x)
  { return __builtin_atan2f(__y, __x); }

  inline long double
  atan2(long double __y, long double __x)
  { return __builtin_atan2l(__y, __x); }


  template<typename _Tp, typename _Up>
    inline
    typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    atan2(_Tp __y, _Up __x)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return atan2(__type(__y), __type(__x));
    }

  using ::ceil;


  inline float
  ceil(float __x)
  { return __builtin_ceilf(__x); }

  inline long double
  ceil(long double __x)
  { return __builtin_ceill(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    ceil(_Tp __x)
    { return __builtin_ceil(__x); }

  using ::cos;


  inline float
  cos(float __x)
  { return __builtin_cosf(__x); }

  inline long double
  cos(long double __x)
  { return __builtin_cosl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    cos(_Tp __x)
    { return __builtin_cos(__x); }

  using ::cosh;


  inline float
  cosh(float __x)
  { return __builtin_coshf(__x); }

  inline long double
  cosh(long double __x)
  { return __builtin_coshl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    cosh(_Tp __x)
    { return __builtin_cosh(__x); }

  using ::exp;


  inline float
  exp(float __x)
  { return __builtin_expf(__x); }

  inline long double
  exp(long double __x)
  { return __builtin_expl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    exp(_Tp __x)
    { return __builtin_exp(__x); }

  using ::fabs;


  inline float
  fabs(float __x)
  { return __builtin_fabsf(__x); }

  inline long double
  fabs(long double __x)
  { return __builtin_fabsl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    fabs(_Tp __x)
    { return __builtin_fabs(__x); }

  using ::floor;


  inline float
  floor(float __x)
  { return __builtin_floorf(__x); }

  inline long double
  floor(long double __x)
  { return __builtin_floorl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    floor(_Tp __x)
    { return __builtin_floor(__x); }

  using ::fmod;


  inline float
  fmod(float __x, float __y)
  { return __builtin_fmodf(__x, __y); }

  inline long double
  fmod(long double __x, long double __y)
  { return __builtin_fmodl(__x, __y); }


  template<typename _Tp, typename _Up>
    inline
    typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    fmod(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return fmod(__type(__x), __type(__y));
    }

  using ::frexp;


  inline float
  frexp(float __x, int* __exp)
  { return __builtin_frexpf(__x, __exp); }

  inline long double
  frexp(long double __x, int* __exp)
  { return __builtin_frexpl(__x, __exp); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    frexp(_Tp __x, int* __exp)
    { return __builtin_frexp(__x, __exp); }

  using ::ldexp;


  inline float
  ldexp(float __x, int __exp)
  { return __builtin_ldexpf(__x, __exp); }

  inline long double
  ldexp(long double __x, int __exp)
  { return __builtin_ldexpl(__x, __exp); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    ldexp(_Tp __x, int __exp)
    { return __builtin_ldexp(__x, __exp); }

  using ::log;


  inline float
  log(float __x)
  { return __builtin_logf(__x); }

  inline long double
  log(long double __x)
  { return __builtin_logl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    log(_Tp __x)
    { return __builtin_log(__x); }

  using ::log10;


  inline float
  log10(float __x)
  { return __builtin_log10f(__x); }

  inline long double
  log10(long double __x)
  { return __builtin_log10l(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    log10(_Tp __x)
    { return __builtin_log10(__x); }

  using ::modf;


  inline float
  modf(float __x, float* __iptr)
  { return __builtin_modff(__x, __iptr); }

  inline long double
  modf(long double __x, long double* __iptr)
  { return __builtin_modfl(__x, __iptr); }


  using ::pow;


  inline float
  pow(float __x, float __y)
  { return __builtin_powf(__x, __y); }

  inline long double
  pow(long double __x, long double __y)
  { return __builtin_powl(__x, __y); }




  inline double
  pow(double __x, int __i)
  { return __builtin_powi(__x, __i); }

  inline float
  pow(float __x, int __n)
  { return __builtin_powif(__x, __n); }

  inline long double
  pow(long double __x, int __n)
  { return __builtin_powil(__x, __n); }



  template<typename _Tp, typename _Up>
    inline
    typename __gnu_cxx::__promote_2<_Tp, _Up>::__type
    pow(_Tp __x, _Up __y)
    {
      typedef typename __gnu_cxx::__promote_2<_Tp, _Up>::__type __type;
      return pow(__type(__x), __type(__y));
    }

  using ::sin;


  inline float
  sin(float __x)
  { return __builtin_sinf(__x); }

  inline long double
  sin(long double __x)
  { return __builtin_sinl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    sin(_Tp __x)
    { return __builtin_sin(__x); }

  using ::sinh;


  inline float
  sinh(float __x)
  { return __builtin_sinhf(__x); }

  inline long double
  sinh(long double __x)
  { return __builtin_sinhl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    sinh(_Tp __x)
    { return __builtin_sinh(__x); }

  using ::sqrt;


  inline float
  sqrt(float __x)
  { return __builtin_sqrtf(__x); }

  inline long double
  sqrt(long double __x)
  { return __builtin_sqrtl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    sqrt(_Tp __x)
    { return __builtin_sqrt(__x); }

  using ::tan;


  inline float
  tan(float __x)
  { return __builtin_tanf(__x); }

  inline long double
  tan(long double __x)
  { return __builtin_tanl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    tan(_Tp __x)
    { return __builtin_tan(__x); }

  using ::tanh;


  inline float
  tanh(float __x)
  { return __builtin_tanhf(__x); }

  inline long double
  tanh(long double __x)
  { return __builtin_tanhl(__x); }


  template<typename _Tp>
    inline
    typename __gnu_cxx::__enable_if<__is_integer<_Tp>::__value,
                                    double>::__type
    tanh(_Tp __x)
    { return __builtin_tanh(__x); }


}
# 555 "/usr/include/c++/4.8.2/cmath" 3
namespace std __attribute__ ((__visibility__ ("default")))
{

# 805 "/usr/include/c++/4.8.2/cmath" 3
  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    fpclassify(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_fpclassify(0, 1, 4,
      3, 2, __type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    isfinite(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isfinite(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    isinf(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isinf(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    isnan(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isnan(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    isnormal(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isnormal(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    signbit(_Tp __f)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_signbit(__type(__f));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    isgreater(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isgreater(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    isgreaterequal(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isgreaterequal(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    isless(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isless(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    islessequal(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_islessequal(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    islessgreater(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_islessgreater(__type(__f1), __type(__f2));
    }

  template<typename _Tp>
    inline typename __gnu_cxx::__enable_if<__is_arithmetic<_Tp>::__value,
        int>::__type
    isunordered(_Tp __f1, _Tp __f2)
    {
      typedef typename __gnu_cxx::__promote<_Tp>::__type __type;
      return __builtin_isunordered(__type(__f1), __type(__f2));
    }




}
# 8842 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h" 2
# 1 "/usr/include/c++/4.8.2/cstdlib" 1 3
# 39 "/usr/include/c++/4.8.2/cstdlib" 3
       
# 40 "/usr/include/c++/4.8.2/cstdlib" 3
# 114 "/usr/include/c++/4.8.2/cstdlib" 3
namespace std __attribute__ ((__visibility__ ("default")))
{


  using ::div_t;
  using ::ldiv_t;

  using ::abort;
  using ::abs;
  using ::atexit;





  using ::atof;
  using ::atoi;
  using ::atol;
  using ::bsearch;
  using ::calloc;
  using ::div;
  using ::exit;
  using ::free;
  using ::getenv;
  using ::labs;
  using ::ldiv;
  using ::malloc;

  using ::mblen;
  using ::mbstowcs;
  using ::mbtowc;

  using ::qsort;





  using ::rand;
  using ::realloc;
  using ::srand;
  using ::strtod;
  using ::strtol;
  using ::strtoul;
  using ::system;

  using ::wcstombs;
  using ::wctomb;



  inline long
  abs(long __i) { return __builtin_labs(__i); }

  inline ldiv_t
  div(long __i, long __j) { return ldiv(__i, __j); }



  inline long long
  abs(long long __x) { return __builtin_llabs (__x); }



  inline __int128
  abs(__int128 __x) { return __x >= 0 ? __x : -__x; }



}
# 196 "/usr/include/c++/4.8.2/cstdlib" 3
namespace __gnu_cxx __attribute__ ((__visibility__ ("default")))
{



  using ::lldiv_t;





  using ::_Exit;



  using ::llabs;

  inline lldiv_t
  div(long long __n, long long __d)
  { lldiv_t __q; __q.quot = __n / __d; __q.rem = __n % __d; return __q; }

  using ::lldiv;
# 228 "/usr/include/c++/4.8.2/cstdlib" 3
  using ::atoll;
  using ::strtoll;
  using ::strtoull;

  using ::strtof;
  using ::strtold;


}

namespace std
{

  using ::__gnu_cxx::lldiv_t;

  using ::__gnu_cxx::_Exit;

  using ::__gnu_cxx::llabs;
  using ::__gnu_cxx::div;
  using ::__gnu_cxx::lldiv;

  using ::__gnu_cxx::atoll;
  using ::__gnu_cxx::strtof;
  using ::__gnu_cxx::strtoll;
  using ::__gnu_cxx::strtoull;
  using ::__gnu_cxx::strtold;
}
# 8843 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h" 2
# 8962 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int signbit(float x);



__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int signbit(double x);

__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int signbit(long double x);

__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isfinite(float x);



__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isfinite(double x);

__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isfinite(long double x);






__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isnan(float x);







__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isnan(double x) throw();




__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isnan(long double x);







__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isinf(float x);
# 9013 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isinf(double x) throw();




__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isinf(long double x);
# 9076 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
namespace std
{
  template<typename T> extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) T __pow_helper(T, int);
  template<typename T> extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) T __cmath_power(T, unsigned int);
}

using std::abs;
using std::fabs;
using std::ceil;
using std::floor;
using std::sqrt;

using std::pow;

using std::log;
using std::log10;
using std::fmod;
using std::modf;
using std::exp;
using std::frexp;
using std::ldexp;
using std::asin;
using std::sin;
using std::sinh;
using std::acos;
using std::cos;
using std::cosh;
using std::atan;
using std::atan2;
using std::tan;
using std::tanh;
# 9471 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
namespace std {
# 9480 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long long int abs(long long int);
# 9490 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long int abs(long int);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float abs(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double abs(double);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float fabs(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float ceil(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float floor(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float sqrt(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float pow(float, float);
# 9506 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float pow(float, int);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double pow(double, int);




extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float log(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float log10(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float fmod(float, float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float modf(float, float*);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float exp(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float frexp(float, int*);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float ldexp(float, int);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float asin(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float sin(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float sinh(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float acos(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float cos(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float cosh(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float atan(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float atan2(float, float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float tan(float);
extern __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float tanh(float);
# 9602 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
}
# 9739 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float logb(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int ilogb(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float scalbn(float a, int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float scalbln(float a, long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float exp2(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float expm1(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float log2(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float log1p(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float acosh(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float asinh(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float atanh(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float hypot(float a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float cbrt(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float erf(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float erfc(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float lgamma(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float tgamma(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float copysign(float a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float nextafter(float a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float remainder(float a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float remquo(float a, float b, int *quo);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float round(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long int lround(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long long int llround(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float trunc(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float rint(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long int lrint(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long long int llrint(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float nearbyint(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float fdim(float a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float fma(float a, float b, float c);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float fmax(float a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float fmin(float a, float b);
# 9842 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float exp10(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float rsqrt(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float rcbrt(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float sinpi(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float cospi(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void sincospi(float a, float *sptr, float *cptr);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) void sincos(float a, float *sptr, float *cptr);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float j0(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float j1(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float jn(int n, float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float y0(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float y1(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float yn(int n, float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float cyl_bessel_i0(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float cyl_bessel_i1(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float erfinv(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float erfcinv(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float normcdfinv(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float normcdf(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float erfcx(float a);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double copysign(double a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double copysign(float a, double b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned int min(unsigned int a, unsigned int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned int min(int a, unsigned int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned int min(unsigned int a, int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long int min(long int a, long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long int min(unsigned long int a, unsigned long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long int min(long int a, unsigned long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long int min(unsigned long int a, long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long long int min(long long int a, long long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int min(unsigned long long int a, unsigned long long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int min(long long int a, unsigned long long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int min(unsigned long long int a, long long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float min(float a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double min(double a, double b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double min(float a, double b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double min(double a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned int max(unsigned int a, unsigned int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned int max(int a, unsigned int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned int max(unsigned int a, int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long int max(long int a, long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long int max(unsigned long int a, unsigned long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long int max(long int a, unsigned long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long int max(unsigned long int a, long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) long long int max(long long int a, long long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int max(unsigned long long int a, unsigned long long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int max(long long int a, unsigned long long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int max(unsigned long long int a, long long int b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) float max(float a, float b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double max(double a, double b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double max(float a, double b);

static inline __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) double max(double a, float b);
# 10233 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp" 1
# 77 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 78 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 79 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp" 2
# 327 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp"
__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int signbit(float x) { return __signbitf(x); }



__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int signbit(double x) { return __signbit(x); }

__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int signbit(long double x) { return __signbitl(x);}
# 344 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp"
__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isfinite(float x) { return __finitef(x); }
# 359 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp"
__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isfinite(double x) { return __finite(x); }
# 372 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp"
__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isfinite(long double x) { return __finitel(x); }


__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isnan(float x) { return __isnanf(x); }



__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isnan(double x) throw() { return __isnan(x); }

__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isnan(long double x) { return __isnanl(x); }

__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isinf(float x) { return __isinff(x); }



__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isinf(double x) throw() { return __isinf(x); }

__inline__ __attribute__((always_inline)) __attribute__((host)) __attribute__((device)) __attribute__((cudart_builtin)) int isinf(long double x) { return __isinfl(x); }
# 585 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.hpp"
static inline __attribute__((host)) __attribute__((device)) float logb(float a)
{
  return logbf(a);
}

static inline __attribute__((host)) __attribute__((device)) int ilogb(float a)
{
  return ilogbf(a);
}

static inline __attribute__((host)) __attribute__((device)) float scalbn(float a, int b)
{
  return scalbnf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) float scalbln(float a, long int b)
{
  return scalblnf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) float exp2(float a)
{
  return exp2f(a);
}

static inline __attribute__((host)) __attribute__((device)) float expm1(float a)
{
  return expm1f(a);
}

static inline __attribute__((host)) __attribute__((device)) float log2(float a)
{
  return log2f(a);
}

static inline __attribute__((host)) __attribute__((device)) float log1p(float a)
{
  return log1pf(a);
}

static inline __attribute__((host)) __attribute__((device)) float acosh(float a)
{
  return acoshf(a);
}

static inline __attribute__((host)) __attribute__((device)) float asinh(float a)
{
  return asinhf(a);
}

static inline __attribute__((host)) __attribute__((device)) float atanh(float a)
{
  return atanhf(a);
}

static inline __attribute__((host)) __attribute__((device)) float hypot(float a, float b)
{
  return hypotf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) float cbrt(float a)
{
  return cbrtf(a);
}

static inline __attribute__((host)) __attribute__((device)) float erf(float a)
{
  return erff(a);
}

static inline __attribute__((host)) __attribute__((device)) float erfc(float a)
{
  return erfcf(a);
}

static inline __attribute__((host)) __attribute__((device)) float lgamma(float a)
{
  return lgammaf(a);
}

static inline __attribute__((host)) __attribute__((device)) float tgamma(float a)
{
  return tgammaf(a);
}

static inline __attribute__((host)) __attribute__((device)) float copysign(float a, float b)
{
  return copysignf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) float nextafter(float a, float b)
{
  return nextafterf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) float remainder(float a, float b)
{
  return remainderf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) float remquo(float a, float b, int *quo)
{
  return remquof(a, b, quo);
}

static inline __attribute__((host)) __attribute__((device)) float round(float a)
{
  return roundf(a);
}

static inline __attribute__((host)) __attribute__((device)) long int lround(float a)
{
  return lroundf(a);
}

static inline __attribute__((host)) __attribute__((device)) long long int llround(float a)
{
  return llroundf(a);
}

static inline __attribute__((host)) __attribute__((device)) float trunc(float a)
{
  return truncf(a);
}

static inline __attribute__((host)) __attribute__((device)) float rint(float a)
{
  return rintf(a);
}

static inline __attribute__((host)) __attribute__((device)) long int lrint(float a)
{
  return lrintf(a);
}

static inline __attribute__((host)) __attribute__((device)) long long int llrint(float a)
{
  return llrintf(a);
}

static inline __attribute__((host)) __attribute__((device)) float nearbyint(float a)
{
  return nearbyintf(a);
}

static inline __attribute__((host)) __attribute__((device)) float fdim(float a, float b)
{
  return fdimf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) float fma(float a, float b, float c)
{
  return fmaf(a, b, c);
}

static inline __attribute__((host)) __attribute__((device)) float fmax(float a, float b)
{
  return fmaxf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) float fmin(float a, float b)
{
  return fminf(a, b);
}







static inline __attribute__((host)) __attribute__((device)) float exp10(float a)
{
  return exp10f(a);
}

static inline __attribute__((host)) __attribute__((device)) float rsqrt(float a)
{
  return rsqrtf(a);
}

static inline __attribute__((host)) __attribute__((device)) float rcbrt(float a)
{
  return rcbrtf(a);
}

static inline __attribute__((host)) __attribute__((device)) float sinpi(float a)
{
  return sinpif(a);
}

static inline __attribute__((host)) __attribute__((device)) float cospi(float a)
{
  return cospif(a);
}

static inline __attribute__((host)) __attribute__((device)) void sincospi(float a, float *sptr, float *cptr)
{
  sincospif(a, sptr, cptr);
}

static inline __attribute__((host)) __attribute__((device)) void sincos(float a, float *sptr, float *cptr)
{
  sincosf(a, sptr, cptr);
}

static inline __attribute__((host)) __attribute__((device)) float j0(float a)
{
  return j0f(a);
}

static inline __attribute__((host)) __attribute__((device)) float j1(float a)
{
  return j1f(a);
}

static inline __attribute__((host)) __attribute__((device)) float jn(int n, float a)
{
  return jnf(n, a);
}

static inline __attribute__((host)) __attribute__((device)) float y0(float a)
{
  return y0f(a);
}

static inline __attribute__((host)) __attribute__((device)) float y1(float a)
{
  return y1f(a);
}

static inline __attribute__((host)) __attribute__((device)) float yn(int n, float a)
{
  return ynf(n, a);
}

static inline __attribute__((host)) __attribute__((device)) float cyl_bessel_i0(float a)
{
  return cyl_bessel_i0f(a);
}

static inline __attribute__((host)) __attribute__((device)) float cyl_bessel_i1(float a)
{
  return cyl_bessel_i1f(a);
}

static inline __attribute__((host)) __attribute__((device)) float erfinv(float a)
{
  return erfinvf(a);
}

static inline __attribute__((host)) __attribute__((device)) float erfcinv(float a)
{
  return erfcinvf(a);
}

static inline __attribute__((host)) __attribute__((device)) float normcdfinv(float a)
{
  return normcdfinvf(a);
}

static inline __attribute__((host)) __attribute__((device)) float normcdf(float a)
{
  return normcdff(a);
}

static inline __attribute__((host)) __attribute__((device)) float erfcx(float a)
{
  return erfcxf(a);
}

static inline __attribute__((host)) __attribute__((device)) double copysign(double a, float b)
{
  return copysign(a, (double)b);
}

static inline __attribute__((host)) __attribute__((device)) double copysign(float a, double b)
{
  return copysign((double)a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned int min(unsigned int a, unsigned int b)
{
  return umin(a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned int min(int a, unsigned int b)
{
  return umin((unsigned int)a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned int min(unsigned int a, int b)
{
  return umin(a, (unsigned int)b);
}

static inline __attribute__((host)) __attribute__((device)) long int min(long int a, long int b)
{





  if (sizeof(long int) == sizeof(int)) {



    return (long int)min((int)a, (int)b);
  } else {
    return (long int)llmin((long long int)a, (long long int)b);
  }
}

static inline __attribute__((host)) __attribute__((device)) unsigned long int min(unsigned long int a, unsigned long int b)
{



  if (sizeof(unsigned long int) == sizeof(unsigned int)) {



    return (unsigned long int)umin((unsigned int)a, (unsigned int)b);
  } else {
    return (unsigned long int)ullmin((unsigned long long int)a, (unsigned long long int)b);
  }
}

static inline __attribute__((host)) __attribute__((device)) unsigned long int min(long int a, unsigned long int b)
{



  if (sizeof(unsigned long int) == sizeof(unsigned int)) {



    return (unsigned long int)umin((unsigned int)a, (unsigned int)b);
  } else {
    return (unsigned long int)ullmin((unsigned long long int)a, (unsigned long long int)b);
  }
}

static inline __attribute__((host)) __attribute__((device)) unsigned long int min(unsigned long int a, long int b)
{



  if (sizeof(unsigned long int) == sizeof(unsigned int)) {



    return (unsigned long int)umin((unsigned int)a, (unsigned int)b);
  } else {
    return (unsigned long int)ullmin((unsigned long long int)a, (unsigned long long int)b);
  }
}

static inline __attribute__((host)) __attribute__((device)) long long int min(long long int a, long long int b)
{
  return llmin(a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned long long int min(unsigned long long int a, unsigned long long int b)
{
  return ullmin(a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned long long int min(long long int a, unsigned long long int b)
{
  return ullmin((unsigned long long int)a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned long long int min(unsigned long long int a, long long int b)
{
  return ullmin(a, (unsigned long long int)b);
}

static inline __attribute__((host)) __attribute__((device)) float min(float a, float b)
{
  return fminf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) double min(double a, double b)
{
  return fmin(a, b);
}

static inline __attribute__((host)) __attribute__((device)) double min(float a, double b)
{
  return fmin((double)a, b);
}

static inline __attribute__((host)) __attribute__((device)) double min(double a, float b)
{
  return fmin(a, (double)b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned int max(unsigned int a, unsigned int b)
{
  return umax(a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned int max(int a, unsigned int b)
{
  return umax((unsigned int)a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned int max(unsigned int a, int b)
{
  return umax(a, (unsigned int)b);
}

static inline __attribute__((host)) __attribute__((device)) long int max(long int a, long int b)
{




  if (sizeof(long int) == sizeof(int)) {



    return (long int)max((int)a, (int)b);
  } else {
    return (long int)llmax((long long int)a, (long long int)b);
  }
}

static inline __attribute__((host)) __attribute__((device)) unsigned long int max(unsigned long int a, unsigned long int b)
{



  if (sizeof(unsigned long int) == sizeof(unsigned int)) {



    return (unsigned long int)umax((unsigned int)a, (unsigned int)b);
  } else {
    return (unsigned long int)ullmax((unsigned long long int)a, (unsigned long long int)b);
  }
}

static inline __attribute__((host)) __attribute__((device)) unsigned long int max(long int a, unsigned long int b)
{



  if (sizeof(unsigned long int) == sizeof(unsigned int)) {



    return (unsigned long int)umax((unsigned int)a, (unsigned int)b);
  } else {
    return (unsigned long int)ullmax((unsigned long long int)a, (unsigned long long int)b);
  }
}

static inline __attribute__((host)) __attribute__((device)) unsigned long int max(unsigned long int a, long int b)
{



  if (sizeof(unsigned long int) == sizeof(unsigned int)) {



    return (unsigned long int)umax((unsigned int)a, (unsigned int)b);
  } else {
    return (unsigned long int)ullmax((unsigned long long int)a, (unsigned long long int)b);
  }
}

static inline __attribute__((host)) __attribute__((device)) long long int max(long long int a, long long int b)
{
  return llmax(a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned long long int max(unsigned long long int a, unsigned long long int b)
{
  return ullmax(a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned long long int max(long long int a, unsigned long long int b)
{
  return ullmax((unsigned long long int)a, b);
}

static inline __attribute__((host)) __attribute__((device)) unsigned long long int max(unsigned long long int a, long long int b)
{
  return ullmax(a, (unsigned long long int)b);
}

static inline __attribute__((host)) __attribute__((device)) float max(float a, float b)
{
  return fmaxf(a, b);
}

static inline __attribute__((host)) __attribute__((device)) double max(double a, double b)
{
  return fmax(a, b);
}

static inline __attribute__((host)) __attribute__((device)) double max(float a, double b)
{
  return fmax((double)a, b);
}

static inline __attribute__((host)) __attribute__((device)) double max(double a, float b)
{
  return fmax(a, (double)b);
}
# 10234 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/math_functions.h" 2
# 268 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/common_functions.h" 2
# 116 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_surface_types.h" 1
# 74 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_surface_types.h"
template<class T, int dim = 1>
struct __attribute__((device_builtin_surface_type)) surface : public surfaceReference
{

  __attribute__((host)) surface(void)
  {
    channelDesc = cudaCreateChannelDesc<T>();
  }

  __attribute__((host)) surface(struct cudaChannelFormatDesc desc)
  {
    channelDesc = desc;
  }

};

template<int dim>
struct __attribute__((device_builtin_surface_type)) surface<void, dim> : public surfaceReference
{

  __attribute__((host)) surface(void)
  {
    channelDesc = cudaCreateChannelDesc<void>();
  }

};
# 117 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_texture_types.h" 1
# 74 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_texture_types.h"
template<class T, int texType = 0x01, enum cudaTextureReadMode mode = cudaReadModeElementType>
struct __attribute__((device_builtin_texture_type)) texture : public textureReference
{

  __attribute__((host)) texture(int norm = 0,
                   enum cudaTextureFilterMode fMode = cudaFilterModePoint,
                   enum cudaTextureAddressMode aMode = cudaAddressModeClamp)
  {
    normalized = norm;
    filterMode = fMode;
    addressMode[0] = aMode;
    addressMode[1] = aMode;
    addressMode[2] = aMode;
    channelDesc = cudaCreateChannelDesc<T>();
    sRGB = 0;
  }

  __attribute__((host)) texture(int norm,
                   enum cudaTextureFilterMode fMode,
                   enum cudaTextureAddressMode aMode,
                   struct cudaChannelFormatDesc desc)
  {
    normalized = norm;
    filterMode = fMode;
    addressMode[0] = aMode;
    addressMode[1] = aMode;
    addressMode[2] = aMode;
    channelDesc = desc;
    sRGB = 0;
  }

};
# 118 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 1
# 79 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 80 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_types.h" 1
# 81 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 82 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2







extern "C"
{
# 100 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __mulhi(int x, int y);
# 110 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __umulhi(unsigned int x, unsigned int y);
# 120 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) long long int __mul64hi(long long int x, long long int y);
# 130 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned long long int __umul64hi(unsigned long long int x, unsigned long long int y);
# 139 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __int_as_float(int x);
# 148 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __float_as_int(float x);
# 157 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __uint_as_float(unsigned int x);
# 166 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __float_as_uint(float x);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) void __syncthreads(void);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) void __prof_trigger(int);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) void __threadfence(void);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) void __threadfence_block(void);
__attribute__((device)) __attribute__((cudart_builtin))

__attribute__((__noreturn__))



__attribute__((device_builtin)) void __trap(void);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) void __brkpt();
# 201 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __saturatef(float x);
# 270 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __sad(int x, int y, unsigned int z);
# 338 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __usad(unsigned int x, unsigned int y, unsigned int z);
# 348 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __mul24(int x, int y);
# 358 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __umul24(unsigned int x, unsigned int y);
# 371 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float fdividef(float x, float y);
# 446 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fdividef(float x, float y);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) double fdivide(double x, double y);
# 459 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __sinf(float x) throw ();
# 471 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __cosf(float x) throw ();
# 485 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __tanf(float x) throw ();
# 500 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) void __sincosf(float x, float *sptr, float *cptr) throw ();
# 550 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __expf(float x) throw ();
# 582 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __exp10f(float x) throw ();
# 608 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __log2f(float x) throw ();
# 636 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __log10f(float x) throw ();
# 680 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __logf(float x) throw ();
# 723 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) __attribute__((cudart_builtin)) float __powf(float x, float y) throw ();
# 732 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __float2int_rn(float x);
# 741 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __float2int_rz(float x);
# 750 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __float2int_ru(float);
# 759 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __float2int_rd(float x);
# 768 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __float2uint_rn(float x);
# 777 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __float2uint_rz(float x);
# 786 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __float2uint_ru(float x);
# 795 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __float2uint_rd(float x);
# 804 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __int2float_rn(int x);
# 813 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __int2float_rz(int x);
# 822 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __int2float_ru(int x);
# 831 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __int2float_rd(int x);
# 840 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __uint2float_rn(unsigned int x);
# 849 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __uint2float_rz(unsigned int x);
# 858 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __uint2float_ru(unsigned int x);
# 867 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __uint2float_rd(unsigned int x);
# 876 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) long long int __float2ll_rn(float x);
# 885 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) long long int __float2ll_rz(float x);
# 894 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) long long int __float2ll_ru(float x);
# 903 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) long long int __float2ll_rd(float x);
# 912 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned long long int __float2ull_rn(float x);
# 921 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned long long int __float2ull_rz(float x);
# 930 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned long long int __float2ull_ru(float x);
# 939 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned long long int __float2ull_rd(float x);
# 948 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __ll2float_rn(long long int x);
# 957 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __ll2float_rz(long long int x);
# 966 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __ll2float_ru(long long int x);
# 975 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __ll2float_rd(long long int x);
# 984 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __ull2float_rn(unsigned long long int x);
# 993 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __ull2float_rz(unsigned long long int x);
# 1002 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __ull2float_ru(unsigned long long int x);
# 1011 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __ull2float_rd(unsigned long long int x);
# 1023 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fadd_rn(float x, float y);
# 1035 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fadd_rz(float x, float y);
# 1047 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fadd_ru(float x, float y);
# 1059 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fadd_rd(float x, float y);
# 1071 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fsub_rn(float x, float y);
# 1083 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fsub_rz(float x, float y);
# 1095 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fsub_ru(float x, float y);
# 1107 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fsub_rd(float x, float y);
# 1119 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fmul_rn(float x, float y);
# 1131 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fmul_rz(float x, float y);
# 1143 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fmul_ru(float x, float y);
# 1155 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fmul_rd(float x, float y);
# 1308 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fmaf_rn(float x, float y, float z);
# 1461 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fmaf_rz(float x, float y, float z);
# 1614 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fmaf_ru(float x, float y, float z);
# 1767 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fmaf_rd(float x, float y, float z);
# 1800 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __frcp_rn(float x);
# 1833 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __frcp_rz(float x);
# 1866 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __frcp_ru(float x);
# 1899 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __frcp_rd(float x);
# 1930 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fsqrt_rn(float x);
# 1961 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fsqrt_rz(float x);
# 1992 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fsqrt_ru(float x);
# 2023 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fsqrt_rd(float x);
# 2062 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __frsqrt_rn(float x);
# 2073 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fdiv_rn(float x, float y);
# 2084 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fdiv_rz(float x, float y);
# 2095 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fdiv_ru(float x, float y);
# 2106 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) float __fdiv_rd(float x, float y);
# 2115 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __clz(int x);
# 2126 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __ffs(int x);
# 2135 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __popc(unsigned int x);
# 2144 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __brev(unsigned int x);
# 2153 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __clzll(long long int x);
# 2164 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __ffsll(long long int x);
# 2175 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __popcll(unsigned long long int x);
# 2184 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned long long int __brevll(unsigned long long int x);
# 2208 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __byte_perm(unsigned int x, unsigned int y, unsigned int s);
# 2220 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __hadd(int, int);
# 2233 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __rhadd(int, int);
# 2245 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __uhadd(unsigned int, unsigned int);
# 2258 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __urhadd(unsigned int, unsigned int);
# 2268 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) int __double2int_rz(double);
# 2277 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __double2uint_rz(double);
# 2286 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) long long int __double2ll_rz(double);
# 2295 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned long long int __double2ull_rz(double);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __pm0(void);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __pm1(void);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __pm2(void);
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __pm3(void);
# 2325 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vabs2(unsigned int a);
# 2336 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vabsss2(unsigned int a);
# 2347 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vadd2(unsigned int a, unsigned int b);
# 2358 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vaddss2 (unsigned int a, unsigned int b);
# 2368 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vaddus2 (unsigned int a, unsigned int b);
# 2379 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vavgs2(unsigned int a, unsigned int b);
# 2390 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vavgu2(unsigned int a, unsigned int b);
# 2401 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vhaddu2(unsigned int a, unsigned int b);
# 2412 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpeq2(unsigned int a, unsigned int b);
# 2423 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpges2(unsigned int a, unsigned int b);
# 2434 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpgeu2(unsigned int a, unsigned int b);
# 2445 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpgts2(unsigned int a, unsigned int b);
# 2456 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpgtu2(unsigned int a, unsigned int b);
# 2467 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmples2(unsigned int a, unsigned int b);
# 2479 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpleu2(unsigned int a, unsigned int b);
# 2490 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmplts2(unsigned int a, unsigned int b);
# 2501 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpltu2(unsigned int a, unsigned int b);
# 2512 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpne2(unsigned int a, unsigned int b);
# 2523 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vabsdiffu2(unsigned int a, unsigned int b);
# 2534 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vmaxs2(unsigned int a, unsigned int b);
# 2545 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vmaxu2(unsigned int a, unsigned int b);
# 2556 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vmins2(unsigned int a, unsigned int b);
# 2567 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vminu2(unsigned int a, unsigned int b);
# 2578 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vseteq2(unsigned int a, unsigned int b);
# 2589 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetges2(unsigned int a, unsigned int b);
# 2600 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetgeu2(unsigned int a, unsigned int b);
# 2611 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetgts2(unsigned int a, unsigned int b);
# 2622 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetgtu2(unsigned int a, unsigned int b);
# 2633 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetles2(unsigned int a, unsigned int b);
# 2644 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetleu2(unsigned int a, unsigned int b);
# 2655 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetlts2(unsigned int a, unsigned int b);
# 2666 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetltu2(unsigned int a, unsigned int b);
# 2677 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetne2(unsigned int a, unsigned int b);
# 2688 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsadu2(unsigned int a, unsigned int b);
# 2699 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsub2(unsigned int a, unsigned int b);
# 2710 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsubss2 (unsigned int a, unsigned int b);
# 2721 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsubus2 (unsigned int a, unsigned int b);
# 2731 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vneg2(unsigned int a);
# 2741 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vnegss2(unsigned int a);
# 2752 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vabsdiffs2(unsigned int a, unsigned int b);
# 2763 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsads2(unsigned int a, unsigned int b);
# 2773 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vabs4(unsigned int a);
# 2784 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vabsss4(unsigned int a);
# 2795 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vadd4(unsigned int a, unsigned int b);
# 2806 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vaddss4 (unsigned int a, unsigned int b);
# 2816 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vaddus4 (unsigned int a, unsigned int b);
# 2827 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vavgs4(unsigned int a, unsigned int b);
# 2838 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vavgu4(unsigned int a, unsigned int b);
# 2849 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vhaddu4(unsigned int a, unsigned int b);
# 2860 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpeq4(unsigned int a, unsigned int b);
# 2871 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpges4(unsigned int a, unsigned int b);
# 2882 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpgeu4(unsigned int a, unsigned int b);
# 2893 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpgts4(unsigned int a, unsigned int b);
# 2904 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpgtu4(unsigned int a, unsigned int b);
# 2915 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmples4(unsigned int a, unsigned int b);
# 2926 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpleu4(unsigned int a, unsigned int b);
# 2937 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmplts4(unsigned int a, unsigned int b);
# 2948 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpltu4(unsigned int a, unsigned int b);
# 2959 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vcmpne4(unsigned int a, unsigned int b);
# 2970 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vabsdiffu4(unsigned int a, unsigned int b);
# 2981 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vmaxs4(unsigned int a, unsigned int b);
# 2992 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vmaxu4(unsigned int a, unsigned int b);
# 3003 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vmins4(unsigned int a, unsigned int b);
# 3014 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vminu4(unsigned int a, unsigned int b);
# 3025 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vseteq4(unsigned int a, unsigned int b);
# 3036 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetles4(unsigned int a, unsigned int b);
# 3047 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetleu4(unsigned int a, unsigned int b);
# 3058 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetlts4(unsigned int a, unsigned int b);
# 3069 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetltu4(unsigned int a, unsigned int b);
# 3080 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetges4(unsigned int a, unsigned int b);
# 3091 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetgeu4(unsigned int a, unsigned int b);
# 3102 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetgts4(unsigned int a, unsigned int b);
# 3113 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetgtu4(unsigned int a, unsigned int b);
# 3124 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsetne4(unsigned int a, unsigned int b);
# 3135 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsadu4(unsigned int a, unsigned int b);
# 3146 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsub4(unsigned int a, unsigned int b);
# 3157 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsubss4(unsigned int a, unsigned int b);
# 3168 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsubus4(unsigned int a, unsigned int b);
# 3178 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vneg4(unsigned int a);
# 3188 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vnegss4(unsigned int a);
# 3199 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vabsdiffs4(unsigned int a, unsigned int b);
# 3210 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
__attribute__((device)) __attribute__((cudart_builtin)) __attribute__((device_builtin)) unsigned int __vsads4(unsigned int a, unsigned int b);






}







static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) int mulhi(int a, int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned int mulhi(unsigned int a, unsigned int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned int mulhi(int a, unsigned int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned int mulhi(unsigned int a, int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) long long int mul64hi(long long int a, long long int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int mul64hi(unsigned long long int a, unsigned long long int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int mul64hi(long long int a, unsigned long long int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned long long int mul64hi(unsigned long long int a, long long int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) int float_as_int(float a);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) float int_as_float(int a);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned int float_as_uint(float a);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) float uint_as_float(unsigned int a);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) float saturate(float a);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) int mul24(int a, int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned int umul24(unsigned int a, unsigned int b);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) int float2int(float a, enum cudaRoundMode mode = cudaRoundZero);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) unsigned int float2uint(float a, enum cudaRoundMode mode = cudaRoundZero);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) float int2float(int a, enum cudaRoundMode mode = cudaRoundNearest);

static __inline__ __attribute__((device)) __attribute__((cudart_builtin)) float uint2float(unsigned int a, enum cudaRoundMode mode = cudaRoundNearest);
# 3275 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.hpp" 1
# 79 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.hpp"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 80 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.hpp" 2

# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 82 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.hpp" 2
# 90 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.hpp"
static __inline__ __attribute__((device)) int mulhi(int a, int b)
{
  return __mulhi(a, b);
}

static __inline__ __attribute__((device)) unsigned int mulhi(unsigned int a, unsigned int b)
{
  return __umulhi(a, b);
}

static __inline__ __attribute__((device)) unsigned int mulhi(int a, unsigned int b)
{
  return __umulhi((unsigned int)a, b);
}

static __inline__ __attribute__((device)) unsigned int mulhi(unsigned int a, int b)
{
  return __umulhi(a, (unsigned int)b);
}

static __inline__ __attribute__((device)) long long int mul64hi(long long int a, long long int b)
{
  return __mul64hi(a, b);
}

static __inline__ __attribute__((device)) unsigned long long int mul64hi(unsigned long long int a, unsigned long long int b)
{
  return __umul64hi(a, b);
}

static __inline__ __attribute__((device)) unsigned long long int mul64hi(long long int a, unsigned long long int b)
{
  return __umul64hi((unsigned long long int)a, b);
}

static __inline__ __attribute__((device)) unsigned long long int mul64hi(unsigned long long int a, long long int b)
{
  return __umul64hi(a, (unsigned long long int)b);
}

static __inline__ __attribute__((device)) int float_as_int(float a)
{
  return __float_as_int(a);
}

static __inline__ __attribute__((device)) float int_as_float(int a)
{
  return __int_as_float(a);
}

static __inline__ __attribute__((device)) unsigned int float_as_uint(float a)
{
  return __float_as_uint(a);
}

static __inline__ __attribute__((device)) float uint_as_float(unsigned int a)
{
  return __uint_as_float(a);
}
static __inline__ __attribute__((device)) float saturate(float a)
{
  return __saturatef(a);
}

static __inline__ __attribute__((device)) int mul24(int a, int b)
{
  return __mul24(a, b);
}

static __inline__ __attribute__((device)) unsigned int umul24(unsigned int a, unsigned int b)
{
  return __umul24(a, b);
}

static __inline__ __attribute__((device)) int float2int(float a, enum cudaRoundMode mode)
{
  return mode == cudaRoundNearest ? __float2int_rn(a) :
         mode == cudaRoundPosInf ? __float2int_ru(a) :
         mode == cudaRoundMinInf ? __float2int_rd(a) :
                                    __float2int_rz(a);
}

static __inline__ __attribute__((device)) unsigned int float2uint(float a, enum cudaRoundMode mode)
{
  return mode == cudaRoundNearest ? __float2uint_rn(a) :
         mode == cudaRoundPosInf ? __float2uint_ru(a) :
         mode == cudaRoundMinInf ? __float2uint_rd(a) :
                                    __float2uint_rz(a);
}

static __inline__ __attribute__((device)) float int2float(int a, enum cudaRoundMode mode)
{
  return mode == cudaRoundZero ? __int2float_rz(a) :
         mode == cudaRoundPosInf ? __int2float_ru(a) :
         mode == cudaRoundMinInf ? __int2float_rd(a) :
                                   __int2float_rn(a);
}

static __inline__ __attribute__((device)) float uint2float(unsigned int a, enum cudaRoundMode mode)
{
  return mode == cudaRoundZero ? __uint2float_rz(a) :
         mode == cudaRoundPosInf ? __uint2float_ru(a) :
         mode == cudaRoundMinInf ? __uint2float_rd(a) :
                                   __uint2float_rn(a);
}
# 3276 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2


# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.h" 1
# 76 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.h"
extern "C"
{
extern __attribute__((device)) __attribute__((device_builtin)) int __iAtomicAdd(int *address, int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicAdd(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) int __iAtomicExch(int *address, int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicExch(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) float __fAtomicExch(float *address, float val);
extern __attribute__((device)) __attribute__((device_builtin)) int __iAtomicMin(int *address, int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicMin(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) int __iAtomicMax(int *address, int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicMax(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicInc(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicDec(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) int __iAtomicAnd(int *address, int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicAnd(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) int __iAtomicOr(int *address, int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicOr(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) int __iAtomicXor(int *address, int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicXor(unsigned int *address, unsigned int val);
extern __attribute__((device)) __attribute__((device_builtin)) int __iAtomicCAS(int *address, int compare, int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __uAtomicCAS(unsigned int *address, unsigned int compare, unsigned int val);
}
# 106 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.h"
static __inline__ __attribute__((device)) int atomicAdd(int *address, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicAdd(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) int atomicSub(int *address, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicSub(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) int atomicExch(int *address, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicExch(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) float atomicExch(float *address, float val) ;

static __inline__ __attribute__((device)) int atomicMin(int *address, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicMin(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) int atomicMax(int *address, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicMax(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) unsigned int atomicInc(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) unsigned int atomicDec(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) int atomicAnd(int *address, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicAnd(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) int atomicOr(int *address, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicOr(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) int atomicXor(int *address, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicXor(unsigned int *address, unsigned int val) ;

static __inline__ __attribute__((device)) int atomicCAS(int *address, int compare, int val) ;

static __inline__ __attribute__((device)) unsigned int atomicCAS(unsigned int *address, unsigned int compare, unsigned int val) ;
# 171 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.h"
extern "C"
{

extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __ullAtomicAdd(unsigned long long int *address, unsigned long long int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __ullAtomicExch(unsigned long long int *address, unsigned long long int val);
extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __ullAtomicCAS(unsigned long long int *address, unsigned long long int compare, unsigned long long int val);

extern __attribute__((device)) __attribute__((device_builtin)) __attribute__((deprecated("__any""() is deprecated in favor of ""__any""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) int __any(int cond);
extern __attribute__((device)) __attribute__((device_builtin)) __attribute__((deprecated("__all""() is deprecated in favor of ""__all""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) int __all(int cond);
}
# 189 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.h"
static __inline__ __attribute__((device)) unsigned long long int atomicAdd(unsigned long long int *address, unsigned long long int val) ;

static __inline__ __attribute__((device)) unsigned long long int atomicExch(unsigned long long int *address, unsigned long long int val) ;

static __inline__ __attribute__((device)) unsigned long long int atomicCAS(unsigned long long int *address, unsigned long long int compare, unsigned long long int val) ;

static __inline__ __attribute__((device)) __attribute__((deprecated("__any""() is deprecated in favor of ""__any""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) bool any(bool cond) ;

static __inline__ __attribute__((device)) __attribute__((deprecated("__all""() is deprecated in favor of ""__all""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) bool all(bool cond) ;
# 208 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.hpp" 1
# 75 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.hpp"
static __inline__ __attribute__((device)) int atomicAdd(int *address, int val)
{
  return __iAtomicAdd(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicAdd(unsigned int *address, unsigned int val)
{
  return __uAtomicAdd(address, val);
}

static __inline__ __attribute__((device)) int atomicSub(int *address, int val)
{
  return __iAtomicAdd(address, (unsigned int)-(int)val);
}

static __inline__ __attribute__((device)) unsigned int atomicSub(unsigned int *address, unsigned int val)
{
  return __uAtomicAdd(address, (unsigned int)-(int)val);
}

static __inline__ __attribute__((device)) int atomicExch(int *address, int val)
{
  return __iAtomicExch(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicExch(unsigned int *address, unsigned int val)
{
  return __uAtomicExch(address, val);
}

static __inline__ __attribute__((device)) float atomicExch(float *address, float val)
{
  return __fAtomicExch(address, val);
}

static __inline__ __attribute__((device)) int atomicMin(int *address, int val)
{
  return __iAtomicMin(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicMin(unsigned int *address, unsigned int val)
{
  return __uAtomicMin(address, val);
}

static __inline__ __attribute__((device)) int atomicMax(int *address, int val)
{
  return __iAtomicMax(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicMax(unsigned int *address, unsigned int val)
{
  return __uAtomicMax(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicInc(unsigned int *address, unsigned int val)
{
  return __uAtomicInc(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicDec(unsigned int *address, unsigned int val)
{
  return __uAtomicDec(address, val);
}

static __inline__ __attribute__((device)) int atomicAnd(int *address, int val)
{
  return __iAtomicAnd(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicAnd(unsigned int *address, unsigned int val)
{
  return __uAtomicAnd(address, val);
}

static __inline__ __attribute__((device)) int atomicOr(int *address, int val)
{
  return __iAtomicOr(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicOr(unsigned int *address, unsigned int val)
{
  return __uAtomicOr(address, val);
}

static __inline__ __attribute__((device)) int atomicXor(int *address, int val)
{
  return __iAtomicXor(address, val);
}

static __inline__ __attribute__((device)) unsigned int atomicXor(unsigned int *address, unsigned int val)
{
  return __uAtomicXor(address, val);
}

static __inline__ __attribute__((device)) int atomicCAS(int *address, int compare, int val)
{
  return __iAtomicCAS(address, compare, val);
}

static __inline__ __attribute__((device)) unsigned int atomicCAS(unsigned int *address, unsigned int compare, unsigned int val)
{
  return __uAtomicCAS(address, compare, val);
}
# 194 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.hpp"
static __inline__ __attribute__((device)) unsigned long long int atomicAdd(unsigned long long int *address, unsigned long long int val)
{
  return __ullAtomicAdd(address, val);
}

static __inline__ __attribute__((device)) unsigned long long int atomicExch(unsigned long long int *address, unsigned long long int val)
{
  return __ullAtomicExch(address, val);
}

static __inline__ __attribute__((device)) unsigned long long int atomicCAS(unsigned long long int *address, unsigned long long int compare, unsigned long long int val)
{
  return __ullAtomicCAS(address, compare, val);
}

static __inline__ __attribute__((device)) bool any(bool cond)
{
  return (bool)__any((int)cond);
}

static __inline__ __attribute__((device)) bool all(bool cond)
{
  return (bool)__all((int)cond);
}
# 209 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_atomic_functions.h" 2
# 3279 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h" 1
# 83 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 84 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h" 2

# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 86 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h" 2

extern "C"
{
# 97 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) long long int __double_as_longlong(double x);
# 106 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __longlong_as_double(long long int x);
# 263 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __fma_rn(double x, double y, double z);
# 420 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __fma_rz(double x, double y, double z);
# 577 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __fma_ru(double x, double y, double z);
# 734 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __fma_rd(double x, double y, double z);
# 746 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dadd_rn(double x, double y);
# 758 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dadd_rz(double x, double y);
# 770 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dadd_ru(double x, double y);
# 782 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dadd_rd(double x, double y);
# 794 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsub_rn(double x, double y);
# 806 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsub_rz(double x, double y);
# 818 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsub_ru(double x, double y);
# 830 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsub_rd(double x, double y);
# 842 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dmul_rn(double x, double y);
# 854 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dmul_rz(double x, double y);
# 866 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dmul_ru(double x, double y);
# 878 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dmul_rd(double x, double y);
# 887 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) float __double2float_rn(double x);
# 896 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) float __double2float_rz(double x);
# 905 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) float __double2float_ru(double x);
# 914 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) float __double2float_rd(double x);
# 923 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2int_rn(double x);
# 932 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2int_ru(double x);
# 941 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2int_rd(double x);
# 950 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __double2uint_rn(double x);
# 959 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __double2uint_ru(double x);
# 968 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __double2uint_rd(double x);
# 977 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) long long int __double2ll_rn(double x);
# 986 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) long long int __double2ll_ru(double x);
# 995 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) long long int __double2ll_rd(double x);
# 1004 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __double2ull_rn(double x);
# 1013 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __double2ull_ru(double x);
# 1022 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __double2ull_rd(double x);







extern __attribute__((device)) __attribute__((device_builtin)) double __int2double_rn(int x);







extern __attribute__((device)) __attribute__((device_builtin)) double __uint2double_rn(unsigned int x);
# 1047 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ll2double_rn(long long int x);
# 1056 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ll2double_rz(long long int x);
# 1065 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ll2double_ru(long long int x);
# 1074 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ll2double_rd(long long int x);
# 1083 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ull2double_rn(unsigned long long int x);
# 1092 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ull2double_rz(unsigned long long int x);
# 1101 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ull2double_ru(unsigned long long int x);
# 1110 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ull2double_rd(unsigned long long int x);
# 1119 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2hiint(double x);
# 1128 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2loint(double x);
# 1138 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __hiloint2double(int hi, int lo);
}







static __inline__ __attribute__((device)) double fma(double a, double b, double c, enum cudaRoundMode mode);

static __inline__ __attribute__((device)) double dmul(double a, double b, enum cudaRoundMode mode = cudaRoundNearest);

static __inline__ __attribute__((device)) double dadd(double a, double b, enum cudaRoundMode mode = cudaRoundNearest);

static __inline__ __attribute__((device)) double dsub(double a, double b, enum cudaRoundMode mode = cudaRoundNearest);

static __inline__ __attribute__((device)) int double2int(double a, enum cudaRoundMode mode = cudaRoundZero);

static __inline__ __attribute__((device)) unsigned int double2uint(double a, enum cudaRoundMode mode = cudaRoundZero);

static __inline__ __attribute__((device)) long long int double2ll(double a, enum cudaRoundMode mode = cudaRoundZero);

static __inline__ __attribute__((device)) unsigned long long int double2ull(double a, enum cudaRoundMode mode = cudaRoundZero);

static __inline__ __attribute__((device)) double ll2double(long long int a, enum cudaRoundMode mode = cudaRoundNearest);

static __inline__ __attribute__((device)) double ull2double(unsigned long long int a, enum cudaRoundMode mode = cudaRoundNearest);

static __inline__ __attribute__((device)) double int2double(int a, enum cudaRoundMode mode = cudaRoundNearest);

static __inline__ __attribute__((device)) double uint2double(unsigned int a, enum cudaRoundMode mode = cudaRoundNearest);

static __inline__ __attribute__((device)) double float2double(float a, enum cudaRoundMode mode = cudaRoundNearest);






# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.hpp" 1
# 83 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.hpp"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/builtin_types.h" 1
# 84 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.hpp" 2

# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/host_defines.h" 1
# 86 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.hpp" 2







static __inline__ __attribute__((device)) double fma(double a, double b, double c, enum cudaRoundMode mode)
{
  return mode == cudaRoundZero ? __fma_rz(a, b, c) :
         mode == cudaRoundPosInf ? __fma_ru(a, b, c) :
         mode == cudaRoundMinInf ? __fma_rd(a, b, c) :
                                   __fma_rn(a, b, c);
}

static __inline__ __attribute__((device)) double dmul(double a, double b, enum cudaRoundMode mode)
{
  return mode == cudaRoundZero ? __dmul_rz(a, b) :
         mode == cudaRoundPosInf ? __dmul_ru(a, b) :
         mode == cudaRoundMinInf ? __dmul_rd(a, b) :
                                   __dmul_rn(a, b);
}

static __inline__ __attribute__((device)) double dadd(double a, double b, enum cudaRoundMode mode)
{
  return mode == cudaRoundZero ? __dadd_rz(a, b) :
         mode == cudaRoundPosInf ? __dadd_ru(a, b) :
         mode == cudaRoundMinInf ? __dadd_rd(a, b) :
                                   __dadd_rn(a, b);
}

static __inline__ __attribute__((device)) double dsub(double a, double b, enum cudaRoundMode mode)
{
  return mode == cudaRoundZero ? __dsub_rz(a, b) :
         mode == cudaRoundPosInf ? __dsub_ru(a, b) :
         mode == cudaRoundMinInf ? __dsub_rd(a, b) :
                                   __dsub_rn(a, b);
}

static __inline__ __attribute__((device)) int double2int(double a, enum cudaRoundMode mode)
{
  return mode == cudaRoundNearest ? __double2int_rn(a) :
         mode == cudaRoundPosInf ? __double2int_ru(a) :
         mode == cudaRoundMinInf ? __double2int_rd(a) :
                                    __double2int_rz(a);
}

static __inline__ __attribute__((device)) unsigned int double2uint(double a, enum cudaRoundMode mode)
{
  return mode == cudaRoundNearest ? __double2uint_rn(a) :
         mode == cudaRoundPosInf ? __double2uint_ru(a) :
         mode == cudaRoundMinInf ? __double2uint_rd(a) :
                                    __double2uint_rz(a);
}

static __inline__ __attribute__((device)) long long int double2ll(double a, enum cudaRoundMode mode)
{
  return mode == cudaRoundNearest ? __double2ll_rn(a) :
         mode == cudaRoundPosInf ? __double2ll_ru(a) :
         mode == cudaRoundMinInf ? __double2ll_rd(a) :
                                    __double2ll_rz(a);
}

static __inline__ __attribute__((device)) unsigned long long int double2ull(double a, enum cudaRoundMode mode)
{
  return mode == cudaRoundNearest ? __double2ull_rn(a) :
         mode == cudaRoundPosInf ? __double2ull_ru(a) :
         mode == cudaRoundMinInf ? __double2ull_rd(a) :
                                    __double2ull_rz(a);
}

static __inline__ __attribute__((device)) double ll2double(long long int a, enum cudaRoundMode mode)
{
  return mode == cudaRoundZero ? __ll2double_rz(a) :
         mode == cudaRoundPosInf ? __ll2double_ru(a) :
         mode == cudaRoundMinInf ? __ll2double_rd(a) :
                                   __ll2double_rn(a);
}

static __inline__ __attribute__((device)) double ull2double(unsigned long long int a, enum cudaRoundMode mode)
{
  return mode == cudaRoundZero ? __ull2double_rz(a) :
         mode == cudaRoundPosInf ? __ull2double_ru(a) :
         mode == cudaRoundMinInf ? __ull2double_rd(a) :
                                   __ull2double_rn(a);
}

static __inline__ __attribute__((device)) double int2double(int a, enum cudaRoundMode mode)
{
  return (double)a;
}

static __inline__ __attribute__((device)) double uint2double(unsigned int a, enum cudaRoundMode mode)
{
  return (double)a;
}

static __inline__ __attribute__((device)) double float2double(float a, enum cudaRoundMode mode)
{
  return (double)a;
}
# 1179 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_double_functions.h" 2
# 3280 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_atomic_functions.h" 1
# 77 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_atomic_functions.h"
extern "C"
{
extern __attribute__((device)) __attribute__((device_builtin)) float __fAtomicAdd(float *address, float val);
}
# 89 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_atomic_functions.h"
static __inline__ __attribute__((device)) float atomicAdd(float *address, float val) ;







# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_atomic_functions.hpp" 1
# 75 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_atomic_functions.hpp"
static __inline__ __attribute__((device)) float atomicAdd(float *address, float val)
{
  return __fAtomicAdd(address, val);
}
# 98 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_atomic_functions.h" 2
# 3281 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_atomic_functions.h" 1
# 128 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_atomic_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_atomic_functions.hpp" 1
# 129 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_atomic_functions.h" 2
# 3282 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_35_atomic_functions.h" 1
# 56 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_35_atomic_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_atomic_functions.h" 1
# 57 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_35_atomic_functions.h" 2
# 3283 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_60_atomic_functions.h" 1
# 535 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_60_atomic_functions.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_60_atomic_functions.hpp" 1
# 536 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_60_atomic_functions.h" 2
# 3284 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h" 1
# 90 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern "C"
{
extern __attribute__((device)) __attribute__((device_builtin)) void __threadfence_system(void);
# 104 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ddiv_rn(double x, double y);
# 116 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ddiv_rz(double x, double y);
# 128 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ddiv_ru(double x, double y);
# 140 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ddiv_rd(double x, double y);
# 174 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __drcp_rn(double x);
# 208 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __drcp_rz(double x);
# 242 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __drcp_ru(double x);
# 276 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __drcp_rd(double x);
# 308 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsqrt_rn(double x);
# 340 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsqrt_rz(double x);
# 372 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsqrt_ru(double x);
# 404 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsqrt_rd(double x);
extern __attribute__((device)) __attribute__((device_builtin)) __attribute__((deprecated("__ballot""() is deprecated in favor of ""__ballot""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned int __ballot(int);
extern __attribute__((device)) __attribute__((device_builtin)) int __syncthreads_count(int);
extern __attribute__((device)) __attribute__((device_builtin)) int __syncthreads_and(int);
extern __attribute__((device)) __attribute__((device_builtin)) int __syncthreads_or(int);
extern __attribute__((device)) __attribute__((device_builtin)) long long int clock64(void);






extern __attribute__((device)) __attribute__((device_builtin)) float __fmaf_ieee_rn(float, float, float);
extern __attribute__((device)) __attribute__((device_builtin)) float __fmaf_ieee_rz(float, float, float);
extern __attribute__((device)) __attribute__((device_builtin)) float __fmaf_ieee_ru(float, float, float);
extern __attribute__((device)) __attribute__((device_builtin)) float __fmaf_ieee_rd(float, float, float);
# 431 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) long long int __double_as_longlong(double x);
# 440 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __longlong_as_double(long long int x);
# 597 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __fma_rn(double x, double y, double z);
# 754 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __fma_rz(double x, double y, double z);
# 911 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __fma_ru(double x, double y, double z);
# 1068 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __fma_rd(double x, double y, double z);
# 1080 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dadd_rn(double x, double y);
# 1092 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dadd_rz(double x, double y);
# 1104 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dadd_ru(double x, double y);
# 1116 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dadd_rd(double x, double y);
# 1128 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsub_rn(double x, double y);
# 1140 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsub_rz(double x, double y);
# 1152 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsub_ru(double x, double y);
# 1164 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dsub_rd(double x, double y);
# 1176 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dmul_rn(double x, double y);
# 1188 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dmul_rz(double x, double y);
# 1200 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dmul_ru(double x, double y);
# 1212 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __dmul_rd(double x, double y);
# 1221 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) float __double2float_rn(double x);
# 1230 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) float __double2float_rz(double x);
# 1239 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) float __double2float_ru(double x);
# 1248 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) float __double2float_rd(double x);
# 1257 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2int_rn(double x);
# 1266 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2int_ru(double x);
# 1275 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2int_rd(double x);
# 1284 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __double2uint_rn(double x);
# 1293 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __double2uint_ru(double x);
# 1302 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __double2uint_rd(double x);
# 1311 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) long long int __double2ll_rn(double x);
# 1320 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) long long int __double2ll_ru(double x);
# 1329 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) long long int __double2ll_rd(double x);
# 1338 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __double2ull_rn(double x);
# 1347 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __double2ull_ru(double x);
# 1356 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) unsigned long long int __double2ull_rd(double x);







extern __attribute__((device)) __attribute__((device_builtin)) double __int2double_rn(int x);







extern __attribute__((device)) __attribute__((device_builtin)) double __uint2double_rn(unsigned int x);
# 1381 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ll2double_rn(long long int x);
# 1390 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ll2double_rz(long long int x);
# 1399 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ll2double_ru(long long int x);
# 1408 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ll2double_rd(long long int x);
# 1417 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ull2double_rn(unsigned long long int x);
# 1426 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ull2double_rz(unsigned long long int x);
# 1435 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ull2double_ru(unsigned long long int x);
# 1444 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __ull2double_rd(unsigned long long int x);
# 1453 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2hiint(double x);
# 1462 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) int __double2loint(double x);
# 1472 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h"
extern __attribute__((device)) __attribute__((device_builtin)) double __hiloint2double(int hi, int lo);


}






static __inline__ __attribute__((device)) __attribute__((deprecated("__ballot""() is deprecated in favor of ""__ballot""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned int ballot(bool pred) ;

static __inline__ __attribute__((device)) int syncthreads_count(bool pred) ;

static __inline__ __attribute__((device)) bool syncthreads_and(bool pred) ;

static __inline__ __attribute__((device)) bool syncthreads_or(bool pred) ;




static __inline__ __attribute__((device)) unsigned int __isGlobal(const void *ptr) ;
static __inline__ __attribute__((device)) unsigned int __isShared(const void *ptr) ;
static __inline__ __attribute__((device)) unsigned int __isConstant(const void *ptr) ;
static __inline__ __attribute__((device)) unsigned int __isLocal(const void *ptr) ;







# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.hpp" 1
# 75 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.hpp"
static __inline__ __attribute__((device)) unsigned int ballot(bool pred)
{
  return __ballot((int)pred);
}

static __inline__ __attribute__((device)) int syncthreads_count(bool pred)
{
  return __syncthreads_count((int)pred);
}

static __inline__ __attribute__((device)) bool syncthreads_and(bool pred)
{
  return (bool)__syncthreads_and((int)pred);
}

static __inline__ __attribute__((device)) bool syncthreads_or(bool pred)
{
  return (bool)__syncthreads_or((int)pred);
}


extern "C" __attribute__((device)) unsigned __nv_isGlobal_impl(const void *);
extern "C" __attribute__((device)) unsigned __nv_isShared_impl(const void *);
extern "C" __attribute__((device)) unsigned __nv_isConstant_impl(const void *);
extern "C" __attribute__((device)) unsigned __nv_isLocal_impl(const void *);

static __inline__ __attribute__((device)) unsigned int __isGlobal(const void *ptr)
{
  return __nv_isGlobal_impl(ptr);
}

static __inline__ __attribute__((device)) unsigned int __isShared(const void *ptr)
{
  return __nv_isShared_impl(ptr);
}

static __inline__ __attribute__((device)) unsigned int __isConstant(const void *ptr)
{
  return __nv_isConstant_impl(ptr);
}

static __inline__ __attribute__((device)) unsigned int __isLocal(const void *ptr)
{
  return __nv_isLocal_impl(ptr);
}
# 1505 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_20_intrinsics.h" 2
# 3285 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.h" 1
# 102 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.h"
static __attribute__((device)) __inline__ unsigned __fns(unsigned mask, unsigned base, int offset) ;
static __attribute__((device)) __inline__ void __barrier_sync(unsigned id) ;
static __attribute__((device)) __inline__ void __barrier_sync_count(unsigned id, unsigned cnt) ;
static __attribute__((device)) __inline__ void __syncwarp(unsigned mask=0xFFFFFFFF) ;
static __attribute__((device)) __inline__ int __all_sync(unsigned mask, int pred) ;
static __attribute__((device)) __inline__ int __any_sync(unsigned mask, int pred) ;
static __attribute__((device)) __inline__ int __uni_sync(unsigned mask, int pred) ;
static __attribute__((device)) __inline__ unsigned __ballot_sync(unsigned mask, int pred) ;
static __attribute__((device)) __inline__ unsigned __activemask() ;
# 119 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.h"
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl""() is deprecated in favor of ""__shfl""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) int __shfl(int var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl""() is deprecated in favor of ""__shfl""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned int __shfl(unsigned int var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_up""() is deprecated in favor of ""__shfl_up""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) int __shfl_up(int var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_up""() is deprecated in favor of ""__shfl_up""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned int __shfl_up(unsigned int var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_down""() is deprecated in favor of ""__shfl_down""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) int __shfl_down(int var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_down""() is deprecated in favor of ""__shfl_down""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned int __shfl_down(unsigned int var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_xor""() is deprecated in favor of ""__shfl_xor""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) int __shfl_xor(int var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_xor""() is deprecated in favor of ""__shfl_xor""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned int __shfl_xor(unsigned int var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl""() is deprecated in favor of ""__shfl""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) float __shfl(float var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_up""() is deprecated in favor of ""__shfl_up""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) float __shfl_up(float var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_down""() is deprecated in favor of ""__shfl_down""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) float __shfl_down(float var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_xor""() is deprecated in favor of ""__shfl_xor""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) float __shfl_xor(float var, int laneMask, int width=32) ;


static __attribute__((device)) __inline__ int __shfl_sync(unsigned mask, int var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ unsigned int __shfl_sync(unsigned mask, unsigned int var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ int __shfl_up_sync(unsigned mask, int var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ unsigned int __shfl_up_sync(unsigned mask, unsigned int var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ int __shfl_down_sync(unsigned mask, int var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ unsigned int __shfl_down_sync(unsigned mask, unsigned int var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ int __shfl_xor_sync(unsigned mask, int var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ unsigned int __shfl_xor_sync(unsigned mask, unsigned int var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ float __shfl_sync(unsigned mask, float var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ float __shfl_up_sync(unsigned mask, float var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ float __shfl_down_sync(unsigned mask, float var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ float __shfl_xor_sync(unsigned mask, float var, int laneMask, int width=32) ;



static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl""() is deprecated in favor of ""__shfl""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned long long __shfl(unsigned long long var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl""() is deprecated in favor of ""__shfl""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) long long __shfl(long long var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_up""() is deprecated in favor of ""__shfl_up""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) long long __shfl_up(long long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_up""() is deprecated in favor of ""__shfl_up""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned long long __shfl_up(unsigned long long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_down""() is deprecated in favor of ""__shfl_down""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) long long __shfl_down(long long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_down""() is deprecated in favor of ""__shfl_down""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned long long __shfl_down(unsigned long long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_xor""() is deprecated in favor of ""__shfl_xor""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) long long __shfl_xor(long long var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_xor""() is deprecated in favor of ""__shfl_xor""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned long long __shfl_xor(unsigned long long var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl""() is deprecated in favor of ""__shfl""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) double __shfl(double var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_up""() is deprecated in favor of ""__shfl_up""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) double __shfl_up(double var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_down""() is deprecated in favor of ""__shfl_down""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) double __shfl_down(double var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_xor""() is deprecated in favor of ""__shfl_xor""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) double __shfl_xor(double var, int laneMask, int width=32) ;


static __attribute__((device)) __inline__ long long __shfl_sync(unsigned mask, long long var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ unsigned long long __shfl_sync(unsigned mask, unsigned long long var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ long long __shfl_up_sync(unsigned mask, long long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ unsigned long long __shfl_up_sync(unsigned mask, unsigned long long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ long long __shfl_down_sync(unsigned mask, long long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ unsigned long long __shfl_down_sync(unsigned mask, unsigned long long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ long long __shfl_xor_sync(unsigned mask, long long var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ unsigned long long __shfl_xor_sync(unsigned mask, unsigned long long var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ double __shfl_sync(unsigned mask, double var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ double __shfl_up_sync(unsigned mask, double var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ double __shfl_down_sync(unsigned mask, double var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ double __shfl_xor_sync(unsigned mask, double var, int laneMask, int width=32) ;



static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl""() is deprecated in favor of ""__shfl""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) long __shfl(long var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl""() is deprecated in favor of ""__shfl""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned long __shfl(unsigned long var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_up""() is deprecated in favor of ""__shfl_up""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) long __shfl_up(long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_up""() is deprecated in favor of ""__shfl_up""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned long __shfl_up(unsigned long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_down""() is deprecated in favor of ""__shfl_down""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) long __shfl_down(long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_down""() is deprecated in favor of ""__shfl_down""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned long __shfl_down(unsigned long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_xor""() is deprecated in favor of ""__shfl_xor""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) long __shfl_xor(long var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ __attribute__((deprecated("__shfl_xor""() is deprecated in favor of ""__shfl_xor""_sync() and may be removed in a future release (Use -Wno-deprecated-declarations to suppress this warning)."))) unsigned long __shfl_xor(unsigned long var, int laneMask, int width=32) ;


static __attribute__((device)) __inline__ long __shfl_sync(unsigned mask, long var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ unsigned long __shfl_sync(unsigned mask, unsigned long var, int srcLane, int width=32) ;
static __attribute__((device)) __inline__ long __shfl_up_sync(unsigned mask, long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ unsigned long __shfl_up_sync(unsigned mask, unsigned long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ long __shfl_down_sync(unsigned mask, long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ unsigned long __shfl_down_sync(unsigned mask, unsigned long var, unsigned int delta, int width=32) ;
static __attribute__((device)) __inline__ long __shfl_xor_sync(unsigned mask, long var, int laneMask, int width=32) ;
static __attribute__((device)) __inline__ unsigned long __shfl_xor_sync(unsigned mask, unsigned long var, int laneMask, int width=32) ;
# 212 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.hpp" 1
# 73 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.hpp"
extern "C"
{
}
# 89 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.hpp"
static __attribute__((device)) __inline__
unsigned __fns(unsigned mask, unsigned base, int offset) {
  extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __nvvm_fns(unsigned int mask, unsigned int base, int offset);
  return __nvvm_fns(mask, base, offset);
}

static __attribute__((device)) __inline__
void __barrier_sync(unsigned id) {
  extern __attribute__((device)) __attribute__((device_builtin)) void __nvvm_barrier_sync(unsigned id);
  return __nvvm_barrier_sync(id);
}

static __attribute__((device)) __inline__
void __barrier_sync_count(unsigned id, unsigned cnt) {
  extern __attribute__((device)) __attribute__((device_builtin)) void __nvvm_barrier_sync_cnt(unsigned id, unsigned cnt);
  return __nvvm_barrier_sync_cnt(id, cnt);
}

static __attribute__((device)) __inline__
void __syncwarp(unsigned mask) {
  extern __attribute__((device)) __attribute__((device_builtin)) void __nvvm_bar_warp_sync(unsigned mask);
  return __nvvm_bar_warp_sync(mask);
}

static __attribute__((device)) __inline__
int __all_sync(unsigned mask, int pred) {
  extern __attribute__((device)) __attribute__((device_builtin)) int __nvvm_vote_all_sync(unsigned int mask, int pred);
  return __nvvm_vote_all_sync(mask, pred);
}

static __attribute__((device)) __inline__
int __any_sync(unsigned mask, int pred) {
  extern __attribute__((device)) __attribute__((device_builtin)) int __nvvm_vote_any_sync(unsigned int mask, int pred);
  return __nvvm_vote_any_sync(mask, pred);
}

static __attribute__((device)) __inline__
int __uni_sync(unsigned mask, int pred) {
  extern __attribute__((device)) __attribute__((device_builtin)) int __nvvm_vote_uni_sync(unsigned int mask, int pred);
  return __nvvm_vote_uni_sync(mask, pred);
}

static __attribute__((device)) __inline__
unsigned __ballot_sync(unsigned mask, int pred) {
  extern __attribute__((device)) __attribute__((device_builtin)) unsigned int __nvvm_vote_ballot_sync(unsigned int mask, int pred);
  return __nvvm_vote_ballot_sync(mask, pred);
}

static __attribute__((device)) __inline__
unsigned __activemask() {
    unsigned ret;
    asm volatile ("activemask.b32 %0;" : "=r"(ret));
    return ret;
}




static __attribute__((device)) __inline__ int __shfl(int var, int srcLane, int width) {
 int ret;
 int c = ((32 -width) << 8) | 0x1f;
 asm volatile ("shfl.idx.b32 %0, %1, %2, %3;" : "=r"(ret) : "r"(var), "r"(srcLane), "r"(c));
 return ret;
}

static __attribute__((device)) __inline__ unsigned int __shfl(unsigned int var, int srcLane, int width) {
 return (unsigned int) __shfl((int)var, srcLane, width);
}

static __attribute__((device)) __inline__ int __shfl_up(int var, unsigned int delta, int width) {
 int ret;
 int c = (32 -width) << 8;
 asm volatile ("shfl.up.b32 %0, %1, %2, %3;" : "=r"(ret) : "r"(var), "r"(delta), "r"(c));
 return ret;
}

static __attribute__((device)) __inline__ unsigned int __shfl_up(unsigned int var, unsigned int delta, int width) {
 return (unsigned int) __shfl_up((int)var, delta, width);
}

static __attribute__((device)) __inline__ int __shfl_down(int var, unsigned int delta, int width) {
 int ret;
 int c = ((32 -width) << 8) | 0x1f;
 asm volatile ("shfl.down.b32 %0, %1, %2, %3;" : "=r"(ret) : "r"(var), "r"(delta), "r"(c));
 return ret;
}

static __attribute__((device)) __inline__ unsigned int __shfl_down(unsigned int var, unsigned int delta, int width) {
 return (unsigned int) __shfl_down((int)var, delta, width);
}

static __attribute__((device)) __inline__ int __shfl_xor(int var, int laneMask, int width) {
 int ret;
 int c = ((32 -width) << 8) | 0x1f;
 asm volatile ("shfl.bfly.b32 %0, %1, %2, %3;" : "=r"(ret) : "r"(var), "r"(laneMask), "r"(c));
 return ret;
}

static __attribute__((device)) __inline__ unsigned int __shfl_xor(unsigned int var, int laneMask, int width) {
 return (unsigned int) __shfl_xor((int)var, laneMask, width);
}

static __attribute__((device)) __inline__ float __shfl(float var, int srcLane, int width) {
 float ret;
        int c;
 c = ((32 -width) << 8) | 0x1f;
 asm volatile ("shfl.idx.b32 %0, %1, %2, %3;" : "=f"(ret) : "f"(var), "r"(srcLane), "r"(c));
 return ret;
}

static __attribute__((device)) __inline__ float __shfl_up(float var, unsigned int delta, int width) {
 float ret;
        int c;
 c = (32 -width) << 8;
 asm volatile ("shfl.up.b32 %0, %1, %2, %3;" : "=f"(ret) : "f"(var), "r"(delta), "r"(c));
 return ret;
}

static __attribute__((device)) __inline__ float __shfl_down(float var, unsigned int delta, int width) {
 float ret;
        int c;
 c = ((32 -width) << 8) | 0x1f;
 asm volatile ("shfl.down.b32 %0, %1, %2, %3;" : "=f"(ret) : "f"(var), "r"(delta), "r"(c));
 return ret;
}

static __attribute__((device)) __inline__ float __shfl_xor(float var, int laneMask, int width) {
 float ret;
        int c;
 c = ((32 -width) << 8) | 0x1f;
 asm volatile ("shfl.bfly.b32 %0, %1, %2, %3;" : "=f"(ret) : "f"(var), "r"(laneMask), "r"(c));
 return ret;
}



static __attribute__((device)) __inline__ long long __shfl(long long var, int srcLane, int width) {
 int lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "l"(var));
 hi = __shfl(hi, srcLane, width);
 lo = __shfl(lo, srcLane, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=l"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ unsigned long long __shfl(unsigned long long var, int srcLane, int width) {
 return (unsigned long long) __shfl((long long) var, srcLane, width);
}

static __attribute__((device)) __inline__ long long __shfl_up(long long var, unsigned int delta, int width) {
 int lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "l"(var));
 hi = __shfl_up(hi, delta, width);
 lo = __shfl_up(lo, delta, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=l"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ unsigned long long __shfl_up(unsigned long long var, unsigned int delta, int width) {
 return (unsigned long long) __shfl_up((long long) var, delta, width);
}

static __attribute__((device)) __inline__ long long __shfl_down(long long var, unsigned int delta, int width) {
 int lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "l"(var));
 hi = __shfl_down(hi, delta, width);
 lo = __shfl_down(lo, delta, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=l"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ unsigned long long __shfl_down(unsigned long long var, unsigned int delta, int width) {
 return (unsigned long long) __shfl_down((long long) var, delta, width);
}

static __attribute__((device)) __inline__ long long __shfl_xor(long long var, int laneMask, int width) {
 int lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "l"(var));
 hi = __shfl_xor(hi, laneMask, width);
 lo = __shfl_xor(lo, laneMask, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=l"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ unsigned long long __shfl_xor(unsigned long long var, int laneMask, int width) {
 return (unsigned long long) __shfl_xor((long long) var, laneMask, width);
}

static __attribute__((device)) __inline__ double __shfl(double var, int srcLane, int width) {
 unsigned lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "d"(var));
 hi = __shfl(hi, srcLane, width);
 lo = __shfl(lo, srcLane, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=d"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ double __shfl_up(double var, unsigned int delta, int width) {
 unsigned lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "d"(var));
 hi = __shfl_up(hi, delta, width);
 lo = __shfl_up(lo, delta, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=d"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ double __shfl_down(double var, unsigned int delta, int width) {
 unsigned lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "d"(var));
 hi = __shfl_down(hi, delta, width);
 lo = __shfl_down(lo, delta, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=d"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ double __shfl_xor(double var, int laneMask, int width) {
 unsigned lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "d"(var));
 hi = __shfl_xor(hi, laneMask, width);
 lo = __shfl_xor(lo, laneMask, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=d"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ long __shfl(long var, int srcLane, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl((long long) var, srcLane, width) :
  __shfl((int) var, srcLane, width);
}

static __attribute__((device)) __inline__ unsigned long __shfl(unsigned long var, int srcLane, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl((unsigned long long) var, srcLane, width) :
  __shfl((unsigned int) var, srcLane, width);
}

static __attribute__((device)) __inline__ long __shfl_up(long var, unsigned int delta, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_up((long long) var, delta, width) :
  __shfl_up((int) var, delta, width);
}

static __attribute__((device)) __inline__ unsigned long __shfl_up(unsigned long var, unsigned int delta, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_up((unsigned long long) var, delta, width) :
  __shfl_up((unsigned int) var, delta, width);
}

static __attribute__((device)) __inline__ long __shfl_down(long var, unsigned int delta, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_down((long long) var, delta, width) :
  __shfl_down((int) var, delta, width);
}

static __attribute__((device)) __inline__ unsigned long __shfl_down(unsigned long var, unsigned int delta, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_down((unsigned long long) var, delta, width) :
  __shfl_down((unsigned int) var, delta, width);
}

static __attribute__((device)) __inline__ long __shfl_xor(long var, int laneMask, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_xor((long long) var, laneMask, width) :
  __shfl_xor((int) var, laneMask, width);
}

static __attribute__((device)) __inline__ unsigned long __shfl_xor(unsigned long var, int laneMask, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_xor((unsigned long long) var, laneMask, width) :
  __shfl_xor((unsigned int) var, laneMask, width);
}
# 369 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.hpp"
static __attribute__((device)) __inline__ int __shfl_sync(unsigned mask, int var, int srcLane, int width) {
        extern __attribute__((device)) __attribute__((device_builtin)) unsigned __nvvm_shfl_idx_sync(unsigned mask, unsigned a, unsigned b, unsigned c);
 int ret;
 int c = ((32 -width) << 8) | 0x1f;
        ret = __nvvm_shfl_idx_sync(mask, var, srcLane, c);
 return ret;
}

static __attribute__((device)) __inline__ unsigned int __shfl_sync(unsigned mask, unsigned int var, int srcLane, int width) {
        return (unsigned int) __shfl_sync(mask, (int)var, srcLane, width);
}

static __attribute__((device)) __inline__ int __shfl_up_sync(unsigned mask, int var, unsigned int delta, int width) {
        extern __attribute__((device)) __attribute__((device_builtin)) unsigned __nvvm_shfl_up_sync(unsigned mask, unsigned a, unsigned b, unsigned c);
 int ret;
 int c = (32 -width) << 8;
        ret = __nvvm_shfl_up_sync(mask, var, delta, c);
 return ret;
}

static __attribute__((device)) __inline__ unsigned int __shfl_up_sync(unsigned mask, unsigned int var, unsigned int delta, int width) {
        return (unsigned int) __shfl_up_sync(mask, (int)var, delta, width);
}

static __attribute__((device)) __inline__ int __shfl_down_sync(unsigned mask, int var, unsigned int delta, int width) {
        extern __attribute__((device)) __attribute__((device_builtin)) unsigned __nvvm_shfl_down_sync(unsigned mask, unsigned a, unsigned b, unsigned c);
 int ret;
 int c = ((32 -width) << 8) | 0x1f;
        ret = __nvvm_shfl_down_sync(mask, var, delta, c);
 return ret;
}

static __attribute__((device)) __inline__ unsigned int __shfl_down_sync(unsigned mask, unsigned int var, unsigned int delta, int width) {
        return (unsigned int) __shfl_down_sync(mask, (int)var, delta, width);
}

static __attribute__((device)) __inline__ int __shfl_xor_sync(unsigned mask, int var, int laneMask, int width) {
        extern __attribute__((device)) __attribute__((device_builtin)) unsigned __nvvm_shfl_bfly_sync(unsigned mask, unsigned a, unsigned b, unsigned c);
 int ret;
 int c = ((32 -width) << 8) | 0x1f;
        ret = __nvvm_shfl_bfly_sync(mask, var, laneMask, c);
 return ret;
}

static __attribute__((device)) __inline__ unsigned int __shfl_xor_sync(unsigned mask, unsigned int var, int laneMask, int width) {
 return (unsigned int) __shfl_xor_sync(mask, (int)var, laneMask, width);
}

static __attribute__((device)) __inline__ float __shfl_sync(unsigned mask, float var, int srcLane, int width) {
        extern __attribute__((device)) __attribute__((device_builtin)) unsigned __nvvm_shfl_idx_sync(unsigned mask, unsigned a, unsigned b, unsigned c);
        int ret;
        int c;
 c = ((32 -width) << 8) | 0x1f;
        ret = __nvvm_shfl_idx_sync(mask, __float_as_int(var), srcLane, c);
 return __int_as_float(ret);
}

static __attribute__((device)) __inline__ float __shfl_up_sync(unsigned mask, float var, unsigned int delta, int width) {
        extern __attribute__((device)) __attribute__((device_builtin)) unsigned __nvvm_shfl_up_sync(unsigned mask, unsigned a, unsigned b, unsigned c);
 int ret;
        int c;
 c = (32 -width) << 8;
        ret = __nvvm_shfl_up_sync(mask, __float_as_int(var), delta, c);
 return __int_as_float(ret);
}

static __attribute__((device)) __inline__ float __shfl_down_sync(unsigned mask, float var, unsigned int delta, int width) {
        extern __attribute__((device)) __attribute__((device_builtin)) unsigned __nvvm_shfl_down_sync(unsigned mask, unsigned a, unsigned b, unsigned c);
 int ret;
        int c;
 c = ((32 -width) << 8) | 0x1f;
        ret = __nvvm_shfl_down_sync(mask, __float_as_int(var), delta, c);
 return __int_as_float(ret);
}

static __attribute__((device)) __inline__ float __shfl_xor_sync(unsigned mask, float var, int laneMask, int width) {
        extern __attribute__((device)) __attribute__((device_builtin)) unsigned __nvvm_shfl_bfly_sync(unsigned mask, unsigned a, unsigned b, unsigned c);
 int ret;
        int c;
 c = ((32 -width) << 8) | 0x1f;
        ret = __nvvm_shfl_bfly_sync(mask, __float_as_int(var), laneMask, c);
 return __int_as_float(ret);
}


static __attribute__((device)) __inline__ long long __shfl_sync(unsigned mask, long long var, int srcLane, int width) {
 int lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "l"(var));
 hi = __shfl_sync(mask, hi, srcLane, width);
 lo = __shfl_sync(mask, lo, srcLane, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=l"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ unsigned long long __shfl_sync(unsigned mask, unsigned long long var, int srcLane, int width) {
        return (unsigned long long) __shfl_sync(mask, (long long) var, srcLane, width);
}

static __attribute__((device)) __inline__ long long __shfl_up_sync(unsigned mask, long long var, unsigned int delta, int width) {
 int lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "l"(var));
 hi = __shfl_up_sync(mask, hi, delta, width);
 lo = __shfl_up_sync(mask, lo, delta, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=l"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ unsigned long long __shfl_up_sync(unsigned mask, unsigned long long var, unsigned int delta, int width) {
        return (unsigned long long) __shfl_up_sync(mask, (long long) var, delta, width);
}

static __attribute__((device)) __inline__ long long __shfl_down_sync(unsigned mask, long long var, unsigned int delta, int width) {
 int lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "l"(var));
 hi = __shfl_down_sync(mask, hi, delta, width);
 lo = __shfl_down_sync(mask, lo, delta, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=l"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ unsigned long long __shfl_down_sync(unsigned mask, unsigned long long var, unsigned int delta, int width) {
        return (unsigned long long) __shfl_down_sync(mask, (long long) var, delta, width);
}

static __attribute__((device)) __inline__ long long __shfl_xor_sync(unsigned mask, long long var, int laneMask, int width) {
 int lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "l"(var));
 hi = __shfl_xor_sync(mask, hi, laneMask, width);
 lo = __shfl_xor_sync(mask, lo, laneMask, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=l"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ unsigned long long __shfl_xor_sync(unsigned mask, unsigned long long var, int laneMask, int width) {
        return (unsigned long long) __shfl_xor_sync(mask, (long long) var, laneMask, width);
}

static __attribute__((device)) __inline__ double __shfl_sync(unsigned mask, double var, int srcLane, int width) {
 unsigned lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "d"(var));
 hi = __shfl_sync(mask, hi, srcLane, width);
 lo = __shfl_sync(mask, lo, srcLane, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=d"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ double __shfl_up_sync(unsigned mask, double var, unsigned int delta, int width) {
 unsigned lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "d"(var));
 hi = __shfl_up_sync(mask, hi, delta, width);
 lo = __shfl_up_sync(mask, lo, delta, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=d"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ double __shfl_down_sync(unsigned mask, double var, unsigned int delta, int width) {
 unsigned lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "d"(var));
 hi = __shfl_down_sync(mask, hi, delta, width);
 lo = __shfl_down_sync(mask, lo, delta, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=d"(var) : "r"(lo), "r"(hi));
 return var;
}

static __attribute__((device)) __inline__ double __shfl_xor_sync(unsigned mask, double var, int laneMask, int width) {
 unsigned lo, hi;
 asm volatile("mov.b64 {%0,%1}, %2;" : "=r"(lo), "=r"(hi) : "d"(var));
 hi = __shfl_xor_sync(mask, hi, laneMask, width);
 lo = __shfl_xor_sync(mask, lo, laneMask, width);
 asm volatile("mov.b64 %0, {%1,%2};" : "=d"(var) : "r"(lo), "r"(hi));
 return var;
}



static __attribute__((device)) __inline__ long __shfl_sync(unsigned mask, long var, int srcLane, int width) {
 return (sizeof(long) == sizeof(long long)) ?
                __shfl_sync(mask, (long long) var, srcLane, width) :
  __shfl_sync(mask, (int) var, srcLane, width);
}

static __attribute__((device)) __inline__ unsigned long __shfl_sync(unsigned mask, unsigned long var, int srcLane, int width) {
 return (sizeof(long) == sizeof(long long)) ?
                __shfl_sync(mask, (unsigned long long) var, srcLane, width) :
  __shfl_sync(mask, (unsigned int) var, srcLane, width);
}

static __attribute__((device)) __inline__ long __shfl_up_sync(unsigned mask, long var, unsigned int delta, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_up_sync(mask, (long long) var, delta, width) :
  __shfl_up_sync(mask, (int) var, delta, width);
}

static __attribute__((device)) __inline__ unsigned long __shfl_up_sync(unsigned mask, unsigned long var, unsigned int delta, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_up_sync(mask, (unsigned long long) var, delta, width) :
  __shfl_up_sync(mask, (unsigned int) var, delta, width);
}

static __attribute__((device)) __inline__ long __shfl_down_sync(unsigned mask, long var, unsigned int delta, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_down_sync(mask, (long long) var, delta, width) :
  __shfl_down_sync(mask, (int) var, delta, width);
}

static __attribute__((device)) __inline__ unsigned long __shfl_down_sync(unsigned mask, unsigned long var, unsigned int delta, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_down_sync(mask, (unsigned long long) var, delta, width) :
  __shfl_down_sync(mask, (unsigned int) var, delta, width);
}

static __attribute__((device)) __inline__ long __shfl_xor_sync(unsigned mask, long var, int laneMask, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_xor_sync(mask, (long long) var, laneMask, width) :
  __shfl_xor_sync(mask, (int) var, laneMask, width);
}

static __attribute__((device)) __inline__ unsigned long __shfl_xor_sync(unsigned mask, unsigned long var, int laneMask, int width) {
 return (sizeof(long) == sizeof(long long)) ?
  __shfl_xor_sync(mask, (unsigned long long) var, laneMask, width) :
  __shfl_xor_sync(mask, (unsigned int) var, laneMask, width);
}
# 213 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_30_intrinsics.h" 2
# 3286 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_intrinsics.h" 1
# 291 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_intrinsics.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_intrinsics.hpp" 1
# 292 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_intrinsics.h" 2
# 3287 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_35_intrinsics.h" 1
# 111 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_35_intrinsics.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_32_intrinsics.h" 1
# 112 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_35_intrinsics.h" 2
# 3288 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_61_intrinsics.h" 1
# 120 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_61_intrinsics.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_61_intrinsics.hpp" 1
# 121 "/usr/local/cuda/bin/../targets/x86_64-linux/include/sm_61_intrinsics.h" 2
# 3289 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/sm_70_rt.h" 1
# 123 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/sm_70_rt.h"
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/sm_70_rt.hpp" 1
# 124 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/sm_70_rt.h" 2
# 3290 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/surface_functions.h" 1
# 65 "/usr/local/cuda/bin/../targets/x86_64-linux/include/surface_functions.h"
template <typename T> struct __nv_surf_trait { typedef void * cast_type; };

template<> struct __nv_surf_trait<char> { typedef char * cast_type; };
template<> struct __nv_surf_trait<signed char> { typedef signed char * cast_type; };
template<> struct __nv_surf_trait<unsigned char> { typedef unsigned char * cast_type; };
template<> struct __nv_surf_trait<char1> { typedef char1 * cast_type; };
template<> struct __nv_surf_trait<uchar1> { typedef uchar1 * cast_type; };
template<> struct __nv_surf_trait<char2> { typedef char2 * cast_type; };
template<> struct __nv_surf_trait<uchar2> { typedef uchar2 * cast_type; };
template<> struct __nv_surf_trait<char4> { typedef char4 * cast_type; };
template<> struct __nv_surf_trait<uchar4> { typedef uchar4 * cast_type; };
template<> struct __nv_surf_trait<short> { typedef short * cast_type; };
template<> struct __nv_surf_trait<unsigned short> { typedef unsigned short * cast_type; };
template<> struct __nv_surf_trait<short1> { typedef short1 * cast_type; };
template<> struct __nv_surf_trait<ushort1> { typedef ushort1 * cast_type; };
template<> struct __nv_surf_trait<short2> { typedef short2 * cast_type; };
template<> struct __nv_surf_trait<ushort2> { typedef ushort2 * cast_type; };
template<> struct __nv_surf_trait<short4> { typedef short4 * cast_type; };
template<> struct __nv_surf_trait<ushort4> { typedef ushort4 * cast_type; };
template<> struct __nv_surf_trait<int> { typedef int * cast_type; };
template<> struct __nv_surf_trait<unsigned int> { typedef unsigned int * cast_type; };
template<> struct __nv_surf_trait<int1> { typedef int1 * cast_type; };
template<> struct __nv_surf_trait<uint1> { typedef uint1 * cast_type; };
template<> struct __nv_surf_trait<int2> { typedef int2 * cast_type; };
template<> struct __nv_surf_trait<uint2> { typedef uint2 * cast_type; };
template<> struct __nv_surf_trait<int4> { typedef int4 * cast_type; };
template<> struct __nv_surf_trait<uint4> { typedef uint4 * cast_type; };
template<> struct __nv_surf_trait<long long> { typedef long long * cast_type; };
template<> struct __nv_surf_trait<unsigned long long> { typedef unsigned long long * cast_type; };
template<> struct __nv_surf_trait<longlong1> { typedef longlong1 * cast_type; };
template<> struct __nv_surf_trait<ulonglong1> { typedef ulonglong1 * cast_type; };
template<> struct __nv_surf_trait<longlong2> { typedef longlong2 * cast_type; };
template<> struct __nv_surf_trait<ulonglong2> { typedef ulonglong2 * cast_type; };
# 108 "/usr/local/cuda/bin/../targets/x86_64-linux/include/surface_functions.h"
template<> struct __nv_surf_trait<float> { typedef float * cast_type; };
template<> struct __nv_surf_trait<float1> { typedef float1 * cast_type; };
template<> struct __nv_surf_trait<float2> { typedef float2 * cast_type; };
template<> struct __nv_surf_trait<float4> { typedef float4 * cast_type; };


template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf1Dread(T *res, surface<void, 0x01> surf, int x, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf1Dread_v2", (void *)res, s, surf, x, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) T surf1Dread(surface<void, 0x01> surf, int x, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  T temp;
  __nv_tex_surf_handler("__surf1Dread_v2", (typename __nv_surf_trait<T>::cast_type)&temp, (int)sizeof(T), surf, x, mode);
  return temp;

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf1Dread(T *res, surface<void, 0x01> surf, int x, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  *res = surf1Dread<T>(surf, x, mode);

}


template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf2Dread(T *res, surface<void, 0x02> surf, int x, int y, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf2Dread_v2", (void *)res, s, surf, x, y, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) T surf2Dread(surface<void, 0x02> surf, int x, int y, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  T temp;
  __nv_tex_surf_handler("__surf2Dread_v2", (typename __nv_surf_trait<T>::cast_type)&temp, (int)sizeof(T), surf, x, y, mode);
  return temp;

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf2Dread(T *res, surface<void, 0x02> surf, int x, int y, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  *res = surf2Dread<T>(surf, x, y, mode);

}


template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf3Dread(T *res, surface<void, 0x03> surf, int x, int y, int z, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf3Dread_v2", (void *)res, s, surf, x, y, z, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) T surf3Dread(surface<void, 0x03> surf, int x, int y, int z, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  T temp;
  __nv_tex_surf_handler("__surf3Dread_v2", (typename __nv_surf_trait<T>::cast_type)&temp, (int)sizeof(T), surf, x, y, z, mode);
  return temp;

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf3Dread(T *res, surface<void, 0x03> surf, int x, int y, int z, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  *res = surf3Dread<T>(surf, x, y, z, mode);

}



template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf1DLayeredread(T *res, surface<void, 0xF1> surf, int x, int layer, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf1DLayeredread_v2", (void *)res, s, surf, x, layer, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) T surf1DLayeredread(surface<void, 0xF1> surf, int x, int layer, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  T temp;
  __nv_tex_surf_handler("__surf1DLayeredread_v2", (typename __nv_surf_trait<T>::cast_type)&temp, (int)sizeof(T), surf, x, layer, mode);
  return temp;

}


template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf1DLayeredread(T *res, surface<void, 0xF1> surf, int x, int layer, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  *res = surf1DLayeredread<T>(surf, x, layer, mode);

}


template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf2DLayeredread(T *res, surface<void, 0xF2> surf, int x, int y, int layer, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf2DLayeredread_v2", (void *)res, s, surf, x, y, layer, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) T surf2DLayeredread(surface<void, 0xF2> surf, int x, int y, int layer, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  T temp;
  __nv_tex_surf_handler("__surf2DLayeredread_v2", (typename __nv_surf_trait<T>::cast_type)&temp, (int)sizeof(T), surf, x, y, layer, mode);
  return temp;

}


template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf2DLayeredread(T *res, surface<void, 0xF2> surf, int x, int y, int layer, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  *res = surf2DLayeredread<T>(surf, x, y, layer, mode);

}


template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surfCubemapread(T *res, surface<void, 0x0C> surf, int x, int y, int face, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surfCubemapread_v2", (void *)res, s, surf, x, y, face, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) T surfCubemapread(surface<void, 0x0C> surf, int x, int y, int face, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  T temp;

  __nv_tex_surf_handler("__surfCubemapread_v2", (typename __nv_surf_trait<T>::cast_type)&temp, (int)sizeof(T), surf, x, y, face, mode);
  return temp;

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surfCubemapread(T *res, surface<void, 0x0C> surf, int x, int y, int face, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  *res = surfCubemapread<T>(surf, x, y, face, mode);

}


template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surfCubemapLayeredread(T *res, surface<void, 0xFC> surf, int x, int y, int layerFace, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surfCubemapLayeredread_v2", (void *)res, s, surf, x, y, layerFace, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) T surfCubemapLayeredread(surface<void, 0xFC> surf, int x, int y, int layerFace, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  T temp;
  __nv_tex_surf_handler("__surfCubemapLayeredread_v2", (typename __nv_surf_trait<T>::cast_type)&temp, (int)sizeof(T), surf, x, y, layerFace, mode);
  return temp;

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surfCubemapLayeredread(T *res, surface<void, 0xFC> surf, int x, int y, int layerFace, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  *res = surfCubemapLayeredread<T>(surf, x, y, layerFace, mode);

}


template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf1Dwrite(T val, surface<void, 0x01> surf, int x, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf1Dwrite_v2", (void *)&val, s, surf, x, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf1Dwrite(T val, surface<void, 0x01> surf, int x, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf1Dwrite_v2", (typename __nv_surf_trait<T>::cast_type)&val, (int)sizeof(T), surf, x, mode);

}



template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf2Dwrite(T val, surface<void, 0x02> surf, int x, int y, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf2Dwrite_v2", (void *)&val, s, surf, x, y, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf2Dwrite(T val, surface<void, 0x02> surf, int x, int y, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf2Dwrite_v2", (typename __nv_surf_trait<T>::cast_type)&val, (int)sizeof(T), surf, x, y, mode);

}


template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf3Dwrite(T val, surface<void, 0x03> surf, int x, int y, int z, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf3Dwrite_v2", (void *)&val, s, surf, x, y, z,mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf3Dwrite(T val, surface<void, 0x03> surf, int x, int y, int z, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf3Dwrite_v2", (typename __nv_surf_trait<T>::cast_type)&val, (int)sizeof(T), surf, x, y, z, mode);

}


template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf1DLayeredwrite(T val, surface<void, 0xF1> surf, int x, int layer, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf1DLayeredwrite_v2", (void *)&val, s, surf, x, layer,mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf1DLayeredwrite(T val, surface<void, 0xF1> surf, int x, int layer, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf1DLayeredwrite_v2", (typename __nv_surf_trait<T>::cast_type)&val, (int)sizeof(T), surf, x, layer, mode);

}


template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf2DLayeredwrite(T val, surface<void, 0xF2> surf, int x, int y, int layer, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf2DLayeredwrite_v2", (void *)&val, s, surf, x, y, layer,mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surf2DLayeredwrite(T val, surface<void, 0xF2> surf, int x, int y, int layer, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surf2DLayeredwrite_v2", (typename __nv_surf_trait<T>::cast_type)&val, (int)sizeof(T), surf, x, y, layer, mode);

}


template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surfCubemapwrite(T val, surface<void, 0x0C> surf, int x, int y, int face, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surfCubemapwrite_v2", (void *)&val, s, surf, x, y, face, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surfCubemapwrite(T val, surface<void, 0x0C> surf, int x, int y, int face, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surfCubemapwrite_v2", (typename __nv_surf_trait<T>::cast_type)&val, (int)sizeof(T), surf, x, y, face, mode);

}



template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surfCubemapLayeredwrite(T val, surface<void, 0xFC> surf, int x, int y, int layerFace, int s, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surfCubemapLayeredwrite_v2", (void *)&val, s, surf, x, y, layerFace, mode);

}

template<class T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) void surfCubemapLayeredwrite(T val, surface<void, 0xFC> surf, int x, int y, int layerFace, enum cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__surfCubemapLayeredwrite_v2", (typename __nv_surf_trait<T>::cast_type)&val, (int)sizeof(T), surf, x, y, layerFace, mode);

}
# 3291 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_fetch_functions.h" 1
# 66 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_fetch_functions.h"
template <typename T>
struct __nv_tex_rmet_ret { };

template<> struct __nv_tex_rmet_ret<char> { typedef char type; };
template<> struct __nv_tex_rmet_ret<signed char> { typedef signed char type; };
template<> struct __nv_tex_rmet_ret<unsigned char> { typedef unsigned char type; };
template<> struct __nv_tex_rmet_ret<char1> { typedef char1 type; };
template<> struct __nv_tex_rmet_ret<uchar1> { typedef uchar1 type; };
template<> struct __nv_tex_rmet_ret<char2> { typedef char2 type; };
template<> struct __nv_tex_rmet_ret<uchar2> { typedef uchar2 type; };
template<> struct __nv_tex_rmet_ret<char4> { typedef char4 type; };
template<> struct __nv_tex_rmet_ret<uchar4> { typedef uchar4 type; };

template<> struct __nv_tex_rmet_ret<short> { typedef short type; };
template<> struct __nv_tex_rmet_ret<unsigned short> { typedef unsigned short type; };
template<> struct __nv_tex_rmet_ret<short1> { typedef short1 type; };
template<> struct __nv_tex_rmet_ret<ushort1> { typedef ushort1 type; };
template<> struct __nv_tex_rmet_ret<short2> { typedef short2 type; };
template<> struct __nv_tex_rmet_ret<ushort2> { typedef ushort2 type; };
template<> struct __nv_tex_rmet_ret<short4> { typedef short4 type; };
template<> struct __nv_tex_rmet_ret<ushort4> { typedef ushort4 type; };

template<> struct __nv_tex_rmet_ret<int> { typedef int type; };
template<> struct __nv_tex_rmet_ret<unsigned int> { typedef unsigned int type; };
template<> struct __nv_tex_rmet_ret<int1> { typedef int1 type; };
template<> struct __nv_tex_rmet_ret<uint1> { typedef uint1 type; };
template<> struct __nv_tex_rmet_ret<int2> { typedef int2 type; };
template<> struct __nv_tex_rmet_ret<uint2> { typedef uint2 type; };
template<> struct __nv_tex_rmet_ret<int4> { typedef int4 type; };
template<> struct __nv_tex_rmet_ret<uint4> { typedef uint4 type; };
# 107 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_fetch_functions.h"
template<> struct __nv_tex_rmet_ret<float> { typedef float type; };
template<> struct __nv_tex_rmet_ret<float1> { typedef float1 type; };
template<> struct __nv_tex_rmet_ret<float2> { typedef float2 type; };
template<> struct __nv_tex_rmet_ret<float4> { typedef float4 type; };


template <typename T> struct __nv_tex_rmet_cast { typedef T* type; };
# 125 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_fetch_functions.h"
template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex1Dfetch(texture<T, 0x01, cudaReadModeElementType> t, int x)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex1Dfetch_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x);
  return temp;

}

template <typename T>
struct __nv_tex_rmnf_ret { };

template <> struct __nv_tex_rmnf_ret<char> { typedef float type; };
template <> struct __nv_tex_rmnf_ret<signed char> { typedef float type; };
template <> struct __nv_tex_rmnf_ret<unsigned char> { typedef float type; };
template <> struct __nv_tex_rmnf_ret<short> { typedef float type; };
template <> struct __nv_tex_rmnf_ret<unsigned short> { typedef float type; };
template <> struct __nv_tex_rmnf_ret<char1> { typedef float1 type; };
template <> struct __nv_tex_rmnf_ret<uchar1> { typedef float1 type; };
template <> struct __nv_tex_rmnf_ret<short1> { typedef float1 type; };
template <> struct __nv_tex_rmnf_ret<ushort1> { typedef float1 type; };
template <> struct __nv_tex_rmnf_ret<char2> { typedef float2 type; };
template <> struct __nv_tex_rmnf_ret<uchar2> { typedef float2 type; };
template <> struct __nv_tex_rmnf_ret<short2> { typedef float2 type; };
template <> struct __nv_tex_rmnf_ret<ushort2> { typedef float2 type; };
template <> struct __nv_tex_rmnf_ret<char4> { typedef float4 type; };
template <> struct __nv_tex_rmnf_ret<uchar4> { typedef float4 type; };
template <> struct __nv_tex_rmnf_ret<short4> { typedef float4 type; };
template <> struct __nv_tex_rmnf_ret<ushort4> { typedef float4 type; };

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex1Dfetch(texture<T, 0x01, cudaReadModeNormalizedFloat> t, int x)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex1Dfetch_rmnf_v2", &type_dummy, &retval, t, x);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex1D(texture<T, 0x01, cudaReadModeElementType> t, float x)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex1D_v2", (typename __nv_tex_rmet_cast<T>::type) &temp, t, x);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex1D(texture<T, 0x01, cudaReadModeNormalizedFloat> t, float x)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex1D_rmnf_v2", &type_dummy, &retval, t, x);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex2D(texture<T, 0x02, cudaReadModeElementType> t, float x, float y)
{

  typename __nv_tex_rmet_ret<T>::type temp;

  __nv_tex_surf_handler("__tex2D_v2", (typename __nv_tex_rmet_cast<T>::type) &temp, t, x, y);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex2D(texture<T, 0x02, cudaReadModeNormalizedFloat> t, float x, float y)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex2D_rmnf_v2", &type_dummy, &retval, t, x, y);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex1DLayered(texture<T, 0xF1, cudaReadModeElementType> t, float x, int layer)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex1DLayered_v2", (typename __nv_tex_rmet_cast<T>::type) &temp, t, x, layer);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex1DLayered(texture<T, 0xF1, cudaReadModeNormalizedFloat> t, float x, int layer)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex1DLayered_rmnf_v2", &type_dummy, &retval, t, x, layer);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex2DLayered(texture<T, 0xF2, cudaReadModeElementType> t, float x, float y, int layer)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex2DLayered_v2", (typename __nv_tex_rmet_cast<T>::type) &temp, t, x, y, layer);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex2DLayered(texture<T, 0xF2, cudaReadModeNormalizedFloat> t, float x, float y, int layer)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex2DLayered_rmnf_v2", &type_dummy, &retval, t, x, y, layer);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex3D(texture<T, 0x03, cudaReadModeElementType> t, float x, float y, float z)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex3D_v2", (typename __nv_tex_rmet_cast<T>::type) &temp, t, x, y, z);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex3D(texture<T, 0x03, cudaReadModeNormalizedFloat> t, float x, float y, float z)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex3D_rmnf_v2", &type_dummy, &retval, t, x, y, z);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type texCubemap(texture<T, 0x0C, cudaReadModeElementType> t, float x, float y, float z)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__texCubemap_v2", (typename __nv_tex_rmet_cast<T>::type) &temp, t, x, y, z);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type texCubemap(texture<T, 0x0C, cudaReadModeNormalizedFloat> t, float x, float y, float z)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__texCubemap_rmnf_v2", &type_dummy, &retval, t, x, y, z);
  return retval;

}


template <typename T>
struct __nv_tex2dgather_ret { };
template <> struct __nv_tex2dgather_ret<char> { typedef char4 type; };
template <> struct __nv_tex2dgather_ret<signed char> { typedef char4 type; };
template <> struct __nv_tex2dgather_ret<char1> { typedef char4 type; };
template <> struct __nv_tex2dgather_ret<char2> { typedef char4 type; };
template <> struct __nv_tex2dgather_ret<char3> { typedef char4 type; };
template <> struct __nv_tex2dgather_ret<char4> { typedef char4 type; };
template <> struct __nv_tex2dgather_ret<unsigned char> { typedef uchar4 type; };
template <> struct __nv_tex2dgather_ret<uchar1> { typedef uchar4 type; };
template <> struct __nv_tex2dgather_ret<uchar2> { typedef uchar4 type; };
template <> struct __nv_tex2dgather_ret<uchar3> { typedef uchar4 type; };
template <> struct __nv_tex2dgather_ret<uchar4> { typedef uchar4 type; };

template <> struct __nv_tex2dgather_ret<short> { typedef short4 type; };
template <> struct __nv_tex2dgather_ret<short1> { typedef short4 type; };
template <> struct __nv_tex2dgather_ret<short2> { typedef short4 type; };
template <> struct __nv_tex2dgather_ret<short3> { typedef short4 type; };
template <> struct __nv_tex2dgather_ret<short4> { typedef short4 type; };
template <> struct __nv_tex2dgather_ret<unsigned short> { typedef ushort4 type; };
template <> struct __nv_tex2dgather_ret<ushort1> { typedef ushort4 type; };
template <> struct __nv_tex2dgather_ret<ushort2> { typedef ushort4 type; };
template <> struct __nv_tex2dgather_ret<ushort3> { typedef ushort4 type; };
template <> struct __nv_tex2dgather_ret<ushort4> { typedef ushort4 type; };

template <> struct __nv_tex2dgather_ret<int> { typedef int4 type; };
template <> struct __nv_tex2dgather_ret<int1> { typedef int4 type; };
template <> struct __nv_tex2dgather_ret<int2> { typedef int4 type; };
template <> struct __nv_tex2dgather_ret<int3> { typedef int4 type; };
template <> struct __nv_tex2dgather_ret<int4> { typedef int4 type; };
template <> struct __nv_tex2dgather_ret<unsigned int> { typedef uint4 type; };
template <> struct __nv_tex2dgather_ret<uint1> { typedef uint4 type; };
template <> struct __nv_tex2dgather_ret<uint2> { typedef uint4 type; };
template <> struct __nv_tex2dgather_ret<uint3> { typedef uint4 type; };
template <> struct __nv_tex2dgather_ret<uint4> { typedef uint4 type; };

template <> struct __nv_tex2dgather_ret<float> { typedef float4 type; };
template <> struct __nv_tex2dgather_ret<float1> { typedef float4 type; };
template <> struct __nv_tex2dgather_ret<float2> { typedef float4 type; };
template <> struct __nv_tex2dgather_ret<float3> { typedef float4 type; };
template <> struct __nv_tex2dgather_ret<float4> { typedef float4 type; };

template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) typename __nv_tex2dgather_ret<T>::type tex2Dgather(texture<T, 0x02, cudaReadModeElementType> t, float x, float y, int comp=0)
{

  T type_dummy;
  typename __nv_tex2dgather_ret<T>::type retval;
  __nv_tex_surf_handler("__tex2Dgather_v2", &type_dummy, &retval, t, x, y, comp);
  return retval;

}


template<typename T> struct __nv_tex2dgather_rmnf_ret { };
template<> struct __nv_tex2dgather_rmnf_ret<char> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<signed char> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<unsigned char> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<char1> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<uchar1> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<char2> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<uchar2> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<char3> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<uchar3> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<char4> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<uchar4> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<signed short> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<unsigned short> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<short1> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<ushort1> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<short2> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<ushort2> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<short3> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<ushort3> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<short4> { typedef float4 type; };
template<> struct __nv_tex2dgather_rmnf_ret<ushort4> { typedef float4 type; };

template <typename T>
static __attribute__((device)) __inline__ __attribute__((always_inline)) typename __nv_tex2dgather_rmnf_ret<T>::type tex2Dgather(texture<T, 0x02, cudaReadModeNormalizedFloat> t, float x, float y, int comp = 0)
{

  T type_dummy;
  typename __nv_tex2dgather_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex2Dgather_rmnf_v2", &type_dummy, &retval, t, x, y, comp);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex1DLod(texture<T, 0x01, cudaReadModeElementType> t, float x, float level)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex1DLod_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, level);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex1DLod(texture<T, 0x01, cudaReadModeNormalizedFloat> t, float x, float level)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex1DLod_rmnf_v2", &type_dummy, &retval, t, x, level);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex2DLod(texture<T, 0x02, cudaReadModeElementType> t, float x, float y, float level)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex2DLod_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, level);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex2DLod(texture<T, 0x02, cudaReadModeNormalizedFloat> t, float x, float y, float level)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex2DLod_rmnf_v2", &type_dummy, &retval, t, x, y, level);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex1DLayeredLod(texture<T, 0xF1, cudaReadModeElementType> t, float x, int layer, float level)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex1DLayeredLod_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, layer, level);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex1DLayeredLod(texture<T, 0xF1, cudaReadModeNormalizedFloat> t, float x, int layer, float level)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex1DLayeredLod_rmnf_v2", &type_dummy, &retval, t, x, layer, level);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex2DLayeredLod(texture<T, 0xF2, cudaReadModeElementType> t, float x, float y, int layer, float level)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex2DLayeredLod_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, layer, level);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex2DLayeredLod(texture<T, 0xF2, cudaReadModeNormalizedFloat> t, float x, float y, int layer, float level)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex2DLayeredLod_rmnf_v2", &type_dummy, &retval, t, x, y, layer, level);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex3DLod(texture<T, 0x03, cudaReadModeElementType> t, float x, float y, float z, float level)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex3DLod_v2",(typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, z, level);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex3DLod(texture<T, 0x03, cudaReadModeNormalizedFloat> t, float x, float y, float z, float level)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex3DLod_rmnf_v2", &type_dummy, &retval, t, x, y, z, level);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type texCubemapLod(texture<T, 0x0C, cudaReadModeElementType> t, float x, float y, float z, float level)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__texCubemapLod_v2",(typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, z, level);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type texCubemapLod(texture<T, 0x0C, cudaReadModeNormalizedFloat> t, float x, float y, float z, float level)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__texCubemapLod_rmnf_v2", &type_dummy, &retval, t, x, y, z, level);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type texCubemapLayered(texture<T, 0xFC, cudaReadModeElementType> t, float x, float y, float z, int layer)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__texCubemapLayered_v2",(typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, z, layer);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type texCubemapLayered(texture<T, 0xFC, cudaReadModeNormalizedFloat> t, float x, float y, float z, int layer)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__texCubemapLayered_rmnf_v2", &type_dummy, &retval, t, x, y, z, layer);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type texCubemapLayeredLod(texture<T, 0xFC, cudaReadModeElementType> t, float x, float y, float z, int layer, float level)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__texCubemapLayeredLod_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, z, layer, level);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type texCubemapLayeredLod(texture<T, 0xFC, cudaReadModeNormalizedFloat> t, float x, float y, float z, int layer, float level)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__texCubemapLayeredLod_rmnf_v2", &type_dummy, &retval, t, x, y, z, layer, level);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type texCubemapGrad(texture<T, 0x0C, cudaReadModeElementType> t, float x, float y, float z, float4 dPdx, float4 dPdy)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__texCubemapGrad_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, z, &dPdx, &dPdy);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type texCubemapGrad(texture<T, 0x0C, cudaReadModeNormalizedFloat> t, float x, float y, float z, float4 dPdx, float4 dPdy)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__texCubemapGrad_rmnf_v2", &type_dummy, &retval, t, x, y, z, &dPdx, &dPdy);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type texCubemapLayeredGrad(texture<T, 0xFC, cudaReadModeElementType> t, float x, float y, float z, int layer, float4 dPdx, float4 dPdy)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__texCubemapLayeredGrad_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, z, layer, &dPdx, &dPdy);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type texCubemapLayeredGrad(texture<T, 0xFC, cudaReadModeNormalizedFloat> t, float x, float y, float z, int layer, float4 dPdx, float4 dPdy)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__texCubemapLayeredGrad_rmnf_v2", &type_dummy, &retval,t, x, y, z, layer, &dPdx, &dPdy);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex1DGrad(texture<T, 0x01, cudaReadModeElementType> t, float x, float dPdx, float dPdy)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex1DGrad_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, dPdx, dPdy);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex1DGrad(texture<T, 0x01, cudaReadModeNormalizedFloat> t, float x, float dPdx, float dPdy)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex1DGrad_rmnf_v2", &type_dummy, &retval,t, x,dPdx, dPdy);
  return retval;

}



template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex2DGrad(texture<T, 0x02, cudaReadModeElementType> t, float x, float y, float2 dPdx, float2 dPdy)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex2DGrad_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, &dPdx, &dPdy);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex2DGrad(texture<T, 0x02, cudaReadModeNormalizedFloat> t, float x, float y, float2 dPdx, float2 dPdy)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex2DGrad_rmnf_v2", &type_dummy, &retval,t, x, y, &dPdx, &dPdy);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex1DLayeredGrad(texture<T, 0xF1, cudaReadModeElementType> t, float x, int layer, float dPdx, float dPdy)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex1DLayeredGrad_v2",(typename __nv_tex_rmet_cast<T>::type)&temp, t, x, layer, dPdx, dPdy);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex1DLayeredGrad(texture<T, 0xF1, cudaReadModeNormalizedFloat> t, float x, int layer, float dPdx, float dPdy)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex1DLayeredGrad_rmnf_v2", &type_dummy, &retval,t, x, layer, dPdx, dPdy);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex2DLayeredGrad(texture<T, 0xF2, cudaReadModeElementType> t, float x, float y, int layer, float2 dPdx, float2 dPdy)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex2DLayeredGrad_v2",(typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, layer, &dPdx, &dPdy);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex2DLayeredGrad(texture<T, 0xF2, cudaReadModeNormalizedFloat> t, float x, float y, int layer, float2 dPdx, float2 dPdy)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex2DLayeredGrad_rmnf_v2", &type_dummy, &retval,t, x, y, layer, &dPdx, &dPdy);
  return retval;

}


template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmet_ret<T>::type tex3DGrad(texture<T, 0x03, cudaReadModeElementType> t, float x, float y, float z, float4 dPdx, float4 dPdy)
{

  typename __nv_tex_rmet_ret<T>::type temp;
  __nv_tex_surf_handler("__tex3DGrad_v2", (typename __nv_tex_rmet_cast<T>::type)&temp, t, x, y, z, &dPdx, &dPdy);
  return temp;

}

template <typename T>
static __inline__ __attribute__((always_inline)) __attribute__((device)) typename __nv_tex_rmnf_ret<T>::type tex3DGrad(texture<T, 0x03, cudaReadModeNormalizedFloat> t, float x, float y, float z, float4 dPdx, float4 dPdy)
{

  T type_dummy;
  typename __nv_tex_rmnf_ret<T>::type retval;
  __nv_tex_surf_handler("__tex3DGrad_rmnf_v2", &type_dummy, &retval,t, x, y, z, &dPdx, &dPdy);
  return retval;

}
# 3292 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_indirect_functions.h" 1
# 60 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_indirect_functions.h"
template <typename T> struct __nv_itex_trait { };
template<> struct __nv_itex_trait<char> { typedef void type; };
template<> struct __nv_itex_trait<signed char> { typedef void type; };
template<> struct __nv_itex_trait<char1> { typedef void type; };
template<> struct __nv_itex_trait<char2> { typedef void type; };
template<> struct __nv_itex_trait<char4> { typedef void type; };
template<> struct __nv_itex_trait<unsigned char> { typedef void type; };
template<> struct __nv_itex_trait<uchar1> { typedef void type; };
template<> struct __nv_itex_trait<uchar2> { typedef void type; };
template<> struct __nv_itex_trait<uchar4> { typedef void type; };
template<> struct __nv_itex_trait<short> { typedef void type; };
template<> struct __nv_itex_trait<short1> { typedef void type; };
template<> struct __nv_itex_trait<short2> { typedef void type; };
template<> struct __nv_itex_trait<short4> { typedef void type; };
template<> struct __nv_itex_trait<unsigned short> { typedef void type; };
template<> struct __nv_itex_trait<ushort1> { typedef void type; };
template<> struct __nv_itex_trait<ushort2> { typedef void type; };
template<> struct __nv_itex_trait<ushort4> { typedef void type; };
template<> struct __nv_itex_trait<int> { typedef void type; };
template<> struct __nv_itex_trait<int1> { typedef void type; };
template<> struct __nv_itex_trait<int2> { typedef void type; };
template<> struct __nv_itex_trait<int4> { typedef void type; };
template<> struct __nv_itex_trait<unsigned int> { typedef void type; };
template<> struct __nv_itex_trait<uint1> { typedef void type; };
template<> struct __nv_itex_trait<uint2> { typedef void type; };
template<> struct __nv_itex_trait<uint4> { typedef void type; };
# 96 "/usr/local/cuda/bin/../targets/x86_64-linux/include/texture_indirect_functions.h"
template<> struct __nv_itex_trait<float> { typedef void type; };
template<> struct __nv_itex_trait<float1> { typedef void type; };
template<> struct __nv_itex_trait<float2> { typedef void type; };
template<> struct __nv_itex_trait<float4> { typedef void type; };



template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex1Dfetch(T *ptr, cudaTextureObject_t obj, int x)
{

   __nv_tex_surf_handler("__itex1Dfetch", ptr, obj, x);

}

template <class T>
static __attribute__((device)) T tex1Dfetch(cudaTextureObject_t texObject, int x)
{

  T ret;
  tex1Dfetch(&ret, texObject, x);
  return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex1D(T *ptr, cudaTextureObject_t obj, float x)
{

   __nv_tex_surf_handler("__itex1D", ptr, obj, x);

}


template <class T>
static __attribute__((device)) T tex1D(cudaTextureObject_t texObject, float x)
{

  T ret;
  tex1D(&ret, texObject, x);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex2D(T *ptr, cudaTextureObject_t obj, float x, float y)
{

   __nv_tex_surf_handler("__itex2D", ptr, obj, x, y);

}

template <class T>
static __attribute__((device)) T tex2D(cudaTextureObject_t texObject, float x, float y)
{

  T ret;
  tex2D(&ret, texObject, x, y);
  return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex3D(T *ptr, cudaTextureObject_t obj, float x, float y, float z)
{

   __nv_tex_surf_handler("__itex3D", ptr, obj, x, y, z);

}

template <class T>
static __attribute__((device)) T tex3D(cudaTextureObject_t texObject, float x, float y, float z)
{

  T ret;
  tex3D(&ret, texObject, x, y, z);
  return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex1DLayered(T *ptr, cudaTextureObject_t obj, float x, int layer)
{

   __nv_tex_surf_handler("__itex1DLayered", ptr, obj, x, layer);

}

template <class T>
static __attribute__((device)) T tex1DLayered(cudaTextureObject_t texObject, float x, int layer)
{

  T ret;
  tex1DLayered(&ret, texObject, x, layer);
  return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex2DLayered(T *ptr, cudaTextureObject_t obj, float x, float y, int layer)
{

  __nv_tex_surf_handler("__itex2DLayered", ptr, obj, x, y, layer);

}

template <class T>
static __attribute__((device)) T tex2DLayered(cudaTextureObject_t texObject, float x, float y, int layer)
{

  T ret;
  tex2DLayered(&ret, texObject, x, y, layer);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type texCubemap(T *ptr, cudaTextureObject_t obj, float x, float y, float z)
{

  __nv_tex_surf_handler("__itexCubemap", ptr, obj, x, y, z);

}


template <class T>
static __attribute__((device)) T texCubemap(cudaTextureObject_t texObject, float x, float y, float z)
{

  T ret;
  texCubemap(&ret, texObject, x, y, z);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type texCubemapLayered(T *ptr, cudaTextureObject_t obj, float x, float y, float z, int layer)
{

  __nv_tex_surf_handler("__itexCubemapLayered", ptr, obj, x, y, z, layer);

}

template <class T>
static __attribute__((device)) T texCubemapLayered(cudaTextureObject_t texObject, float x, float y, float z, int layer)
{

  T ret;
  texCubemapLayered(&ret, texObject, x, y, z, layer);
  return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex2Dgather(T *ptr, cudaTextureObject_t obj, float x, float y, int comp = 0)
{

  __nv_tex_surf_handler("__itex2Dgather", ptr, obj, x, y, comp);

}

template <class T>
static __attribute__((device)) T tex2Dgather(cudaTextureObject_t to, float x, float y, int comp = 0)
{

  T ret;
  tex2Dgather(&ret, to, x, y, comp);
  return ret;

}



template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex1DLod(T *ptr, cudaTextureObject_t obj, float x, float level)
{

  __nv_tex_surf_handler("__itex1DLod", ptr, obj, x, level);

}

template <class T>
static __attribute__((device)) T tex1DLod(cudaTextureObject_t texObject, float x, float level)
{

  T ret;
  tex1DLod(&ret, texObject, x, level);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex2DLod(T *ptr, cudaTextureObject_t obj, float x, float y, float level)
{

  __nv_tex_surf_handler("__itex2DLod", ptr, obj, x, y, level);

}

template <class T>
static __attribute__((device)) T tex2DLod(cudaTextureObject_t texObject, float x, float y, float level)
{

  T ret;
  tex2DLod(&ret, texObject, x, y, level);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex3DLod(T *ptr, cudaTextureObject_t obj, float x, float y, float z, float level)
{

  __nv_tex_surf_handler("__itex3DLod", ptr, obj, x, y, z, level);

}

template <class T>
static __attribute__((device)) T tex3DLod(cudaTextureObject_t texObject, float x, float y, float z, float level)
{

  T ret;
  tex3DLod(&ret, texObject, x, y, z, level);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex1DLayeredLod(T *ptr, cudaTextureObject_t obj, float x, int layer, float level)
{

  __nv_tex_surf_handler("__itex1DLayeredLod", ptr, obj, x, layer, level);

}

template <class T>
static __attribute__((device)) T tex1DLayeredLod(cudaTextureObject_t texObject, float x, int layer, float level)
{

  T ret;
  tex1DLayeredLod(&ret, texObject, x, layer, level);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex2DLayeredLod(T *ptr, cudaTextureObject_t obj, float x, float y, int layer, float level)
{

  __nv_tex_surf_handler("__itex2DLayeredLod", ptr, obj, x, y, layer, level);

}

template <class T>
static __attribute__((device)) T tex2DLayeredLod(cudaTextureObject_t texObject, float x, float y, int layer, float level)
{

  T ret;
  tex2DLayeredLod(&ret, texObject, x, y, layer, level);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type texCubemapLod(T *ptr, cudaTextureObject_t obj, float x, float y, float z, float level)
{

  __nv_tex_surf_handler("__itexCubemapLod", ptr, obj, x, y, z, level);

}

template <class T>
static __attribute__((device)) T texCubemapLod(cudaTextureObject_t texObject, float x, float y, float z, float level)
{

  T ret;
  texCubemapLod(&ret, texObject, x, y, z, level);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type texCubemapGrad(T *ptr, cudaTextureObject_t obj, float x, float y, float z, float4 dPdx, float4 dPdy)
{

  __nv_tex_surf_handler("__itexCubemapGrad_v2", ptr, obj, x, y, z, &dPdx, &dPdy);

}

template <class T>
static __attribute__((device)) T texCubemapGrad(cudaTextureObject_t texObject, float x, float y, float z, float4 dPdx, float4 dPdy)
{

  T ret;
  texCubemapGrad(&ret, texObject, x, y, z, dPdx, dPdy);
  return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type texCubemapLayeredLod(T *ptr, cudaTextureObject_t obj, float x, float y, float z, int layer, float level)
{

  __nv_tex_surf_handler("__itexCubemapLayeredLod", ptr, obj, x, y, z, layer, level);

}

template <class T>
static __attribute__((device)) T texCubemapLayeredLod(cudaTextureObject_t texObject, float x, float y, float z, int layer, float level)
{

  T ret;
  texCubemapLayeredLod(&ret, texObject, x, y, z, layer, level);
  return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex1DGrad(T *ptr, cudaTextureObject_t obj, float x, float dPdx, float dPdy)
{

  __nv_tex_surf_handler("__itex1DGrad", ptr, obj, x, dPdx, dPdy);

}

template <class T>
static __attribute__((device)) T tex1DGrad(cudaTextureObject_t texObject, float x, float dPdx, float dPdy)
{

  T ret;
  tex1DGrad(&ret, texObject, x, dPdx, dPdy);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex2DGrad(T *ptr, cudaTextureObject_t obj, float x, float y, float2 dPdx, float2 dPdy)
{

  __nv_tex_surf_handler("__itex2DGrad_v2", ptr, obj, x, y, &dPdx, &dPdy);


}

template <class T>
static __attribute__((device)) T tex2DGrad(cudaTextureObject_t texObject, float x, float y, float2 dPdx, float2 dPdy)
{

  T ret;
  tex2DGrad(&ret, texObject, x, y, dPdx, dPdy);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex3DGrad(T *ptr, cudaTextureObject_t obj, float x, float y, float z, float4 dPdx, float4 dPdy)
{

  __nv_tex_surf_handler("__itex3DGrad_v2", ptr, obj, x, y, z, &dPdx, &dPdy);

}

template <class T>
static __attribute__((device)) T tex3DGrad(cudaTextureObject_t texObject, float x, float y, float z, float4 dPdx, float4 dPdy)
{

  T ret;
  tex3DGrad(&ret, texObject, x, y, z, dPdx, dPdy);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex1DLayeredGrad(T *ptr, cudaTextureObject_t obj, float x, int layer, float dPdx, float dPdy)
{

  __nv_tex_surf_handler("__itex1DLayeredGrad", ptr, obj, x, layer, dPdx, dPdy);

}

template <class T>
static __attribute__((device)) T tex1DLayeredGrad(cudaTextureObject_t texObject, float x, int layer, float dPdx, float dPdy)
{

  T ret;
  tex1DLayeredGrad(&ret, texObject, x, layer, dPdx, dPdy);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type tex2DLayeredGrad(T * ptr, cudaTextureObject_t obj, float x, float y, int layer, float2 dPdx, float2 dPdy)
{

  __nv_tex_surf_handler("__itex2DLayeredGrad_v2", ptr, obj, x, y, layer, &dPdx, &dPdy);

}

template <class T>
static __attribute__((device)) T tex2DLayeredGrad(cudaTextureObject_t texObject, float x, float y, int layer, float2 dPdx, float2 dPdy)
{

  T ret;
  tex2DLayeredGrad(&ret, texObject, x, y, layer, dPdx, dPdy);
  return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_itex_trait<T>::type texCubemapLayeredGrad(T *ptr, cudaTextureObject_t obj, float x, float y, float z, int layer, float4 dPdx, float4 dPdy)
{

  __nv_tex_surf_handler("__itexCubemapLayeredGrad_v2", ptr, obj, x, y, z, layer, &dPdx, &dPdy);

}

template <class T>
static __attribute__((device)) T texCubemapLayeredGrad(cudaTextureObject_t texObject, float x, float y, float z, int layer, float4 dPdx, float4 dPdy)
{

  T ret;
  texCubemapLayeredGrad(&ret, texObject, x, y, z, layer, dPdx, dPdy);
  return ret;

}
# 3293 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/surface_indirect_functions.h" 1
# 59 "/usr/local/cuda/bin/../targets/x86_64-linux/include/surface_indirect_functions.h"
template<typename T> struct __nv_isurf_trait { };
template<> struct __nv_isurf_trait<char> { typedef void type; };
template<> struct __nv_isurf_trait<signed char> { typedef void type; };
template<> struct __nv_isurf_trait<char1> { typedef void type; };
template<> struct __nv_isurf_trait<unsigned char> { typedef void type; };
template<> struct __nv_isurf_trait<uchar1> { typedef void type; };
template<> struct __nv_isurf_trait<short> { typedef void type; };
template<> struct __nv_isurf_trait<short1> { typedef void type; };
template<> struct __nv_isurf_trait<unsigned short> { typedef void type; };
template<> struct __nv_isurf_trait<ushort1> { typedef void type; };
template<> struct __nv_isurf_trait<int> { typedef void type; };
template<> struct __nv_isurf_trait<int1> { typedef void type; };
template<> struct __nv_isurf_trait<unsigned int> { typedef void type; };
template<> struct __nv_isurf_trait<uint1> { typedef void type; };
template<> struct __nv_isurf_trait<long long> { typedef void type; };
template<> struct __nv_isurf_trait<longlong1> { typedef void type; };
template<> struct __nv_isurf_trait<unsigned long long> { typedef void type; };
template<> struct __nv_isurf_trait<ulonglong1> { typedef void type; };
template<> struct __nv_isurf_trait<float> { typedef void type; };
template<> struct __nv_isurf_trait<float1> { typedef void type; };

template<> struct __nv_isurf_trait<char2> { typedef void type; };
template<> struct __nv_isurf_trait<uchar2> { typedef void type; };
template<> struct __nv_isurf_trait<short2> { typedef void type; };
template<> struct __nv_isurf_trait<ushort2> { typedef void type; };
template<> struct __nv_isurf_trait<int2> { typedef void type; };
template<> struct __nv_isurf_trait<uint2> { typedef void type; };
template<> struct __nv_isurf_trait<longlong2> { typedef void type; };
template<> struct __nv_isurf_trait<ulonglong2> { typedef void type; };
template<> struct __nv_isurf_trait<float2> { typedef void type; };

template<> struct __nv_isurf_trait<char4> { typedef void type; };
template<> struct __nv_isurf_trait<uchar4> { typedef void type; };
template<> struct __nv_isurf_trait<short4> { typedef void type; };
template<> struct __nv_isurf_trait<ushort4> { typedef void type; };
template<> struct __nv_isurf_trait<int4> { typedef void type; };
template<> struct __nv_isurf_trait<uint4> { typedef void type; };
template<> struct __nv_isurf_trait<float4> { typedef void type; };


template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf1Dread(T *ptr, cudaSurfaceObject_t obj, int x, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf1Dread", ptr, obj, x, mode);

}

template <class T>
static __attribute__((device)) T surf1Dread(cudaSurfaceObject_t surfObject, int x, cudaSurfaceBoundaryMode boundaryMode = cudaBoundaryModeTrap)
{

   T ret;
   surf1Dread(&ret, surfObject, x, boundaryMode);
   return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf2Dread(T *ptr, cudaSurfaceObject_t obj, int x, int y, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf2Dread", ptr, obj, x, y, mode);

}

template <class T>
static __attribute__((device)) T surf2Dread(cudaSurfaceObject_t surfObject, int x, int y, cudaSurfaceBoundaryMode boundaryMode = cudaBoundaryModeTrap)
{

   T ret;
   surf2Dread(&ret, surfObject, x, y, boundaryMode);
   return ret;

}


template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf3Dread(T *ptr, cudaSurfaceObject_t obj, int x, int y, int z, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf3Dread", ptr, obj, x, y, z, mode);

}

template <class T>
static __attribute__((device)) T surf3Dread(cudaSurfaceObject_t surfObject, int x, int y, int z, cudaSurfaceBoundaryMode boundaryMode = cudaBoundaryModeTrap)
{

   T ret;
   surf3Dread(&ret, surfObject, x, y, z, boundaryMode);
   return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf1DLayeredread(T *ptr, cudaSurfaceObject_t obj, int x, int layer, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf1DLayeredread", ptr, obj, x, layer, mode);

}

template <class T>
static __attribute__((device)) T surf1DLayeredread(cudaSurfaceObject_t surfObject, int x, int layer, cudaSurfaceBoundaryMode boundaryMode = cudaBoundaryModeTrap)
{

   T ret;
   surf1DLayeredread(&ret, surfObject, x, layer, boundaryMode);
   return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf2DLayeredread(T *ptr, cudaSurfaceObject_t obj, int x, int y, int layer, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf2DLayeredread", ptr, obj, x, y, layer, mode);

}

template <class T>
static __attribute__((device)) T surf2DLayeredread(cudaSurfaceObject_t surfObject, int x, int y, int layer, cudaSurfaceBoundaryMode boundaryMode = cudaBoundaryModeTrap)
{

   T ret;
   surf2DLayeredread(&ret, surfObject, x, y, layer, boundaryMode);
   return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surfCubemapread(T *ptr, cudaSurfaceObject_t obj, int x, int y, int face, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurfCubemapread", ptr, obj, x, y, face, mode);

}

template <class T>
static __attribute__((device)) T surfCubemapread(cudaSurfaceObject_t surfObject, int x, int y, int face, cudaSurfaceBoundaryMode boundaryMode = cudaBoundaryModeTrap)
{

   T ret;
   surfCubemapread(&ret, surfObject, x, y, face, boundaryMode);
   return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surfCubemapLayeredread(T *ptr, cudaSurfaceObject_t obj, int x, int y, int layerface, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurfCubemapLayeredread", ptr, obj, x, y, layerface, mode);

}

template <class T>
static __attribute__((device)) T surfCubemapLayeredread(cudaSurfaceObject_t surfObject, int x, int y, int layerface, cudaSurfaceBoundaryMode boundaryMode = cudaBoundaryModeTrap)
{

   T ret;
   surfCubemapLayeredread(&ret, surfObject, x, y, layerface, boundaryMode);
   return ret;

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf1Dwrite(T val, cudaSurfaceObject_t obj, int x, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf1Dwrite_v2", &val, obj, x, mode);

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf2Dwrite(T val, cudaSurfaceObject_t obj, int x, int y, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf2Dwrite_v2", &val, obj, x, y, mode);

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf3Dwrite(T val, cudaSurfaceObject_t obj, int x, int y, int z, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf3Dwrite_v2", &val, obj, x, y, z, mode);

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf1DLayeredwrite(T val, cudaSurfaceObject_t obj, int x, int layer, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf1DLayeredwrite_v2", &val, obj, x, layer, mode);

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surf2DLayeredwrite(T val, cudaSurfaceObject_t obj, int x, int y, int layer, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurf2DLayeredwrite_v2", &val, obj, x, y, layer, mode);

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surfCubemapwrite(T val, cudaSurfaceObject_t obj, int x, int y, int face, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurfCubemapwrite_v2", &val, obj, x, y, face, mode);

}

template <typename T>
static __attribute__((device)) typename __nv_isurf_trait<T>::type surfCubemapLayeredwrite(T val, cudaSurfaceObject_t obj, int x, int y, int layerface, cudaSurfaceBoundaryMode mode = cudaBoundaryModeTrap)
{

  __nv_tex_surf_handler("__isurfCubemapLayeredwrite_v2", &val, obj, x, y, layerface, mode);

}
# 3294 "/usr/local/cuda/bin/../targets/x86_64-linux/include/crt/device_functions.h" 2


extern "C" __attribute__((host)) __attribute__((device)) unsigned __cudaPushCallConfiguration(dim3 gridDim,
                                      dim3 blockDim,
                                      size_t sharedMem = 0,
                                      struct CUstream_st *stream = 0);
# 119 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2
# 1 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_launch_parameters.h" 1
# 68 "/usr/local/cuda/bin/../targets/x86_64-linux/include/device_launch_parameters.h"
extern "C" {


uint3 __attribute__((device_builtin)) extern const threadIdx;
uint3 __attribute__((device_builtin)) extern const blockIdx;
dim3 __attribute__((device_builtin)) extern const blockDim;
dim3 __attribute__((device_builtin)) extern const gridDim;
int __attribute__((device_builtin)) extern const warpSize;




}
# 120 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h" 2
# 199 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaLaunchKernel(
  const T *func,
  dim3 gridDim,
  dim3 blockDim,
  void **args,
  size_t sharedMem = 0,
  cudaStream_t stream = 0
)
{
    return ::cudaLaunchKernel((const void *)func, gridDim, blockDim, args, sharedMem, stream);
}
# 261 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaLaunchCooperativeKernel(
  const T *func,
  dim3 gridDim,
  dim3 blockDim,
  void **args,
  size_t sharedMem = 0,
  cudaStream_t stream = 0
)
{
    return ::cudaLaunchCooperativeKernel((const void *)func, gridDim, blockDim, args, sharedMem, stream);
}
# 305 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
static __inline__ __attribute__((host)) cudaError_t cudaEventCreate(
  cudaEvent_t *event,
  unsigned int flags
)
{
  return ::cudaEventCreateWithFlags(event, flags);
}
# 370 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
static __inline__ __attribute__((host)) cudaError_t cudaMallocHost(
  void **ptr,
  size_t size,
  unsigned int flags
)
{
  return ::cudaHostAlloc(ptr, size, flags);
}

template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaHostAlloc(
  T **ptr,
  size_t size,
  unsigned int flags
)
{
  return ::cudaHostAlloc((void**)(void*)ptr, size, flags);
}

template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaHostGetDevicePointer(
  T **pDevice,
  void *pHost,
  unsigned int flags
)
{
  return ::cudaHostGetDevicePointer((void**)(void*)pDevice, pHost, flags);
}
# 499 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaMallocManaged(
  T **devPtr,
  size_t size,
  unsigned int flags = 0x01
)
{
  return ::cudaMallocManaged((void**)(void*)devPtr, size, flags);
}
# 589 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaStreamAttachMemAsync(
  cudaStream_t stream,
  T *devPtr,
  size_t length = 0,
  unsigned int flags = 0x04
)
{
  return ::cudaStreamAttachMemAsync(stream, (void*)devPtr, length, flags);
}

template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaMalloc(
  T **devPtr,
  size_t size
)
{
  return ::cudaMalloc((void**)(void*)devPtr, size);
}

template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaMallocHost(
  T **ptr,
  size_t size,
  unsigned int flags = 0
)
{
  return cudaMallocHost((void**)(void*)ptr, size, flags);
}

template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaMallocPitch(
  T **devPtr,
  size_t *pitch,
  size_t width,
  size_t height
)
{
  return ::cudaMallocPitch((void**)(void*)devPtr, pitch, width, height);
}
# 667 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaMemcpyToSymbol(
  const T &symbol,
  const void *src,
        size_t count,
        size_t offset = 0,
        enum cudaMemcpyKind kind = cudaMemcpyHostToDevice
)
{
  return ::cudaMemcpyToSymbol((const void*)&symbol, src, count, offset, kind);
}
# 721 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaMemcpyToSymbolAsync(
  const T &symbol,
  const void *src,
        size_t count,
        size_t offset = 0,
        enum cudaMemcpyKind kind = cudaMemcpyHostToDevice,
        cudaStream_t stream = 0
)
{
  return ::cudaMemcpyToSymbolAsync((const void*)&symbol, src, count, offset, kind, stream);
}
# 769 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaMemcpyFromSymbol(
        void *dst,
  const T &symbol,
        size_t count,
        size_t offset = 0,
        enum cudaMemcpyKind kind = cudaMemcpyDeviceToHost
)
{
  return ::cudaMemcpyFromSymbol(dst, (const void*)&symbol, count, offset, kind);
}
# 823 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaMemcpyFromSymbolAsync(
        void *dst,
  const T &symbol,
        size_t count,
        size_t offset = 0,
        enum cudaMemcpyKind kind = cudaMemcpyDeviceToHost,
        cudaStream_t stream = 0
)
{
  return ::cudaMemcpyFromSymbolAsync(dst, (const void*)&symbol, count, offset, kind, stream);
}
# 859 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaGetSymbolAddress(
        void **devPtr,
  const T &symbol
)
{
  return ::cudaGetSymbolAddress(devPtr, (const void*)&symbol);
}
# 891 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaGetSymbolSize(
        size_t *size,
  const T &symbol
)
{
  return ::cudaGetSymbolSize(size, (const void*)&symbol);
}
# 935 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaBindTexture(
        size_t *offset,
  const struct texture<T, dim, readMode> &tex,
  const void *devPtr,
  const struct cudaChannelFormatDesc &desc,
        size_t size = (2147483647 * 2U + 1U)
)
{
  return ::cudaBindTexture(offset, &tex, devPtr, &desc, size);
}
# 981 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaBindTexture(
        size_t *offset,
  const struct texture<T, dim, readMode> &tex,
  const void *devPtr,
        size_t size = (2147483647 * 2U + 1U)
)
{
  return cudaBindTexture(offset, tex, devPtr, tex.channelDesc, size);
}
# 1038 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaBindTexture2D(
        size_t *offset,
  const struct texture<T, dim, readMode> &tex,
  const void *devPtr,
  const struct cudaChannelFormatDesc &desc,
  size_t width,
  size_t height,
  size_t pitch
)
{
  return ::cudaBindTexture2D(offset, &tex, devPtr, &desc, width, height, pitch);
}
# 1097 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaBindTexture2D(
        size_t *offset,
  const struct texture<T, dim, readMode> &tex,
  const void *devPtr,
  size_t width,
  size_t height,
  size_t pitch
)
{
  return ::cudaBindTexture2D(offset, &tex, devPtr, &tex.channelDesc, width, height, pitch);
}
# 1140 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaBindTextureToArray(
  const struct texture<T, dim, readMode> &tex,
  cudaArray_const_t array,
  const struct cudaChannelFormatDesc &desc
)
{
  return ::cudaBindTextureToArray(&tex, array, &desc);
}
# 1179 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaBindTextureToArray(
  const struct texture<T, dim, readMode> &tex,
  cudaArray_const_t array
)
{
  struct cudaChannelFormatDesc desc;
  cudaError_t err = ::cudaGetChannelDesc(&desc, array);

  return err == cudaSuccess ? cudaBindTextureToArray(tex, array, desc) : err;
}
# 1221 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaBindTextureToMipmappedArray(
  const struct texture<T, dim, readMode> &tex,
  cudaMipmappedArray_const_t mipmappedArray,
  const struct cudaChannelFormatDesc &desc
)
{
  return ::cudaBindTextureToMipmappedArray(&tex, mipmappedArray, &desc);
}
# 1260 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaBindTextureToMipmappedArray(
  const struct texture<T, dim, readMode> &tex,
  cudaMipmappedArray_const_t mipmappedArray
)
{
  struct cudaChannelFormatDesc desc;
  cudaArray_t levelArray;
  cudaError_t err = ::cudaGetMipmappedArrayLevel(&levelArray, mipmappedArray, 0);

  if (err != cudaSuccess) {
      return err;
  }
  err = ::cudaGetChannelDesc(&desc, levelArray);

  return err == cudaSuccess ? cudaBindTextureToMipmappedArray(tex, mipmappedArray, desc) : err;
}
# 1303 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaUnbindTexture(
  const struct texture<T, dim, readMode> &tex
)
{
  return ::cudaUnbindTexture(&tex);
}
# 1339 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim, enum cudaTextureReadMode readMode>
static __inline__ __attribute__((host)) cudaError_t cudaGetTextureAlignmentOffset(
        size_t *offset,
  const struct texture<T, dim, readMode> &tex
)
{
  return ::cudaGetTextureAlignmentOffset(offset, &tex);
}
# 1391 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaFuncSetCacheConfig(
  T *func,
  enum cudaFuncCache cacheConfig
)
{
  return ::cudaFuncSetCacheConfig((const void*)func, cacheConfig);
}

template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaFuncSetSharedMemConfig(
  T *func,
  enum cudaSharedMemConfig config
)
{
  return ::cudaFuncSetSharedMemConfig((const void*)func, config);
}
# 1436 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaOccupancyMaxActiveBlocksPerMultiprocessor(
    int *numBlocks,
    T func,
    int blockSize,
    size_t dynamicSMemSize)
{
    return ::cudaOccupancyMaxActiveBlocksPerMultiprocessorWithFlags(numBlocks, (const void*)func, blockSize, dynamicSMemSize, 0x00);
}
# 1487 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaOccupancyMaxActiveBlocksPerMultiprocessorWithFlags(
    int *numBlocks,
    T func,
    int blockSize,
    size_t dynamicSMemSize,
    unsigned int flags)
{
    return ::cudaOccupancyMaxActiveBlocksPerMultiprocessorWithFlags(numBlocks, (const void*)func, blockSize, dynamicSMemSize, flags);
}




class __cudaOccupancyB2DHelper {
  size_t n;
public:
  inline __attribute__((host)) __cudaOccupancyB2DHelper(size_t n_) : n(n_) {}
  inline __attribute__((host)) size_t operator()(int)
  {
      return n;
  }
};
# 1556 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<typename UnaryFunction, class T>
static __inline__ __attribute__((host)) cudaError_t cudaOccupancyMaxPotentialBlockSizeVariableSMemWithFlags(
    int *minGridSize,
    int *blockSize,
    T func,
    UnaryFunction blockSizeToDynamicSMemSize,
    int blockSizeLimit = 0,
    unsigned int flags = 0)
{
    cudaError_t status;


    int device;
    struct cudaFuncAttributes attr;


    int maxThreadsPerMultiProcessor;
    int warpSize;
    int devMaxThreadsPerBlock;
    int multiProcessorCount;
    int funcMaxThreadsPerBlock;
    int occupancyLimit;
    int granularity;


    int maxBlockSize = 0;
    int numBlocks = 0;
    int maxOccupancy = 0;


    int blockSizeToTryAligned;
    int blockSizeToTry;
    int blockSizeLimitAligned;
    int occupancyInBlocks;
    int occupancyInThreads;
    size_t dynamicSMemSize;





    if (!minGridSize || !blockSize || !func) {
        return cudaErrorInvalidValue;
    }





    status = ::cudaGetDevice(&device);
    if (status != cudaSuccess) {
        return status;
    }

    status = cudaDeviceGetAttribute(
        &maxThreadsPerMultiProcessor,
        cudaDevAttrMaxThreadsPerMultiProcessor,
        device);
    if (status != cudaSuccess) {
        return status;
    }

    status = cudaDeviceGetAttribute(
        &warpSize,
        cudaDevAttrWarpSize,
        device);
    if (status != cudaSuccess) {
        return status;
    }

    status = cudaDeviceGetAttribute(
        &devMaxThreadsPerBlock,
        cudaDevAttrMaxThreadsPerBlock,
        device);
    if (status != cudaSuccess) {
        return status;
    }

    status = cudaDeviceGetAttribute(
        &multiProcessorCount,
        cudaDevAttrMultiProcessorCount,
        device);
    if (status != cudaSuccess) {
        return status;
    }

    status = cudaFuncGetAttributes(&attr, func);
    if (status != cudaSuccess) {
        return status;
    }

    funcMaxThreadsPerBlock = attr.maxThreadsPerBlock;





    occupancyLimit = maxThreadsPerMultiProcessor;
    granularity = warpSize;

    if (blockSizeLimit == 0) {
        blockSizeLimit = devMaxThreadsPerBlock;
    }

    if (devMaxThreadsPerBlock < blockSizeLimit) {
        blockSizeLimit = devMaxThreadsPerBlock;
    }

    if (funcMaxThreadsPerBlock < blockSizeLimit) {
        blockSizeLimit = funcMaxThreadsPerBlock;
    }

    blockSizeLimitAligned = ((blockSizeLimit + (granularity - 1)) / granularity) * granularity;

    for (blockSizeToTryAligned = blockSizeLimitAligned; blockSizeToTryAligned > 0; blockSizeToTryAligned -= granularity) {



        if (blockSizeLimit < blockSizeToTryAligned) {
            blockSizeToTry = blockSizeLimit;
        } else {
            blockSizeToTry = blockSizeToTryAligned;
        }

        dynamicSMemSize = blockSizeToDynamicSMemSize(blockSizeToTry);

        status = cudaOccupancyMaxActiveBlocksPerMultiprocessorWithFlags(
            &occupancyInBlocks,
            func,
            blockSizeToTry,
            dynamicSMemSize,
            flags);

        if (status != cudaSuccess) {
            return status;
        }

        occupancyInThreads = blockSizeToTry * occupancyInBlocks;

        if (occupancyInThreads > maxOccupancy) {
            maxBlockSize = blockSizeToTry;
            numBlocks = occupancyInBlocks;
            maxOccupancy = occupancyInThreads;
        }



        if (occupancyLimit == maxOccupancy) {
            break;
        }
    }







    *minGridSize = numBlocks * multiProcessorCount;
    *blockSize = maxBlockSize;

    return status;
}
# 1751 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<typename UnaryFunction, class T>
static __inline__ __attribute__((host)) cudaError_t cudaOccupancyMaxPotentialBlockSizeVariableSMem(
    int *minGridSize,
    int *blockSize,
    T func,
    UnaryFunction blockSizeToDynamicSMemSize,
    int blockSizeLimit = 0)
{
    return cudaOccupancyMaxPotentialBlockSizeVariableSMemWithFlags(minGridSize, blockSize, func, blockSizeToDynamicSMemSize, blockSizeLimit, 0x00);
}
# 1796 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaOccupancyMaxPotentialBlockSize(
    int *minGridSize,
    int *blockSize,
    T func,
    size_t dynamicSMemSize = 0,
    int blockSizeLimit = 0)
{
  return cudaOccupancyMaxPotentialBlockSizeVariableSMemWithFlags(minGridSize, blockSize, func, __cudaOccupancyB2DHelper(dynamicSMemSize), blockSizeLimit, 0x00);
}
# 1855 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaOccupancyMaxPotentialBlockSizeWithFlags(
    int *minGridSize,
    int *blockSize,
    T func,
    size_t dynamicSMemSize = 0,
    int blockSizeLimit = 0,
    unsigned int flags = 0)
{
    return cudaOccupancyMaxPotentialBlockSizeVariableSMemWithFlags(minGridSize, blockSize, func, __cudaOccupancyB2DHelper(dynamicSMemSize), blockSizeLimit, flags);
}
# 1896 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaFuncGetAttributes(
  struct cudaFuncAttributes *attr,
  T *entry
)
{
  return ::cudaFuncGetAttributes(attr, (const void*)entry);
}
# 1941 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T>
static __inline__ __attribute__((host)) cudaError_t cudaFuncSetAttribute(
  T *entry,
  enum cudaFuncAttribute attr,
  int value
)
{
  return ::cudaFuncSetAttribute((const void*)entry, attr, value);
}
# 1973 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim>
static __attribute__((deprecated)) __inline__ __attribute__((host)) cudaError_t cudaBindSurfaceToArray(
  const struct surface<T, dim> &surf,
  cudaArray_const_t array,
  const struct cudaChannelFormatDesc &desc
)
{
  return ::cudaBindSurfaceToArray(&surf, array, &desc);
}
# 2004 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
template<class T, int dim>
static __attribute__((deprecated)) __inline__ __attribute__((host)) cudaError_t cudaBindSurfaceToArray(
  const struct surface<T, dim> &surf,
  cudaArray_const_t array
)
{
  struct cudaChannelFormatDesc desc;
  cudaError_t err = ::cudaGetChannelDesc(&desc, array);

  return err == cudaSuccess ? cudaBindSurfaceToArray(surf, array, desc) : err;
}
# 2025 "/usr/local/cuda/bin/../targets/x86_64-linux/include/cuda_runtime.h"
#pragma GCC diagnostic pop
# 1 "<command-line>" 2
# 1 "CMakeCUDACompilerId.cu"
# 64 "CMakeCUDACompilerId.cu"
char const* info_compiler = "INFO" ":" "compiler[" "NVIDIA" "]";

char const* info_simulate = "INFO" ":" "simulate[" "GNU" "]";
# 369 "CMakeCUDACompilerId.cu"
char const info_version[] = {
  'I', 'N', 'F', 'O', ':',
  'c','o','m','p','i','l','e','r','_','v','e','r','s','i','o','n','[',
  ('0' + (((10) / 10000000)%10)), ('0' + (((10) / 1000000)%10)), ('0' + (((10) / 100000)%10)), ('0' + (((10) / 10000)%10)), ('0' + (((10) / 1000)%10)), ('0' + (((10) / 100)%10)), ('0' + (((10) / 10)%10)), ('0' + ((10) % 10)),

  '.', ('0' + (((2) / 10000000)%10)), ('0' + (((2) / 1000000)%10)), ('0' + (((2) / 100000)%10)), ('0' + (((2) / 10000)%10)), ('0' + (((2) / 1000)%10)), ('0' + (((2) / 100)%10)), ('0' + (((2) / 10)%10)), ('0' + ((2) % 10)),

   '.', ('0' + (((89) / 10000000)%10)), ('0' + (((89) / 1000000)%10)), ('0' + (((89) / 100000)%10)), ('0' + (((89) / 10000)%10)), ('0' + (((89) / 1000)%10)), ('0' + (((89) / 100)%10)), ('0' + (((89) / 10)%10)), ('0' + ((89) % 10)),





  ']','\0'};
# 398 "CMakeCUDACompilerId.cu"
char const info_simulate_version[] = {
  'I', 'N', 'F', 'O', ':',
  's','i','m','u','l','a','t','e','_','v','e','r','s','i','o','n','[',
  ('0' + (((4) / 10000000)%10)), ('0' + (((4) / 1000000)%10)), ('0' + (((4) / 100000)%10)), ('0' + (((4) / 10000)%10)), ('0' + (((4) / 1000)%10)), ('0' + (((4) / 100)%10)), ('0' + (((4) / 10)%10)), ('0' + ((4) % 10)),

  '.', ('0' + (((8) / 10000000)%10)), ('0' + (((8) / 1000000)%10)), ('0' + (((8) / 100000)%10)), ('0' + (((8) / 10000)%10)), ('0' + (((8) / 1000)%10)), ('0' + (((8) / 100)%10)), ('0' + (((8) / 10)%10)), ('0' + ((8) % 10)),







  ']','\0'};






char const* info_platform = "INFO" ":" "platform[" "Linux" "]";
char const* info_arch = "INFO" ":" "arch[" "]";



const char* info_language_standard_default = "INFO" ":" "standard_default["
# 435 "CMakeCUDACompilerId.cu"
  "03"

"]";

const char* info_language_extensions_default = "INFO" ":" "extensions_default["


  "ON"



"]";



int main(int argc, char* argv[])
{
  int require = 0;
  require += info_compiler[argc];
  require += info_platform[argc];

  require += info_version[argc];


  require += info_simulate[argc];


  require += info_simulate_version[argc];

  require += info_language_standard_default[argc];
  require += info_language_extensions_default[argc];
  (void)argv;
  return require;
}
