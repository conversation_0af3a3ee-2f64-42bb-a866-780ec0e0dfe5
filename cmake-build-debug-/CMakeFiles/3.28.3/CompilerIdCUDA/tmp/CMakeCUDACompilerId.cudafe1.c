# 1 "CMakeCUDACompilerId.cu"
# 64 "CMakeCUDACompilerId.cu"
extern const char *info_compiler;

extern const char *info_simulate;
# 369 "CMakeCUDACompilerId.cu"
static const char info_version[50];
# 398 "CMakeCUDACompilerId.cu"
static const char info_simulate_version[41];
# 418 "CMakeCUDACompilerId.cu"
extern const char *info_platform;
extern const char *info_arch;



extern const char *info_language_standard_default;
# 439 "CMakeCUDACompilerId.cu"
extern const char *info_language_extensions_default;
# 64 "CMakeCUDACompilerId.cu"
const char *info_compiler = ((const char *)"INFO:compiler[NVIDIA]");

const char *info_simulate = ((const char *)"INFO:simulate[GNU]");
# 369 "CMakeCUDACompilerId.cu"
static const char info_version[50] = {((char)73),((char)78),((char)70),((char)79),((char)58),((char)99),((char)111),((char)109),((char)112),((char)105),((char)108),((char)101),((char)114),((char)95),((char)118),((char)101),((char)114),((char)115),((char)105),((char)111),((char)110),((char)91),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)49),((char)48),((char)46),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)50),((char)46),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)56),((char)57),((char)93),((char)0)};
# 398 "CMakeCUDACompilerId.cu"
static const char info_simulate_version[41] = {((char)73),((char)78),((char)70),((char)79),((char)58),((char)115),((char)105),((char)109),((char)117),((char)108),((char)97),((char)116),((char)101),((char)95),((char)118),((char)101),((char)114),((char)115),((char)105),((char)111),((char)110),((char)91),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)52),((char)46),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)48),((char)56),((char)93),((char)0)};
# 418 "CMakeCUDACompilerId.cu"
const char *info_platform = ((const char *)"INFO:platform[Linux]");
const char *info_arch = ((const char *)"INFO:arch[]");



const char *info_language_standard_default = ((const char *)"INFO:standard_default[03]");
# 439 "CMakeCUDACompilerId.cu"
const char *info_language_extensions_default = ((const char *)"INFO:extensions_default[ON]");
