# This is the CMakeCache file.
# For build in directory: /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-
# It was generated by CMake: /opt/cmake/cmake/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//Enable colored diagnostics throughout.
CMAKE_COLOR_DIAGNOSTICS:BOOL=ON

//CUDA architectures
CMAKE_CUDA_ARCHITECTURES:STRING=30

//No help, variable specified on the command line.
CMAKE_CUDA_COMPILER:PATH=/usr/local/cuda/bin/nvcc

//Flags used by the CUDA compiler during all build types.
CMAKE_CUDA_FLAGS:STRING=

//Flags used by the CUDA compiler during DEBUG builds.
CMAKE_CUDA_FLAGS_DEBUG:STRING=-g

//Flags used by the CUDA compiler during MINSIZEREL builds.
CMAKE_CUDA_FLAGS_MINSIZEREL:STRING=-O1 -DNDEBUG

//Flags used by the CUDA compiler during RELEASE builds.
CMAKE_CUDA_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CUDA compiler during RELWITHDEBINFO builds.
CMAKE_CUDA_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=CUDAProjects

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Value Computed by CMake
CUDAProjects_BINARY_DIR:STATIC=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-

//Value Computed by CMake
CUDAProjects_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
CUDAProjects_SOURCE_DIR:STATIC=/data/data/qjs/CLionProjects/CUDAProjects

//Path to a file.
CUDAToolkit_CUPTI_INCLUDE_DIR:PATH=/usr/local/cuda/extras/CUPTI/include

//Path to a library.
CUDAToolkit_rt_LIBRARY:FILEPATH=/usr/lib64/librt.so

//Path to a library.
CUDA_CUDART:FILEPATH=/usr/local/cuda/lib64/libcudart.so

//Path to a library.
CUDA_OpenCL_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libOpenCL.so

//Path to a library.
CUDA_cublasLt_LIBRARY:FILEPATH=/usr/lib64/libcublasLt.so

//Path to a library.
CUDA_cublasLt_static_LIBRARY:FILEPATH=/usr/lib64/libcublasLt_static.a

//Path to a library.
CUDA_cublas_LIBRARY:FILEPATH=/usr/lib64/libcublas.so

//Path to a library.
CUDA_cublas_static_LIBRARY:FILEPATH=/usr/lib64/libcublas_static.a

//Path to a library.
CUDA_cuda_driver_LIBRARY:FILEPATH=/usr/local/cuda/targets/x86_64-linux/lib/stubs/libcuda.so

//Path to a library.
CUDA_cudart_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcudart.so

//Path to a library.
CUDA_cudart_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcudart_static.a

//Path to a library.
CUDA_cufft_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcufft.so

//Path to a library.
CUDA_cufft_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcufft_static.a

//Path to a library.
CUDA_cufft_static_nocallback_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcufft_static_nocallback.a

//Path to a library.
CUDA_cufftw_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcufftw.so

//Path to a library.
CUDA_cufftw_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcufftw_static.a

//Path to a library.
CUDA_culibos_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libculibos.a

//Path to a library.
CUDA_cupti_LIBRARY:FILEPATH=/usr/local/cuda/extras/CUPTI/lib64/libcupti.so

//Path to a library.
CUDA_cupti_static_LIBRARY:FILEPATH=/usr/local/cuda/extras/CUPTI/lib64/libcupti_static.a

//Path to a library.
CUDA_curand_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcurand.so

//Path to a library.
CUDA_curand_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcurand_static.a

//Path to a library.
CUDA_cusolver_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcusolver.so

//Path to a library.
CUDA_cusolver_lapack_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/liblapack_static.a

//Path to a library.
CUDA_cusolver_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcusolver_static.a

//Path to a library.
CUDA_cusparse_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcusparse.so

//Path to a library.
CUDA_cusparse_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libcusparse_static.a

//Path to a library.
CUDA_nppc_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppc.so

//Path to a library.
CUDA_nppc_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppc_static.a

//Path to a library.
CUDA_nppial_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppial.so

//Path to a library.
CUDA_nppial_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppial_static.a

//Path to a library.
CUDA_nppicc_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppicc.so

//Path to a library.
CUDA_nppicc_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppicc_static.a

//Path to a library.
CUDA_nppicom_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppicom.so

//Path to a library.
CUDA_nppicom_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppicom_static.a

//Path to a library.
CUDA_nppidei_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppidei.so

//Path to a library.
CUDA_nppidei_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppidei_static.a

//Path to a library.
CUDA_nppif_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppif.so

//Path to a library.
CUDA_nppif_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppif_static.a

//Path to a library.
CUDA_nppig_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppig.so

//Path to a library.
CUDA_nppig_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppig_static.a

//Path to a library.
CUDA_nppim_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppim.so

//Path to a library.
CUDA_nppim_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppim_static.a

//Path to a library.
CUDA_nppist_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppist.so

//Path to a library.
CUDA_nppist_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppist_static.a

//Path to a library.
CUDA_nppisu_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppisu.so

//Path to a library.
CUDA_nppisu_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppisu_static.a

//Path to a library.
CUDA_nppitc_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppitc.so

//Path to a library.
CUDA_nppitc_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnppitc_static.a

//Path to a library.
CUDA_npps_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnpps.so

//Path to a library.
CUDA_npps_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnpps_static.a

//Path to a library.
CUDA_nvToolsExt_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnvToolsExt.so

//Path to a library.
CUDA_nvgraph_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnvgraph.so

//Path to a library.
CUDA_nvgraph_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnvgraph_static.a

//Path to a library.
CUDA_nvjpeg_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnvjpeg.so

//Path to a library.
CUDA_nvjpeg_static_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnvjpeg_static.a

//Path to a library.
CUDA_nvml_LIBRARY:FILEPATH=/usr/local/cuda/targets/x86_64-linux/lib/stubs/libnvidia-ml.so

//Path to a library.
CUDA_nvperf_host_LIBRARY:FILEPATH=/usr/local/cuda/extras/CUPTI/lib64/libnvperf_host.so

//Path to a library.
CUDA_nvperf_host_static_LIBRARY:FILEPATH=/usr/local/cuda/extras/CUPTI/lib64/libnvperf_host_static.a

//Path to a library.
CUDA_nvperf_target_LIBRARY:FILEPATH=/usr/local/cuda/extras/CUPTI/lib64/libnvperf_target.so

//Path to a library.
CUDA_nvrtc_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnvrtc.so

//Path to a library.
CUDA_nvrtc_builtins_LIBRARY:FILEPATH=/usr/local/cuda-10.2/targets/x86_64-linux/lib/libnvrtc-builtins.so

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=-fopenmp

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=gomp;pthread

//Path to the gomp library for OpenMP
OpenMP_gomp_LIBRARY:FILEPATH=/opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/libgomp.so

//Path to the pthread library for OpenMP
OpenMP_pthread_LIBRARY:FILEPATH=/lib64/libpthread.so


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug-
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=28
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/cmake/cmake/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/cmake/cmake/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/cmake/cmake/bin/ctest
//ADVANCED property for variable: CMAKE_CUDA_COMPILER
CMAKE_CUDA_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS
CMAKE_CUDA_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS_DEBUG
CMAKE_CUDA_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS_MINSIZEREL
CMAKE_CUDA_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS_RELEASE
CMAKE_CUDA_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CUDA_FLAGS_RELWITHDEBINFO
CMAKE_CUDA_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/cmake/cmake/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/data/data/qjs/CLionProjects/CUDAProjects
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=0
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/cmake/cmake/share/cmake-3.28
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDAToolkit_CUPTI_INCLUDE_DIR
CUDAToolkit_CUPTI_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDAToolkit_rt_LIBRARY
CUDAToolkit_rt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_CUDART
CUDA_CUDART-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_OpenCL_LIBRARY
CUDA_OpenCL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublasLt_LIBRARY
CUDA_cublasLt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublasLt_static_LIBRARY
CUDA_cublasLt_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublas_LIBRARY
CUDA_cublas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublas_static_LIBRARY
CUDA_cublas_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cuda_driver_LIBRARY
CUDA_cuda_driver_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudart_LIBRARY
CUDA_cudart_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudart_static_LIBRARY
CUDA_cudart_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufft_LIBRARY
CUDA_cufft_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufft_static_LIBRARY
CUDA_cufft_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufft_static_nocallback_LIBRARY
CUDA_cufft_static_nocallback_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufftw_LIBRARY
CUDA_cufftw_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufftw_static_LIBRARY
CUDA_cufftw_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_culibos_LIBRARY
CUDA_culibos_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cupti_LIBRARY
CUDA_cupti_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cupti_static_LIBRARY
CUDA_cupti_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_curand_LIBRARY
CUDA_curand_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_curand_static_LIBRARY
CUDA_curand_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_LIBRARY
CUDA_cusolver_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_lapack_static_LIBRARY
CUDA_cusolver_lapack_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_static_LIBRARY
CUDA_cusolver_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusparse_LIBRARY
CUDA_cusparse_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusparse_static_LIBRARY
CUDA_cusparse_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppc_LIBRARY
CUDA_nppc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppc_static_LIBRARY
CUDA_nppc_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppial_LIBRARY
CUDA_nppial_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppial_static_LIBRARY
CUDA_nppial_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicc_LIBRARY
CUDA_nppicc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicc_static_LIBRARY
CUDA_nppicc_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicom_LIBRARY
CUDA_nppicom_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicom_static_LIBRARY
CUDA_nppicom_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppidei_LIBRARY
CUDA_nppidei_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppidei_static_LIBRARY
CUDA_nppidei_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppif_LIBRARY
CUDA_nppif_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppif_static_LIBRARY
CUDA_nppif_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppig_LIBRARY
CUDA_nppig_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppig_static_LIBRARY
CUDA_nppig_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppim_LIBRARY
CUDA_nppim_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppim_static_LIBRARY
CUDA_nppim_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppist_LIBRARY
CUDA_nppist_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppist_static_LIBRARY
CUDA_nppist_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppisu_LIBRARY
CUDA_nppisu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppisu_static_LIBRARY
CUDA_nppisu_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppitc_LIBRARY
CUDA_nppitc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppitc_static_LIBRARY
CUDA_nppitc_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_npps_LIBRARY
CUDA_npps_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_npps_static_LIBRARY
CUDA_npps_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvToolsExt_LIBRARY
CUDA_nvToolsExt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvgraph_LIBRARY
CUDA_nvgraph_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvgraph_static_LIBRARY
CUDA_nvgraph_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvjpeg_LIBRARY
CUDA_nvjpeg_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvjpeg_static_LIBRARY
CUDA_nvjpeg_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvml_LIBRARY
CUDA_nvml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvperf_host_LIBRARY
CUDA_nvperf_host_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvperf_host_static_LIBRARY
CUDA_nvperf_host_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvperf_target_LIBRARY
CUDA_nvperf_target_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvrtc_LIBRARY
CUDA_nvrtc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvrtc_builtins_LIBRARY
CUDA_nvrtc_builtins_LIBRARY-ADVANCED:INTERNAL=1
//Details about finding CUDAToolkit
FIND_PACKAGE_MESSAGE_DETAILS_CUDAToolkit:INTERNAL=[/usr/local/cuda/targets/x86_64-linux/include][/usr/local/cuda/lib64/libcudart.so][/usr/local/cuda/bin][v10.2.89()]
//Details about finding OpenMP
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP:INTERNAL=[TRUE][c ][v4.5()]
//Details about finding OpenMP_CXX
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_CXX:INTERNAL=[-fopenmp][/opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/libgomp.so][/lib64/libpthread.so][v4.5()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_fopenmp:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//CXX compiler's OpenMP specification date
OpenMP_CXX_SPEC_DATE:INTERNAL=201511
//Result of TRY_COMPILE
OpenMP_SPECTEST_CXX_:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_gomp_LIBRARY
OpenMP_gomp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_pthread_LIBRARY
OpenMP_pthread_LIBRARY-ADVANCED:INTERNAL=1
//linker supports push/pop state
_CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED:INTERNAL=TRUE

