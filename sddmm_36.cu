// #include <iostream>
// #include <vector>
// #include <string>
// #include <fstream>
// #include <sstream>
// #include <algorithm>
// #include <numeric>
// #include <cmath>
// #include <chrono>
//
// #include <cuda_runtime.h>
// #include <cassert>
//
// // =================================================================
// // 常量定义
// // =================================================================
// const int MEDIUM_DENSITY_THRESHOLD = 32;
// const int HIGH_DENSITY_THRESHOLD = 256;
//
// const int LOW_DENSITY_BLOCK_SIZE = 256;
// const int MEDIUM_DENSITY_BLOCK_SIZE = 256;
// const int FINAL_HIGH_DENSITY_BLOCK_SIZE = 1024; // 使用1024线程以容纳32个Warp
//
// #define CUDA_CHECK(err) { \
//     cudaError_t e = err; \
//     if (e != cudaSuccess) { \
//         printf("Cuda error in file '%s' in line %d : %s.\n", __FILE__, __LINE__, cudaGetErrorString(e)); \
//         exit(EXIT_FAILURE); \
//     } \
// }
//
// struct CSRMatrix {
//     int *row_ptr, *col_idx;
//     float *values;
//     int rows, cols, nnz;
// };
//
// // =================================================================
// // CUDA 核函数
// // =================================================================
//
// // --- 分类核函数 (保持不变) ---
// __global__ void classify_rows_multi_level_kernel(
//     const int *row_ptr, int rows,
//     int *d_low_rows, int *d_medium_rows, int *d_high_rows,
//     int *low_count, int *medium_count, int *high_count) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row >= rows) return;
//     int nnz = row_ptr[row + 1] - row_ptr[row];
//     if (nnz > 0 && nnz <= MEDIUM_DENSITY_THRESHOLD) {
//         int pos = atomicAdd(low_count, 1);
//         d_low_rows[pos] = row;
//     } else if (nnz > MEDIUM_DENSITY_THRESHOLD && nnz <= HIGH_DENSITY_THRESHOLD) {
//         int pos = atomicAdd(medium_count, 1);
//         d_medium_rows[pos] = row;
//     } else if (nnz > HIGH_DENSITY_THRESHOLD) {
//         int pos = atomicAdd(high_count, 1);
//         d_high_rows[pos] = row;
//     }
// }
//
// // --- 低密度核函数 (保持不变) ---
// __global__ void sddmm_low_density_thread_per_row_kernel(
//     const float *__restrict__ A, const float *__restrict__ B,
//     const int *__restrict__ row_ptr, const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices, float *__restrict__ result,
//     int K, int num_rows) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     if (tid >= num_rows) return;
//     int row = row_indices[tid];
//     int row_start = row_ptr[row];
//     int row_end = row_ptr[row + 1];
//     for (int i = row_start; i < row_end; ++i) {
//         int col = col_idx[i];
//         float sum = 0.0f;
//         for (int k = 0; k < K; ++k) sum += A[row * K + k] * B[col * K + k];
//         result[i] = sum;
//     }
// }
//
// // --- 最终的高密度核函数 (采用您最高效的Warp协作模型) ---
// __global__ void sddmm_final_high_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ d_high_rows,
//     float *__restrict__ result,
//     int num_rows,
//     int K) {
//     int high_row_idx = blockIdx.x;
//     if (high_row_idx >= num_rows) return;
//
//     extern __shared__ float a_row_s[];
//     int row = d_high_rows[high_row_idx];
//
//     // 协作加载A的行向量到共享内存
//     for (int k = threadIdx.x; k < K; k += blockDim.x) {
//         a_row_s[k] = A[row * K + k];
//     }
//     __syncthreads();
//
//     // 工作分配给Warp，而不是Thread
//     int warp_id = threadIdx.x / 32;
//     int lane_id = threadIdx.x % 32;
//     int warps_in_block = blockDim.x / 32;
//
//     int row_start = row_ptr[row];
//     int nnz_in_row = row_ptr[row + 1] - row_start;
//
//     // 块内的所有Warp，并行处理不同的非零元
//     for (int nnz_offset = warp_id; nnz_offset < nnz_in_row; nnz_offset += warps_in_block) {
//         int global_idx = row_start + nnz_offset;
//         int col = col_idx[global_idx];
//
//         float partial_sum = 0.0f;
//
//         // Warp内的所有Lane，并行计算点积
//         for (int k = lane_id; k < K; k += 32) {
//             partial_sum += a_row_s[k] * B[col * K + k];
//         }
//
//         // Warp-Shuffle归约
//         for (int offset = 16; offset > 0; offset /= 2) {
//             partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
//         }
//
//         // Warp的0号线程将最终结果写回
//         if (lane_id == 0) {
//             result[global_idx] = partial_sum;
//         }
//     }
// }
//
//
// // =================================================================
// // 主控函数
// // =================================================================
// void sddmm_ultimate_final(
//     const float *d_A, const float *d_B, CSRMatrix &sparse, int K, size_t shared_mem_per_block) {
//     int *d_low_rows, *d_medium_rows, *d_high_rows;
//     int *d_low_count, *d_medium_count, *d_high_count;
//     CUDA_CHECK(cudaMalloc(&d_low_rows, sparse.rows * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_medium_rows, sparse.rows * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_high_rows, sparse.rows * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_low_count, sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_medium_count, sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&d_high_count, sizeof(int)));
//     CUDA_CHECK(cudaMemset(d_low_count, 0, sizeof(int)));
//     CUDA_CHECK(cudaMemset(d_medium_count, 0, sizeof(int)));
//     CUDA_CHECK(cudaMemset(d_high_count, 0, sizeof(int)));
//
//     cudaStream_t streams[3];
//     for (int i = 0; i < 3; ++i) CUDA_CHECK(cudaStreamCreate(&streams[i]));
//
//     dim3 block_classify(256);
//     dim3 grid_classify((sparse.rows + block_classify.x - 1) / block_classify.x);
//     classify_rows_multi_level_kernel<<<grid_classify, block_classify, 0, streams[0]>>>(
//         sparse.row_ptr, sparse.rows, d_low_rows, d_medium_rows, d_high_rows,
//         d_low_count, d_medium_count, d_high_count
//     );
//
//     int h_counts[3];
//     CUDA_CHECK(cudaMemcpyAsync(&h_counts[0], d_low_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
//     CUDA_CHECK(cudaMemcpyAsync(&h_counts[1], d_medium_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
//     CUDA_CHECK(cudaMemcpyAsync(&h_counts[2], d_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
//     CUDA_CHECK(cudaStreamSynchronize(streams[0])); // 等待分类和计数拷贝完成
//
//     printf("行分类结果: 低密度(<=%d)=%d, 中密度(<=%d)=%d, 高密度(>%d)=%d\n",
//            MEDIUM_DENSITY_THRESHOLD, h_counts[0], HIGH_DENSITY_THRESHOLD, h_counts[1], HIGH_DENSITY_THRESHOLD,
//            h_counts[2]);
//
//     // 低密度区
//     if (h_counts[0] > 0) {
//         dim3 block(LOW_DENSITY_BLOCK_SIZE);
//         dim3 grid((h_counts[0] + block.x - 1) / block.x);
//         sddmm_low_density_thread_per_row_kernel<<<grid, block, 0, streams[0]>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx, d_low_rows,
//             sparse.values, K, h_counts[0]
//         );
//     }
//
//     // 中/高密度区 (由于它们现在都使用Warp协作模型，可以合并处理逻辑)
//     // 中密度
//     if (h_counts[1] > 0) {
//         size_t required_mem = (size_t) K * sizeof(float);
//         if (required_mem <= shared_mem_per_block) {
//             dim3 block(MEDIUM_DENSITY_BLOCK_SIZE);
//             dim3 grid(h_counts[1]);
//             sddmm_final_high_density_kernel<<<grid, block, required_mem, streams[1]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_medium_rows,
//                 sparse.values, h_counts[1], K
//             );
//         } else {
//             // K太大时的备用路径
//             dim3 block(LOW_DENSITY_BLOCK_SIZE);
//             dim3 grid((h_counts[1] + block.x - 1) / block.x);
//             sddmm_low_density_thread_per_row_kernel<<<grid, block, 0, streams[1]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_medium_rows,
//                 sparse.values, K, h_counts[1]
//             );
//         }
//     }
//     // 高密度
//     if (h_counts[2] > 0) {
//         size_t required_mem = (size_t) K * sizeof(float);
//         if (required_mem <= shared_mem_per_block) {
//             printf("高密度区策略: 终极内核 (1块/行, Warp协作模型)\n");
//             dim3 block(FINAL_HIGH_DENSITY_BLOCK_SIZE);
//             dim3 grid(h_counts[2]);
//             sddmm_final_high_density_kernel<<<grid, block, required_mem, streams[2]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx, d_high_rows,
//                 sparse.values, h_counts[2], K
//             );
//         } else {
//             // 对于K极大的情况，分段策略依然是必须的，但此处我们专注于优化您当前场景
//             printf("高密度区策略: K=%d太大, 无法放入共享内存。此场景需要分段备用策略(未在此版本中激活)。\n", K);
//         }
//     }
//
//     for (int i = 0; i < 3; ++i) CUDA_CHECK(cudaStreamSynchronize(streams[i]));
//     for (int i = 0; i < 3; ++i) CUDA_CHECK(cudaStreamDestroy(streams[i]));
//     CUDA_CHECK(cudaFree(d_low_rows));
//     CUDA_CHECK(cudaFree(d_medium_rows));
//     CUDA_CHECK(cudaFree(d_high_rows));
//     CUDA_CHECK(cudaFree(d_low_count));
//     CUDA_CHECK(cudaFree(d_medium_count));
//     CUDA_CHECK(cudaFree(d_high_count));
// }
//
//
// // =================================================================
// // 辅助函数和Main函数 (与上一版基本相同)
// // =================================================================
// void sddmm_cpu_reference(const float *A, const float *B, const int *row_ptr, const int *col_idx, float *values, int M,
//                          int N, int K);
//
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz, std::vector<int> &coo_rows,
//                      std::vector<int> &coo_cols);
//
// void coo_to_csr(int M, int nnz, const std::vector<int> &coo_rows_in, const std::vector<int> &coo_cols_in,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx);
//
// __global__ void warmup_kernel();
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "Usage: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr));
//     std::string filename = argv[1];
//     int K = (argc > 2) ? std::atoi(argv[2]) : 128;
//
//     int device;
//     cudaGetDevice(&device);
//     cudaDeviceProp prop;
//     cudaGetDeviceProperties(&prop, device);
//     printf("=== GPU信息 ===\n设备名称: %s (Compute Capability %d.%d)\n", prop.name, prop.major, prop.minor);
//     printf("设备共享内存/块: %zu bytes\n\n", prop.sharedMemPerBlock);
//
//     int M, N, nnz;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(M, nnz, coo_rows, coo_cols, h_csr_row_ptr, h_csr_col_idx);
//
//     std::vector<float> h_A((size_t) M * K);
//     std::vector<float> h_B((size_t) N * K);
//     for (size_t i = 0; i < (size_t) M * K; ++i) h_A[i] = (rand() % 100) / 100.0f;
//     for (size_t i = 0; i < (size_t) N * K; ++i) h_B[i] = (rand() % 100) / 100.0f;
//
//     float *d_A, *d_B;
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//     CUDA_CHECK(cudaMalloc(&d_A, (size_t)M * K * sizeof(float)));
//     CUDA_CHECK(cudaMalloc(&d_B, (size_t)N * K * sizeof(float)));
//     CUDA_CHECK(cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&sparse.col_idx, nnz * sizeof(int)));
//     CUDA_CHECK(cudaMalloc(&sparse.values, nnz * sizeof(float)));
//     CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), (size_t)M * K * sizeof(float), cudaMemcpyHostToDevice));
//     CUDA_CHECK(cudaMemcpy(d_B, h_B.data(), (size_t)N * K * sizeof(float), cudaMemcpyHostToDevice));
//     CUDA_CHECK(cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
//     CUDA_CHECK(cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice));
//
//     printf("=== 矩阵信息 ===\n");
//     printf("矩阵文件: %s\n", filename.c_str());
//     printf("矩阵维度: M=%d, N=%d, K=%d\n", M, N, K);
//     printf("非零元素: %d (稀疏度: %.4f%%)\n\n", nnz, 100.0 * nnz / ((double) M * N));
//
//     std::cout << "预热GPU..." << std::endl;
//     warmup_kernel<<<1, 1>>>();
//     CUDA_CHECK(cudaDeviceSynchronize());
//
//     std::cout << "开始执行SDDMM终极优化版本..." << std::endl;
//     const int num_runs = 5;
//     std::vector<float> times;
//     cudaEvent_t start, stop;
//     CUDA_CHECK(cudaEventCreate(&start));
//     CUDA_CHECK(cudaEventCreate(&stop));
//
//     for (int run = 0; run < num_runs; run++) {
//         CUDA_CHECK(cudaMemset(sparse.values, 0, (size_t)nnz * sizeof(float)));
//         CUDA_CHECK(cudaDeviceSynchronize());
//
//         CUDA_CHECK(cudaEventRecord(start));
//         sddmm_ultimate_final(d_A, d_B, sparse, K, prop.sharedMemPerBlock);
//         CUDA_CHECK(cudaEventRecord(stop));
//
//         CUDA_CHECK(cudaEventSynchronize(stop));
//         float ms;
//         CUDA_CHECK(cudaEventElapsedTime(&ms, start, stop));
//         times.push_back(ms);
//         printf("运行 %d: %.3f ms\n", run + 1, ms);
//     }
//     CUDA_CHECK(cudaEventDestroy(start));
//     CUDA_CHECK(cudaEventDestroy(stop));
//
//     float min_time = *std::min_element(times.begin(), times.end());
//     float avg_time = std::accumulate(times.begin(), times.end(), 0.0f) / num_runs;
//     double gflops = (2.0 * nnz * K) / (min_time * 1e6);
//     printf("\n=== 性能统计 ===\n");
//     printf("平均时间: %.3f ms\n", avg_time);
//     printf("最佳时间: %.3f ms\n", min_time);
//     printf("峰值性能: %.2f GFLOPS\n", gflops);
//
//     std::cout << "\n验证计算正确性..." << std::endl;
//     std::vector<float> h_values_gpu(nnz);
//     CUDA_CHECK(cudaMemcpy(h_values_gpu.data(), sparse.values, (size_t)nnz * sizeof(float), cudaMemcpyDeviceToHost));
//
//     std::vector<float> h_values_cpu(nnz, 0.0f);
//     auto cpu_start = std::chrono::high_resolution_clock::now();
//     sddmm_cpu_reference(h_A.data(), h_B.data(), h_csr_row_ptr.data(), h_csr_col_idx.data(), h_values_cpu.data(), M, N,
//                         K);
//     auto cpu_end = std::chrono::high_resolution_clock::now();
//     auto cpu_duration = std::chrono::duration_cast<std::chrono::milliseconds>(cpu_end - cpu_start);
//     printf("CPU (OMP) 参考实现时间: %ld ms\n", cpu_duration.count());
//     if (min_time > 0) printf("GPU加速比: %.2fx\n", (float) cpu_duration.count() / min_time);
//
//     int correct = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; i++) {
//         float diff = fabs(h_values_cpu[i] - h_values_gpu[i]);
//         if (diff < 1e-4) {
//             correct++;
//         }
//         if (diff > max_error) {
//             max_error = diff;
//         }
//     }
//
//     double total_error = 0.0, l1_norm = 0.0;
//     for (int i = 0; i < nnz; i++) {
//         total_error += std::abs(h_values_cpu[i] - h_values_gpu[i]);
//         l1_norm += std::abs(h_values_cpu[i]);
//     }
//     printf("\n=== 验证结果 ===\n");
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct / nnz) << "%" << std::endl;
//     if (l1_norm > 0) printf("相对误差 (L1 Norm): %e\n", total_error / l1_norm);
//     else printf("CPU结果全为0, 无法计算相对误差。\n");
//
//     CUDA_CHECK(cudaFree(d_A));
//     CUDA_CHECK(cudaFree(d_B));
//     CUDA_CHECK(cudaFree(sparse.row_ptr));
//     CUDA_CHECK(cudaFree(sparse.col_idx));
//     CUDA_CHECK(cudaFree(sparse.values));
//     return 0;
// }
//
// // -----------------------------------------------------------------
// // (Placeholders for unchanged helper functions)
// // You need to copy the full implementation of these from the previous response
// void sddmm_cpu_reference(const float *A, const float *B, const int *row_ptr, const int *col_idx, float *values, int M,
//                          int N, int K) {
// #pragma omp parallel for
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz, std::vector<int> &coo_rows,
//                      std::vector<int> &coo_cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//     while (file.peek() == '%') file.ignore(2048, '\n');
//     file >> M >> N >> nnz;
//     coo_rows.resize(nnz);
//     coo_cols.resize(nnz);
//     for (int i = 0; i < nnz; ++i) {
//         int r, c;
//         file >> r >> c;
//         coo_rows[i] = r - 1;
//         coo_cols[i] = c - 1;
//         file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
//     }
//     file.close();
// }
//
// void coo_to_csr(int M, int nnz, const std::vector<int> &coo_rows_in, const std::vector<int> &coo_cols_in,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
//     csr_row_ptr.assign(M + 1, 0);
//     csr_col_idx.resize(nnz);
//     std::vector<std::pair<int, int> > coo(nnz);
//     for (int i = 0; i < nnz; ++i) coo[i] = {coo_rows_in[i], coo_cols_in[i]};
//     std::sort(coo.begin(), coo.end());
//     for (int i = 0; i < nnz; ++i) {
//         csr_col_idx[i] = coo[i].second;
//         csr_row_ptr[coo[i].first + 1]++;
//     }
//     for (int i = 0; i < M; ++i) csr_row_ptr[i + 1] += csr_row_ptr[i];
// }
//
// __global__ void warmup_kernel() {
// }
//
//
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // === GPU信息 ===
// // 设备名称: Tesla P100-PCIE-16GB (Compute Capability 6.0)
// // 设备共享内存/块: 49152 bytes
// //
// // === 矩阵信息 ===
// // 矩阵文件: /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 矩阵维度: M=12471, N=872622, K=128
// // 非零元素: 22624727 (稀疏度: 0.2079%)
// //
// // 预热GPU...
// // 开始执行SDDMM终极优化版本...
// // 行分类结果: 低密度(<=32)=1459, 中密度(<=256)=3293, 高密度(>256)=7719
// // 高密度区策略: 终极内核 (1块/行, Warp协作模型)
// // 运行 1: 25.675 ms
// // 行分类结果: 低密度(<=32)=1459, 中密度(<=256)=3293, 高密度(>256)=7719
// // 高密度区策略: 终极内核 (1块/行, Warp协作模型)
// // 运行 2: 24.798 ms
// // 行分类结果: 低密度(<=32)=1459, 中密度(<=256)=3293, 高密度(>256)=7719
// // 高密度区策略: 终极内核 (1块/行, Warp协作模型)
// // 运行 3: 24.686 ms
// // 行分类结果: 低密度(<=32)=1459, 中密度(<=256)=3293, 高密度(>256)=7719
// // 高密度区策略: 终极内核 (1块/行, Warp协作模型)
// // 运行 4: 24.659 ms
// // 行分类结果: 低密度(<=32)=1459, 中密度(<=256)=3293, 高密度(>256)=7719
// // 高密度区策略: 终极内核 (1块/行, Warp协作模型)
// // 运行 5: 24.099 ms
// //
// // === 性能统计 ===
// // 平均时间: 24.783 ms
// // 最佳时间: 24.099 ms
// // 峰值性能: 240.33 GFLOPS
// //
// // 验证计算正确性...
// // CPU (OMP) 参考实现时间: 8690 ms
// // GPU加速比: 360.59x
// //
// // === 验证结果 ===
// // 相对误差 (L1 Norm): 1.285904e-07
// //
// // 进程已结束，退出代码为 0
