#!/bin/bash

# SDDMM 优化效果测试脚本

echo "=========================================="
echo "SDDMM 高密度区核函数优化效果测试"
echo "=========================================="

# 检查CUDA环境
echo "1. 检查CUDA环境..."
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到nvcc编译器，请确保CUDA已正确安装"
    exit 1
fi

echo "CUDA版本:"
nvcc --version | grep "release"

echo "GPU信息:"
nvidia-smi --query-gpu=name,memory.total,compute_cap --format=csv,noheader,nounits

echo ""

# 编译程序
echo "2. 编译程序..."
make clean > /dev/null 2>&1

echo "编译原始版本..."
if ! make sddmm_original > /dev/null 2>&1; then
    echo "警告: 原始版本编译失败，跳过对比测试"
    COMPARE_ORIGINAL=false
else
    echo "✓ 原始版本编译成功"
    COMPARE_ORIGINAL=true
fi

echo "编译优化版本..."
if ! make sddmm_performance_test > /dev/null 2>&1; then
    echo "错误: 优化版本编译失败"
    exit 1
fi
echo "✓ 优化版本编译成功"

echo ""

# 检查测试数据
echo "3. 检查测试数据..."
TEST_MATRIX=""
if [ -f "dataset/12month1.mtx" ]; then
    TEST_MATRIX="dataset/12month1.mtx"
    echo "✓ 找到测试矩阵: $TEST_MATRIX"
elif [ -f "dataset/nips.mtx" ]; then
    TEST_MATRIX="dataset/nips.mtx"
    echo "✓ 找到测试矩阵: $TEST_MATRIX"
else
    echo "警告: 未找到测试矩阵文件"
    echo "请将.mtx格式的稀疏矩阵文件放入dataset/目录"
    echo "可以从以下网站下载测试矩阵:"
    echo "  - https://sparse.tamu.edu/"
    echo "  - https://www.cise.ufl.edu/research/sparse/matrices/"
    
    # 创建一个小的测试矩阵
    echo "创建小型测试矩阵..."
    mkdir -p dataset
    cat > dataset/test_small.mtx << EOF
%%MatrixMarket matrix coordinate real general
100 100 1000
1 1 1.0
1 2 2.0
2 1 3.0
2 2 4.0
EOF
    
    # 生成更多测试数据
    for i in {3..100}; do
        for j in {1..10}; do
            if [ $((i * j)) -le 1000 ]; then
                echo "$i $j 1.0" >> dataset/test_small.mtx
            fi
        done
    done
    
    TEST_MATRIX="dataset/test_small.mtx"
    echo "✓ 创建了小型测试矩阵: $TEST_MATRIX"
fi

echo ""

# 运行性能测试
echo "4. 运行性能测试..."

# 测试不同的K值
K_VALUES=(64 128 256)

for K in "${K_VALUES[@]}"; do
    echo "----------------------------------------"
    echo "测试 K=$K"
    echo "----------------------------------------"
    
    if [ "$COMPARE_ORIGINAL" = true ]; then
        echo "原始版本:"
        timeout 60s ./sddmm_original "$TEST_MATRIX" $K 2>/dev/null || echo "原始版本测试超时或失败"
        echo ""
    fi
    
    echo "优化版本:"
    timeout 60s ./sddmm_performance_test "$TEST_MATRIX" $K 2>/dev/null || echo "优化版本测试失败"
    echo ""
done

echo "=========================================="
echo "测试完成"
echo "=========================================="

# 性能分析建议
echo ""
echo "性能分析建议:"
echo "1. 如果优化版本性能提升明显，说明优化有效"
echo "2. 对于大K值(>128)，优化效果应该更明显"
echo "3. 可以使用 'make profile' 进行详细性能分析"
echo "4. 可以使用 'make memcheck' 检查内存使用"

# 清理
echo ""
echo "清理临时文件..."
make clean > /dev/null 2>&1
echo "✓ 清理完成"

echo ""
echo "如需更详细的测试，请运行:"
echo "  make test_k    # 测试不同K值"
echo "  make profile   # 性能分析"
echo "  make memcheck  # 内存检查"
