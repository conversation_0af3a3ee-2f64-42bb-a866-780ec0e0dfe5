// #include <iostream>
// #include <fstream>
// #include <sstream>
// #include <string>
// #include <cstdio>
// #include <cstdlib>
// #include <vector>
// #include <unordered_map>
// #include <algorithm>
// #include <cuda_runtime.h>
// #include <cassert>
// #include <random>
// #include <bits/stdc++.h>
//
// using namespace std;
//
// #define CUDA_CHECK(call) \
// do { \
//     cudaError_t err = (call); \
//     if (err != cudaSuccess) { \
//         fprintf(stderr, "CUDA error at %s:%d code=%d(%s) \"%s\"\n", \
//         __FILE__, __LINE__, err, cudaGetErrorString(err), #call); \
//         exit(EXIT_FAILURE); \
//     } \
// } while(0)
//
// void print_array(const float *arr, int size, const char *name) {
//     printf("%s: [", name);
//     for (int i = 0; i < size; i++) {
//         printf("%.1f%s", arr[i], (i == size - 1) ? "]\n" : ", ");
//     }
// }
//
// struct BlockInfo {
//     int nnz;
//     int *h_local_A_rows;
//     int *h_local_B_cols;
//     float *h_A_block;
//     float *h_B_block;
//     int num_A_rows;
//     int num_B_cols;
//     int *h_unique_rows;
//     int *h_unique_cols;
//     int num_unique_rows;
//     int num_unique_cols;
// };
//
//
// __global__ void sddmm_kernel(const float *A_block, const float *B_block, const int *local_A_rows,
//                              const int *local_B_cols, float *output, int nnz, int K, int num_A_rows) {
//     extern __shared__ float A_shared[];
//
//     for (int i = threadIdx.x; i < num_A_rows * K; i += blockDim.x) {
//         A_shared[i] = A_block[i];
//     }
//     __syncthreads();
//
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (idx >= nnz) return;
//
//     int local_row = local_A_rows[idx];
//     int local_col = local_B_cols[idx];
//
//     const float *A_row = A_shared + local_row * K;
//     const float *B_col = B_block + local_col * K;
//
//     float sum = 0.0f;
//     for (int k = 0; k < K; k++) {
//         sum += A_row[k] * B_col[k];
//     }
//
//     output[idx] = sum;
// }
//
//
// vector<int> find_high_freq_indices(const int *indices, int length, int top_n) {
//     unordered_map<int, int> freq_map;
//     for (int i = 0; i < length; i++) {
//         freq_map[indices[i]]++;
//     }
//
//     vector<pair<int, int> > sorted_freq(freq_map.begin(), freq_map.end());
//     sort(sorted_freq.begin(), sorted_freq.end(),
//          [](const pair<int, int> &a, const pair<int, int> &b) {
//              return a.second > b.second;
//          });
//
//     vector<int> high_freq;
//     for (int i = 0; i < min(top_n, (int) sorted_freq.size()); i++) {
//         high_freq.push_back(sorted_freq[i].first);
//     }
//     return high_freq;
// }
//
// float *d_high_freq_A = nullptr;
// float *d_high_freq_B = nullptr;
//
// void preprocess_blocks(BlockInfo *blocks, int num_blocks, const float *A, const float *B,
//                        const int *all_rows, const int *all_cols, int total_nnz, int K, int N,
//                        const vector<int> &high_freq_rows, const vector<int> &high_freq_cols,
//                        float &pre_h2d_time) {
//     pre_h2d_time = 0.0f;
//     cudaEvent_t start, stop;
//     CUDA_CHECK(cudaEventCreate(&start));
//     CUDA_CHECK(cudaEventCreate(&stop));
//
//     int num_high_rows = high_freq_rows.size();
//     int num_high_cols = high_freq_cols.size();
//     CUDA_CHECK(cudaMalloc(&d_high_freq_A, num_high_rows * K * sizeof(float)));
//     CUDA_CHECK(cudaMalloc(&d_high_freq_B, num_high_cols * K * sizeof(float)));
//
//     // Copy high-frequency rows and columns to device
//     vector<float> high_A_data(num_high_rows * K);
//     for (int i = 0; i < num_high_rows; i++) {
//         int r = high_freq_rows[i];
//         memcpy(high_A_data.data() + i * K, A + r * K, K * sizeof(float));
//     }
//     CUDA_CHECK(cudaEventRecord(start));
//     CUDA_CHECK(cudaMemcpy(d_high_freq_A, high_A_data.data(), num_high_rows * K * sizeof(float), cudaMemcpyHostToDevice))
//     ;
//     CUDA_CHECK(cudaEventRecord(stop));
//     CUDA_CHECK(cudaEventSynchronize(stop));
//     float time;
//     CUDA_CHECK(cudaEventElapsedTime(&time, start, stop));
//     pre_h2d_time += time;
//
//     vector<float> high_B_data(num_high_cols * K);
//     for (int i = 0; i < num_high_cols; i++) {
//         int c = high_freq_cols[i];
//         for (int k = 0; k < K; k++) {
//             high_B_data[i * K + k] = B[c * K + k]; // Corrected B access for row-major
//         }
//     }
//     CUDA_CHECK(cudaEventRecord(start));
//     CUDA_CHECK(cudaMemcpy(d_high_freq_B, high_B_data.data(), num_high_cols * K * sizeof(float), cudaMemcpyHostToDevice))
//     ;
//     CUDA_CHECK(cudaEventRecord(stop));
//     CUDA_CHECK(cudaEventSynchronize(stop));
//     CUDA_CHECK(cudaEventElapsedTime(&time, start, stop));
//     pre_h2d_time += time;
//
//     unordered_set<int> high_row_set(high_freq_rows.begin(), high_freq_rows.end());
//     unordered_set<int> high_col_set(high_freq_cols.begin(), high_freq_cols.end());
//
//     // Distribute NNZs among blocks
//     int nnz_per_block = (total_nnz + num_blocks - 1) / num_blocks;
//     for (int b = 0; b < num_blocks; b++) {
//         int start = b * nnz_per_block;
//         int end = min(start + nnz_per_block, total_nnz);
//         blocks[b].nnz = end - start;
//
//         unordered_map<int, int> row_map, col_map;
//         vector<int> unique_rows, unique_cols;
//
//         for (int i = start; i < end; i++) {
//             int r = all_rows[i];
//             int c = all_cols[i];
//             if (!row_map.count(r)) {
//                 row_map[r] = unique_rows.size();
//                 unique_rows.push_back(r);
//             }
//             if (!col_map.count(c)) {
//                 col_map[c] = unique_cols.size();
//                 unique_cols.push_back(c);
//             }
//         }
//
//         vector<int> non_high_rows, high_rows_in_block;
//         for (int r: unique_rows) {
//             if (high_row_set.count(r)) {
//                 high_rows_in_block.push_back(r);
//             } else {
//                 non_high_rows.push_back(r);
//             }
//         }
//
//         // 非高频数据在前，高频数据在后，便于将连续的非高频数据传递到GPU
//         row_map.clear();
//         for (size_t i = 0; i < non_high_rows.size(); i++) {
//             row_map[non_high_rows[i]] = i;
//         }
//         for (size_t i = 0; i < high_rows_in_block.size(); i++) {
//             row_map[high_rows_in_block[i]] = non_high_rows.size() + i;
//         }
//
//         vector<int> non_high_cols, high_cols_in_block;
//         for (int c: unique_cols) {
//             if (high_col_set.count(c)) {
//                 high_cols_in_block.push_back(c);
//             } else {
//                 non_high_cols.push_back(c);
//             }
//         }
//
//         col_map.clear();
//         for (size_t i = 0; i < non_high_cols.size(); i++) {
//             col_map[non_high_cols[i]] = i;
//         }
//         for (size_t i = 0; i < high_cols_in_block.size(); i++) {
//             col_map[high_cols_in_block[i]] = non_high_cols.size() + i;
//         }
//
//         blocks[b].num_A_rows = non_high_rows.size();
//         blocks[b].num_B_cols = non_high_cols.size();
//         blocks[b].h_A_block = new float[blocks[b].num_A_rows * K];
//         blocks[b].h_B_block = new float[blocks[b].num_B_cols * K];
//         blocks[b].num_unique_rows = unique_rows.size();
//         blocks[b].num_unique_cols = unique_cols.size();
//
//         for (size_t i = 0; i < non_high_rows.size(); i++) {
//             int r = non_high_rows[i];
//             memcpy(blocks[b].h_A_block + i * K, A + r * K, K * sizeof(float));
//         }
//
//         for (size_t i = 0; i < non_high_cols.size(); i++) {
//             int c = non_high_cols[i];
//             for (int k = 0; k < K; k++) {
//                 blocks[b].h_B_block[i * K + k] = B[c * K + k]; // Corrected B access
//             }
//         }
//
//         blocks[b].h_local_A_rows = new int[end - start];
//         blocks[b].h_local_B_cols = new int[end - start];
//         blocks[b].h_unique_rows = new int[unique_rows.size()];
//         memcpy(blocks[b].h_unique_rows, unique_rows.data(), unique_rows.size() * sizeof(int));
//         blocks[b].h_unique_cols = new int[unique_cols.size()];
//         memcpy(blocks[b].h_unique_cols, unique_cols.data(), unique_cols.size() * sizeof(int));
//
//         for (int i = start; i < end; i++) {
//             int idx = i - start;
//             int global_r = all_rows[i];
//             int global_c = all_cols[i];
//             blocks[b].h_local_A_rows[idx] = row_map[global_r];
//             blocks[b].h_local_B_cols[idx] = col_map[global_c];
//         }
//     }
//
//     CUDA_CHECK(cudaEventDestroy(start));
//     CUDA_CHECK(cudaEventDestroy(stop));
// }
//
// void execute_sddmm(BlockInfo *blocks, int num_blocks, int K, int N, float **d_results,
//                    const vector<int> &high_freq_rows, const vector<int> &high_freq_cols,
//                    float &elapsed_time, float &exec_h2d_time) {
//     exec_h2d_time = 0.0f;
//     const int num_buffers = 2;
//     cudaStream_t streams[num_buffers];
//     cudaEvent_t events[num_buffers];
//     cudaEvent_t kernel_start, kernel_stop, h2d_start, h2d_stop;
//     CUDA_CHECK(cudaEventCreate(&kernel_start));
//     CUDA_CHECK(cudaEventCreate(&kernel_stop));
//     CUDA_CHECK(cudaEventCreate(&h2d_start));
//     CUDA_CHECK(cudaEventCreate(&h2d_stop));
//     float kernel_time = 0.0f;
//
//     struct DeviceBuffers {
//         float *A_block = nullptr;
//         float *B_block = nullptr;
//         int *local_A_rows = nullptr;
//         int *local_B_cols = nullptr;
//         float *output = nullptr;
//     } d_buf[num_buffers];
//
//     size_t max_A_rows = 0, max_B_cols = 0, max_nnz = 0;
//     for (int b = 0; b < num_blocks; b++) {
//         max_A_rows = max(max_A_rows, (size_t) (blocks[b].num_A_rows + blocks[b].num_unique_rows));
//         max_B_cols = max(max_B_cols, (size_t) (blocks[b].num_B_cols + blocks[b].num_unique_cols));
//         max_nnz = max(max_nnz, (size_t) blocks[b].nnz);
//     }
//
//     for (int i = 0; i < num_buffers; i++) {
//         CUDA_CHECK(cudaMalloc(&d_buf[i].A_block, max_A_rows * K * sizeof(float)));
//         CUDA_CHECK(cudaMalloc(&d_buf[i].B_block, max_B_cols * K * sizeof(float)));
//         CUDA_CHECK(cudaMalloc(&d_buf[i].local_A_rows, max_nnz * sizeof(int)));
//         CUDA_CHECK(cudaMalloc(&d_buf[i].local_B_cols, max_nnz * sizeof(int)));
//         CUDA_CHECK(cudaMalloc(&d_buf[i].output, max_nnz * sizeof(float)));
//         CUDA_CHECK(cudaStreamCreate(&streams[i]));
//         CUDA_CHECK(cudaEventCreate(&events[i]));
//     }
//
//     unordered_map<int, int> row_idx_map;
//     for (size_t i = 0; i < high_freq_rows.size(); i++) row_idx_map[high_freq_rows[i]] = i;
//     unordered_map<int, int> col_idx_map;
//     for (size_t i = 0; i < high_freq_cols.size(); i++) col_idx_map[high_freq_cols[i]] = i;
//
//     int current = 0;
//     for (int b = 0; b < num_blocks; b++) {
//         int next = (current + 1) % num_buffers;
//
//         if (blocks[b].num_A_rows > 0) {
//             CUDA_CHECK(cudaEventRecord(h2d_start, streams[current]));
//             CUDA_CHECK(cudaMemcpyAsync(
//                 d_buf[current].A_block,
//                 blocks[b].h_A_block,
//                 blocks[b].num_A_rows * K * sizeof(float),
//                 cudaMemcpyHostToDevice,
//                 streams[current]
//             ));
//             CUDA_CHECK(cudaEventRecord(h2d_stop, streams[current]));
//             CUDA_CHECK(cudaEventSynchronize(h2d_stop));
//             float t;
//             CUDA_CHECK(cudaEventElapsedTime(&t, h2d_start, h2d_stop));
//             exec_h2d_time += t;
//         }
//
//         vector<int> high_rows_in_block;
//         for (int i = 0; i < blocks[b].num_unique_rows; i++) {
//             int r = blocks[b].h_unique_rows[i];
//             if (row_idx_map.count(r)) high_rows_in_block.push_back(r);
//         }
//         size_t high_A_offset = blocks[b].num_A_rows * K;
//         for (int r: high_rows_in_block) {
//             int idx = row_idx_map[r];
//             CUDA_CHECK(cudaMemcpyAsync(
//                 d_buf[current].A_block + high_A_offset,
//                 d_high_freq_A + idx * K,
//                 K * sizeof(float),
//                 cudaMemcpyDeviceToDevice,
//                 streams[current]
//             ));
//             high_A_offset += K;
//         }
//
//         if (blocks[b].num_B_cols > 0) {
//             CUDA_CHECK(cudaEventRecord(h2d_start, streams[current])); // <-- 开始
//             CUDA_CHECK(cudaMemcpyAsync(
//                 d_buf[current].B_block,
//                 blocks[b].h_B_block,
//                 blocks[b].num_B_cols * K * sizeof(float),
//                 cudaMemcpyHostToDevice,
//                 streams[current]
//             ));
//             CUDA_CHECK(cudaEventRecord(h2d_stop, streams[current])); // <-- 结束
//             CUDA_CHECK(cudaEventSynchronize(h2d_stop));
//             float t;
//             CUDA_CHECK(cudaEventElapsedTime(&t, h2d_start, h2d_stop));
//             exec_h2d_time += t;
//         }
//
//         vector<int> high_cols_in_block;
//         for (int i = 0; i < blocks[b].num_unique_cols; i++) {
//             int c = blocks[b].h_unique_cols[i];
//             if (col_idx_map.count(c)) high_cols_in_block.push_back(c);
//         }
//
//         size_t high_B_offset = blocks[b].num_B_cols * K;
//         for (int c: high_cols_in_block) {
//             int idx = col_idx_map[c];
//             CUDA_CHECK(cudaMemcpyAsync(
//                 d_buf[current].B_block + high_B_offset,
//                 d_high_freq_B + idx * K,
//                 K * sizeof(float),
//                 cudaMemcpyDeviceToDevice,
//                 streams[current]
//             ));
//             high_B_offset += K;
//         }
//
//         CUDA_CHECK(cudaMemcpyAsync(
//             d_buf[current].local_A_rows,
//             blocks[b].h_local_A_rows,
//             blocks[b].nnz * sizeof(int),
//             cudaMemcpyHostToDevice,
//             streams[current]
//         ));
//         CUDA_CHECK(cudaMemcpyAsync(
//             d_buf[current].local_B_cols,
//             blocks[b].h_local_B_cols,
//             blocks[b].nnz * sizeof(int),
//             cudaMemcpyHostToDevice,
//             streams[current]
//         ));
//
//         dim3 block(256);
//         dim3 grid((blocks[b].nnz + block.x - 1) / block.x);
//         size_t shared_mem_size = (blocks[b].num_A_rows + high_rows_in_block.size()) * K * sizeof(float);
//
//         CUDA_CHECK(cudaEventRecord(kernel_start, streams[current]));
//         sddmm_kernel<<<grid, block, shared_mem_size, streams[current]>>>(
//             d_buf[current].A_block,
//             d_buf[current].B_block,
//             d_buf[current].local_A_rows,
//             d_buf[current].local_B_cols,
//             d_buf[current].output,
//             blocks[b].nnz,
//             K,
//             blocks[b].num_A_rows + high_rows_in_block.size()
//         );
//         CUDA_CHECK(cudaEventRecord(kernel_stop, streams[current]));
//         CUDA_CHECK(cudaEventSynchronize(kernel_stop));
//         float temp_time;
//         CUDA_CHECK(cudaEventElapsedTime(&temp_time, kernel_start, kernel_stop));
//         kernel_time += temp_time;
//
//         CUDA_CHECK(cudaMemcpyAsync(
//             d_results[b],
//             d_buf[current].output,
//             blocks[b].nnz * sizeof(float),
//             cudaMemcpyDeviceToHost,
//             streams[current]
//         ));
//
//         current = next;
//     }
//
//     for (int i = 0; i < num_buffers; i++) {
//         CUDA_CHECK(cudaStreamSynchronize(streams[i]));
//     }
//
//     for (int i = 0; i < num_buffers; i++) {
//         CUDA_CHECK(cudaFree(d_buf[i].A_block));
//         CUDA_CHECK(cudaFree(d_buf[i].B_block));
//         CUDA_CHECK(cudaFree(d_buf[i].local_A_rows));
//         CUDA_CHECK(cudaFree(d_buf[i].local_B_cols));
//         CUDA_CHECK(cudaFree(d_buf[i].output));
//         CUDA_CHECK(cudaStreamDestroy(streams[i]));
//         CUDA_CHECK(cudaEventDestroy(events[i]));
//     }
//
//     CUDA_CHECK(cudaEventDestroy(kernel_start));
//     CUDA_CHECK(cudaEventDestroy(kernel_stop));
//     CUDA_CHECK(cudaEventDestroy(h2d_start));
//     CUDA_CHECK(cudaEventDestroy(h2d_stop));
//     elapsed_time = kernel_time;
// }
//
// void initial(float *X, long n, int k) {
//     // 使用C++11随机数库
//     static std::mt19937 generator(1);
//     std::uniform_real_distribution<float> distribution(0.0f, 0.1f); // 生成[0,0.1)范围的随机数
//
//     for (long r = 0; r < n; ++r) {
//         for (long t = 0; t < k; ++t)
//             X[r * k + t] = distribution(generator); // 生成目标范围的随机数
//     }
// }
//
// vector<int> adaptive_topn_selection(const int *indices, int total_nnz, float coverage_threshold = 0.8) {
//     // 统计每个索引的出现次数
//     unordered_map<int, int> freq_map;
//     for (int i = 0; i < total_nnz; i++) {
//         freq_map[indices[i]]++;
//     }
//
//     // 按照出现次数降序排序
//     vector<pair<int, int> > sorted(freq_map.begin(), freq_map.end());
//     sort(sorted.begin(), sorted.end(), [](const pair<int, int> &a, const pair<int, int> &b) {
//         return a.second > b.second;
//     });
//
//     // 选中高频元素直到达到覆盖率要求时停止
//     int total = total_nnz;
//     int current = 0;
//     vector<int> selected;
//     for (auto &p: sorted) {
//         current += p.second;
//         selected.push_back(p.first);
//         if ((float) current / total >= coverage_threshold) {
//             break;
//         }
//     }
//     return selected;
// }
//
// int main(int argc, char *argv[]) {
//     ifstream fp(argv[1]);
//     const int K = atoi(argv[2]);
//     const int tile_sizeX = atoi(argv[3]);
//     const int tile_sizeY = atoi(argv[4]);
//
//     // 读取矩阵头信息
//     string str;
//     while (getline(fp, str) && !isdigit(str[0])) {
//     }
//     int M, N, total_nnz;
//     istringstream header(str);
//     header >> M >> N >> total_nnz;
//
//     // 初始化A（行优先）、B（列优先）
//     float *A = new float[M * K];
//     initial(A, M, K);
//     float *B = new float[K * N];
//     initial(B, K, N);
//
//     // 读取非零元素数据
//     int *all_rows = new int[total_nnz];
//     int *all_cols = new int[total_nnz];
//     for (int idx = 0; idx < total_nnz; ++idx) {
//         int rid, cid;
//         float val;
//         // fp >> rid >> cid >> val;
//         fp >> cid >> rid;
//         all_rows[idx] = rid - 1;
//         all_cols[idx] = cid - 1;
//     }
//
//     // 统计高频行列
//     const int top_n = 500;
//     const int num_blocks = (total_nnz + tile_sizeX - 1) / tile_sizeX;
//     // vector<int> high_freq_rows = find_high_freq_indices(all_rows, total_nnz, top_n);
//     // vector<int> high_freq_cols = find_high_freq_indices(all_cols, total_nnz, top_n);
//     // 动态计算高频行/列
//     vector<int> high_freq_rows = adaptive_topn_selection(all_rows, total_nnz, 0.8);
//     vector<int> high_freq_cols = adaptive_topn_selection(all_cols, total_nnz, 0.8);
//
//     // 预处理和计算
//     BlockInfo *blocks = new BlockInfo[num_blocks];
//     float **d_results = new float *[num_blocks];
//     for (int b = 0; b < num_blocks; b++) {
//         d_results[b] = new float[total_nnz];
//     }
//
//     float pre_h2d_time = 0.0f;
//     preprocess_blocks(blocks, num_blocks, A, B, all_rows, all_cols, total_nnz, K, N,
//                       high_freq_rows, high_freq_cols, pre_h2d_time);
//
//     float exec_h2d_time = 0.0f;
//     float elapsed_time;
//     execute_sddmm(blocks, num_blocks, K, N, d_results, high_freq_rows, high_freq_cols,
//                   elapsed_time, exec_h2d_time);
//
//     // 计算并打印总传输时间
//     float total_transfer_time = pre_h2d_time + exec_h2d_time;
//     printf("pre_h2d_time: %f\n", pre_h2d_time);
//     printf("exec_h2d_time: %f\n", exec_h2d_time);
//     printf("Total Host-to-Device (H2D) Transfer Time: %.4f ms\n", total_transfer_time);
//
//     // 合并结果
//     float *final_result = new float[total_nnz];
//     int offset = 0;
//     for (int b = 0; b < num_blocks; b++) {
//         memcpy(final_result + offset, d_results[b], blocks[b].nnz * sizeof(float));
//         offset += blocks[b].nnz;
//     }
//
//     // CPU计算结果
//     float *expected = new float[total_nnz];
//     for (int i = 0; i < total_nnz; ++i) {
//         int row = all_rows[i];
//         int col = all_cols[i];
//         float sum = 0.0f;
//         for (int k = 0; k < K; ++k) {
//             sum += A[row * K + k] * B[col * K + k]; // B是列优先
//         }
//         expected[i] = sum;
//     }
//
//     // 正确性检验
//     printf("\n****** 正确性检验 *******\n");
//
//     // 打印首尾各3个元素对比
//     for (int i = 0; i < 3; ++i) {
//         printf("索引 %4d: GPU=%-8.5f  CPU=%-8.5f\n",
//                i, final_result[i], expected[i]);
//     }
//     for (int i = total_nnz - 3; i < total_nnz; ++i) {
//         if (i >= 0)
//             printf("索引 %4d: GPU=%-8.5f  CPU=%-8.5f\n",
//                    i, final_result[i], expected[i]);
//     }
//
//     // 统计差异数量
//     long diff_count = 0;
//     const float epsilon = 1e-5;
//     for (int i = 0; i < total_nnz; ++i) {
//         if (fabs(final_result[i] - expected[i]) > epsilon) {
//             if (diff_count < 5) {
//                 // 打印前5个差异
//                 printf("差异索引 %d: GPU=%.6f  CPU=%.6f 差值=%.6f\n",
//                        i, final_result[i], expected[i],
//                        final_result[i] - expected[i]);
//             }
//             diff_count++;
//         }
//     }
//
//     // 输出统计结果
//     if (diff_count == 0) {
//         printf("\n结果完全一致!\n");
//     } else {
//         printf("\n发现差异: 总差异元素数量 = %ld (容差=%.0e)\n",
//                diff_count, epsilon);
//     }
//
//     // 释放内存
//     delete[] expected;
//
//
//     // 打印时间
//     printf("Time for SDDMM with K = 256: %.4f ms\n", elapsed_time);
//
//     // 释放资源
//     for (int b = 0; b < num_blocks; b++) {
//         delete[] blocks[b].h_A_block;
//         delete[] blocks[b].h_B_block;
//         delete[] blocks[b].h_local_A_rows;
//         delete[] blocks[b].h_local_B_cols;
//         delete[] blocks[b].h_unique_rows;
//         delete[] blocks[b].h_unique_cols;
//         delete[] d_results[b];
//     }
//     delete[] blocks;
//     delete[] d_results;
//     delete[] final_result;
//
//     return 0;
// }
