// #include <cuda_runtime.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
// #include <cstdint>
// #include <cassert>
// #include <thrust/device_vector.h>
// #include <thrust/scan.h>
//
// const int TILE_SIZE = 32;
// const int VECTOR_SIZE = 4;
// const int HIGH_DENSITY_THREADS = 256;
// const int BLOCK_SIZE = 32;
// const int WARP_SIZE = 32;
//
// struct CSRMatrix {
//     int *row_ptr;
//     int *col_idx;
//     float *values;
//     int rows;
//     int cols;
//     int nnz;
// };
//
// __global__ void compute_row_nnz_and_classify_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int *d_high_rows,
//     int *d_low_rows,
//     int *high_count,
//     int *low_count,
//     int *d_total_high_nnz,
//     int rows,
//     int threshold,
//     int rows_per_thread
// ) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     for (int row = tid * rows_per_thread; row < min((tid + 1) * rows_per_thread, rows); ++row) {
//         int nnz = row_ptr[row + 1] - row_ptr[row];
//         row_nnz[row] = nnz;
//         if (nnz > threshold) {
//             int pos = atomicAdd(high_count, 1);
//             if (pos < rows) d_high_rows[pos] = row;
//             atomicAdd(d_total_high_nnz, nnz);
//         } else {
//             int pos = atomicAdd(low_count, 1);
//             if (pos < rows) d_low_rows[pos] = row;
//         }
//     }
// }
//
// __global__ void fill_high_row_nnz(
//     const int *d_high_rows,
//     const int *d_row_nnz,
//     int *d_high_row_nnz,
//     int h_high_count
// ) {
//     int i = blockIdx.x * blockDim.x + threadIdx.x;
//     if (i < h_high_count) {
//         int row = d_high_rows[i];
//         d_high_row_nnz[i] = d_row_nnz[row];
//     }
// }
//
// __global__ void fill_high_nnz_list(
//     const int *d_high_rows,
//     const int *row_ptr,
//     int *d_high_rows_list,
//     int *d_high_global_idx_list,
//     const int *d_prefix_sum,
//     int h_high_count
// ) {
//     int i = blockIdx.x;
//     if (i >= h_high_count) return;
//     int row = d_high_rows[i];
//     int start = d_prefix_sum[i];
//     int row_start = row_ptr[row];
//     int nnz = row_ptr[row + 1] - row_start;
//     for (int j = threadIdx.x; j < nnz; j += blockDim.x) {
//         d_high_rows_list[start + j] = row;
//         d_high_global_idx_list[start + j] = row_start + j;
//     }
// }
//
// __global__ void sddmm_high_density_parallel_k_kernel(
//     const float *A,
//     const float *B,
//     const int *d_high_rows_list,
//     const int *d_high_global_idx_list,
//     const int *col_idx,
//     float *result,
//     int total_high_nnz,
//     int K
// ) {
//     const int WARPS_PER_BLOCK = 8;
//     int warp_id = threadIdx.x / WARP_SIZE;
//     int lane_id = threadIdx.x % WARP_SIZE;
//     int i = blockIdx.x * WARPS_PER_BLOCK + warp_id;
//     if (i >= total_high_nnz) return;
//
//     int row = d_high_rows_list[i];
//     int global_idx = d_high_global_idx_list[i];
//     int col = col_idx[global_idx];
//
//     float partial_sum = 0.0f;
//     for (int k = lane_id; k < K; k += WARP_SIZE) {
//         partial_sum += A[row * K + k] * B[col * K + k];
//     }
//
//     for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
//         partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
//     }
//
//     if (lane_id == 0) {
//         result[global_idx] = partial_sum;
//     }
// }
//
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int index = blockIdx.x * blockDim.x + threadIdx.x;
//     if (index >= num_rows) return;
//
//     int orig_row = row_indices[index];
//     const int row_start = row_ptr[orig_row];
//     const int row_end = row_ptr[orig_row + 1];
//     const int nnz_in_row = row_end - row_start;
//
//     float a_reg[128];
//     for (int k = 0; k < K; k++) {
//         a_reg[k] = A[orig_row * K + k];
//     }
//
//     for (int idx = 0; idx < nnz_in_row; idx++) {
//         int col = col_idx[row_start + idx];
//         if (col < 0 || col >= N) continue;
//
//         float sum = 0.0f;
//         int k = 0;
//
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec, b_vec;
//             memcpy(&a_vec, &a_reg[k], sizeof(float4));
//             memcpy(&b_vec, &B[col * K + k], sizeof(float4));
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y
//                     + a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//
//         for (; k < K; ++k) {
//             sum += a_reg[k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// void sddmm_csr(
//     const float *d_A,
//     const float *d_B,
//     CSRMatrix &sparse,
//     int K
// ) {
//     int *d_row_nnz, *d_high_rows, *d_low_rows;
//     int *d_high_count, *d_low_count, *d_total_high_nnz;
//     int h_high_count = 0, h_low_count = 0, h_total_high_nnz = 0;
//     int threshold = 32;
//     const int ROWS_PER_THREAD = 4;
//
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_low_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_count, sizeof(int));
//     cudaMalloc(&d_low_count, sizeof(int));
//     cudaMalloc(&d_total_high_nnz, sizeof(int));
//     cudaMemset(d_high_count, 0, sizeof(int));
//     cudaMemset(d_low_count, 0, sizeof(int));
//     cudaMemset(d_total_high_nnz, 0, sizeof(int));
//
//     cudaStream_t stream_high, stream_low;
//     cudaStreamCreate(&stream_high);
//     cudaStreamCreate(&stream_low);
//
//     cudaEvent_t step_start, step_stop;
//     cudaEventCreate(&step_start);
//     cudaEventCreate(&step_stop);
//     cudaEventRecord(step_start);
//
//     dim3 block(256);
//     dim3 grid((sparse.rows / ROWS_PER_THREAD + block.x - 1) / block.x);
//     compute_row_nnz_and_classify_kernel<<<grid, block, 0, stream_high>>>(
//         sparse.row_ptr, d_row_nnz, d_high_rows, d_low_rows,
//         d_high_count, d_low_count, d_total_high_nnz,
//         sparse.rows, threshold, ROWS_PER_THREAD
//     );
//
//     cudaEventRecord(step_stop);
//     cudaEventSynchronize(step_stop);
//     float step_ms;
//     cudaEventElapsedTime(&step_ms, step_start, step_stop);
//     printf("[步骤1] 行分类计算耗时: %.3f ms\n", step_ms);
//
//     cudaMemcpy(&h_high_count, d_high_count, sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(&h_low_count, d_low_count, sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(&h_total_high_nnz, d_total_high_nnz, sizeof(int), cudaMemcpyDeviceToHost);
//
//     cudaEvent_t step2_start, step2_stop;
//     cudaEventCreate(&step2_start);
//     cudaEventCreate(&step2_stop);
//     cudaEventRecord(step2_start);
//
//     if (h_high_count > 0) {
//         int *d_high_row_nnz;
//         cudaMalloc(&d_high_row_nnz, h_high_count * sizeof(int));
//         dim3 block_nnz(256);
//         dim3 grid_nnz((h_high_count + block_nnz.x - 1) / block_nnz.x);
//         fill_high_row_nnz<<<grid_nnz, block_nnz, 0, stream_high>>>(
//             d_high_rows, d_row_nnz, d_high_row_nnz, h_high_count
//         );
//
//         thrust::device_ptr<int> d_high_row_nnz_ptr(d_high_row_nnz);
//         thrust::device_vector<int> d_prefix_sum(h_high_count);
//         thrust::exclusive_scan(d_high_row_nnz_ptr, d_high_row_nnz_ptr + h_high_count, d_prefix_sum.begin());
//
//         int *d_high_rows_list, *d_high_global_idx_list;
//         cudaMalloc(&d_high_rows_list, h_total_high_nnz * sizeof(int));
//         cudaMalloc(&d_high_global_idx_list, h_total_high_nnz * sizeof(int));
//
//         dim3 block_list(256);
//         dim3 grid_list(h_high_count);
//         fill_high_nnz_list<<<grid_list, block_list, 0, stream_high>>>(
//             d_high_rows, sparse.row_ptr, d_high_rows_list,
//             d_high_global_idx_list, thrust::raw_pointer_cast(d_prefix_sum.data()),
//             h_high_count
//         );
//
//         const int BLOCK_SIZE_HD = 256;
//         const int WARPS_PER_BLOCK = BLOCK_SIZE_HD / WARP_SIZE;
//         dim3 block_hd(BLOCK_SIZE_HD);
//         dim3 grid_hd((h_total_high_nnz + WARPS_PER_BLOCK - 1) / WARPS_PER_BLOCK);
//         sddmm_high_density_parallel_k_kernel<<<grid_hd, block_hd, 0, stream_high>>>(
//             d_A, d_B, d_high_rows_list, d_high_global_idx_list,
//             sparse.col_idx, sparse.values, h_total_high_nnz, K
//         );
//
//         cudaFree(d_high_row_nnz);
//         cudaFree(d_high_rows_list);
//         cudaFree(d_high_global_idx_list);
//     }
//
//     cudaEventRecord(step2_stop, stream_high);
//     cudaEventSynchronize(step2_stop);
//     float step2_ms;
//     cudaEventElapsedTime(&step2_ms, step2_start, step2_stop);
//     printf("[步骤2] 高密度区计算耗时: %.3f ms\n", step2_ms);
//
//     cudaEvent_t step3_start, step3_stop;
//     cudaEventCreate(&step3_start);
//     cudaEventCreate(&step3_stop);
//     cudaEventRecord(step3_start, stream_low);
//
//     if (h_low_count > 0) {
//         dim3 block_ld(256);
//         dim3 grid_ld((h_low_count + block_ld.x - 1) / block_ld.x);
//         sddmm_low_density_kernel<<<grid_ld, block_ld, 0, stream_low>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx,
//             d_low_rows, sparse.values,
//             sparse.rows, K, sparse.cols, h_low_count
//         );
//     }
//
//     cudaEventRecord(step3_stop, stream_low);
//     cudaEventSynchronize(step3_stop);
//     float step3_ms;
//     cudaEventElapsedTime(&step3_ms, step3_start, step3_stop);
//     printf("[步骤3] 低密度区计算耗时: %.3f ms\n", step3_ms);
//
//     cudaFree(d_row_nnz);
//     cudaFree(d_high_rows);
//     cudaFree(d_low_rows);
//     cudaFree(d_high_count);
//     cudaFree(d_low_count);
//     cudaFree(d_total_high_nnz);
//     cudaStreamDestroy(stream_high);
//     cudaStreamDestroy(stream_low);
// }
//
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     while (std::getline(file, line) && line[0] == '%');
//
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value;
//         file >> row >> col;
//         if (file.peek() != '\n') {
//             file >> value;
//         }
//         rows[i] = row - 1;
//         cols[i] = col - 1;
//     }
//     file.close();
// }
//
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx,
//                 int &max_nnz_per_row) {
//     csr_row_ptr.resize(M + 1, 0);
//     max_nnz_per_row = 0;
//
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[rows[i]]++;
//     }
//
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         if (temp > max_nnz_per_row) max_nnz_per_row = temp;
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int col = cols[i];
//         int index = csr_row_ptr[row] + row_counter[row];
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// __global__ void start() {
// }
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "Usage: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr));
//     std::string filename = argv[1];
//     int K =  128;
//
//     int M, N, nnz, max_nnz_per_row = 0;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx, max_nnz_per_row);
//
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     for (int i = 0; i < M * K; ++i) h_A[i] = (rand() % 100) / 100.0f;
//     for (int i = 0; i < N * K; ++i) h_B[i] = (rand() % 100) / 100.0f;
//
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//
//     cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 预热GPU
//     std::cout << "预热GPU..." << std::endl;
//     start<<<1, 1>>>();
//
//     std::cout << "开始执行SDDMM..." << std::endl;
//
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//     cudaEventRecord(start);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     float ms;
//     cudaEventElapsedTime(&ms, start, stop);
//     printf("SDDMM计算时间: %.3f ms\n", ms);
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     std::vector<float> h_values_gpu(nnz);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     std::vector<float> h_values_cpu(nnz);
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//
//     int correct = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_cpu[i] - h_values_gpu[i]);
//         if (diff < 1e-4) correct++;
//         if (diff > max_error) max_error = diff;
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct / nnz) << "%" << std::endl;
//
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//
//     return 0;
// }
//
// // warp shuffle
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 预热GPU...
// // 开始执行SDDMM...
// // [步骤1] 行分类计算耗时: 0.035 ms
// // [步骤2] 高密度区计算耗时: 33.458 ms
// // [步骤3] 低密度区计算耗时: 1.815 ms
// // SDDMM计算时间: 35.494 ms
// // SDDMM执行完成
// // 验证结果:
// // 最大绝对误差: 3.05176e-05
// // 正确率: 100%
// //
// // 进程已结束，退出代码为 0
