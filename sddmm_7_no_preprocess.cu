//  #include <iostream>
//  #include <fstream>
//  #include <sstream>
//  #include <string>
//  #include <cstdio>
//  #include <cstdlib>
//  #include <vector>
//  #include <unordered_map>
//  #include <algorithm>
//  #include <cuda_runtime.h>
//  #include <cassert>
//
//  using namespace std;
//
//  #define CUDA_CHECK(call) \
//  do { \
//      cudaError_t err = (call); \
//      if (err != cudaSuccess) { \
//          fprintf(stderr, "CUDA error at %s:%d code=%d(%s) \"%s\"\n", \
//          __FILE__, __LINE__, err, cudaGetErrorString(err), #call); \
//          exit(EXIT_FAILURE); \
//      } \
//  } while(0)
//
//  void print_array(const float *arr, int size, const char *name) {
//      printf("%s: [", name);
//      for (int i = 0; i < size; i++) {
//          printf("%.1f%s", arr[i], (i == size - 1) ? "]\n" : ", ");
//      }
//  }
//
//  struct BlockInfo {
//      int nnz;
//      int *h_rows;
//      int *h_cols;
//      int *h_local_A_rows;
//      int *h_local_B_cols;
//      float *h_A_block;
//      float *h_B_block;
//      int num_A_rows;
//      int num_B_cols;
//      int *h_unique_rows;
//      int *h_unique_cols;
//  };
//
// __global__ void sddmm_kernel(const float *A_block, const float *B_block, const int *local_A_rows,
//                              const int *local_B_cols, float *output, int nnz, int K, int merged_A_rows,
//                              int merged_B_cols, int TILE_K) {
//     extern __shared__ float shared[];
//     float *A_tile = shared;
//     float *B_tile = &shared[merged_A_rows * TILE_K];
//
//     int tid = threadIdx.x;
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     bool valid = idx < nnz;
//
//     float sum = 0.0f;
//     int local_row = valid ? local_A_rows[idx] : 0;
//     int local_col = valid ? local_B_cols[idx] : 0;
//
//     for (int k_base = 0; k_base < K; k_base += TILE_K) {
//         int tile_size = min(TILE_K, K - k_base);
//
//         // 加载A_tile
//         for (int i = tid; i < merged_A_rows * tile_size; i += blockDim.x) {
//             int row = i / tile_size;
//             int k = i % tile_size;
//             if (row < merged_A_rows && (k_base + k) < K) {
//                 A_tile[row * tile_size + k] = A_block[row * K + (k_base + k)];
//             }
//         }
//
//         // 加载B_tile
//         for (int i = tid; i < merged_B_cols * tile_size; i += blockDim.x) {
//             int col = i / tile_size;
//             int k = i % tile_size;
//             if (col < merged_B_cols && (k_base + k) < K) {
//                 B_tile[col * tile_size + k] = B_block[col * K + (k_base + k)];
//             }
//         }
//         __syncthreads();
//
//         if (valid) {
//             // 计算点积
//             for (int k = 0; k < tile_size; k++) {
//                 int a_pos = local_row * tile_size + k;
//                 int b_pos = local_col * tile_size + k;
//
//                 if (a_pos < merged_A_rows * tile_size && b_pos < merged_B_cols * tile_size) {
//                     sum += A_tile[a_pos] * B_tile[b_pos];
//                 }
//             }
//         }
//         __syncthreads();
//     }
//
//     if (valid) {
//         output[idx] = sum;
//     }
// }
//
//  // 统计高频行列的辅助函数（返回频率前top_n的行列索引）
//  vector<int> find_high_freq_indices(const int *indices, int length, int top_n) {
//      unordered_map<int, int> freq_map;
//      for (int i = 0; i < length; i++) {
//          freq_map[indices[i]]++;
//      }
//
//      // 按频率降序排序
//      vector<pair<int, int> > sorted_freq(freq_map.begin(), freq_map.end());
//      sort(sorted_freq.begin(), sorted_freq.end(),
//           [](const pair<int, int> &a, const pair<int, int> &b) {
//               return a.second > b.second;
//           });
//
//      // 提取前top_n个索引
//      vector<int> high_freq;
//      for (int i = 0; i < min(top_n, (int) sorted_freq.size()); i++) {
//          high_freq.push_back(sorted_freq[i].first);
//      }
//      return high_freq;
//  }
//
//  float *d_high_freq_A = nullptr;
//  float *d_high_freq_B = nullptr;
//
//  void preprocess_blocks(BlockInfo *blocks, int num_blocks, const float *A, const float *B,
//                         const int *all_rows, const int *all_cols, int total_nnz, int K, int N,
//                         const vector<int> &high_freq_rows, const vector<int> &high_freq_cols) {
//      // 提前将高频行和列传输到GPU
//      int num_high_rows = high_freq_rows.size();
//      int num_high_cols = high_freq_cols.size();
//      CUDA_CHECK(cudaMalloc(&d_high_freq_A, num_high_rows * K * sizeof(float)));
//      CUDA_CHECK(cudaMalloc(&d_high_freq_B, num_high_cols * K * sizeof(float)));
//
//      for (size_t i = 0; i < num_high_rows; i++) {
//          int r = high_freq_rows[i];
//          CUDA_CHECK(cudaMemcpy(d_high_freq_A + i * K, A + r * K, K * sizeof(float), cudaMemcpyHostToDevice));
//      }
//
//      for (size_t i = 0; i < num_high_cols; i++) {
//          int c = high_freq_cols[i];
//          for (int k = 0; k < K; k++) {
//              float val = B[k * N + c];
//              CUDA_CHECK(cudaMemcpy(d_high_freq_B + i * K + k, &val, sizeof(float), cudaMemcpyHostToDevice));
//          }
//      }
//
//      int nnz_per_block = (total_nnz + num_blocks - 1) / num_blocks;
//      for (int b = 0; b < num_blocks; b++) {
//          int start = b * nnz_per_block;
//          int end = min(start + nnz_per_block, total_nnz);
//          blocks[b].nnz = end - start;
//
//          unordered_map<int, int> row_map, col_map;
//          vector<int> unique_rows, unique_cols;
//
//          for (int i = start; i < end; i++) {
//              int r = all_rows[i];
//              if (!row_map.count(r)) {
//                  row_map[r] = unique_rows.size();
//                  unique_rows.push_back(r);
//              }
//              int c = all_cols[i];
//              if (!col_map.count(c)) {
//                  col_map[c] = unique_cols.size();
//                  unique_cols.push_back(c);
//              }
//          }
//
//          blocks[b].num_A_rows = unique_rows.size();
//          blocks[b].num_B_cols = unique_cols.size();
//          blocks[b].h_A_block = new float[blocks[b].num_A_rows * K];
//          blocks[b].h_B_block = new float[blocks[b].num_B_cols * K];
//          blocks[b].h_local_A_rows = new int[end - start];
//          blocks[b].h_local_B_cols = new int[end - start];
//          blocks[b].h_rows = new int[end - start];
//          blocks[b].h_cols = new int[end - start];
//          blocks[b].h_unique_rows = new int[unique_rows.size()];
//          memcpy(blocks[b].h_unique_rows, unique_rows.data(), unique_rows.size() * sizeof(int));
//          blocks[b].h_unique_cols = new int[unique_cols.size()];
//          memcpy(blocks[b].h_unique_cols, unique_cols.data(), unique_cols.size() * sizeof(int));
//
//          for (size_t i = 0; i < unique_rows.size(); i++) {
//              int r = unique_rows[i];
//              memcpy(blocks[b].h_A_block + i * K, A + r * K, K * sizeof(float));
//          }
//          for (size_t i = 0; i < unique_cols.size(); i++) {
//              int c = unique_cols[i];
//              for (int k = 0; k < K; k++) {
//                  blocks[b].h_B_block[i * K + k] = B[k * N + c];
//              }
//          }
//
//          for (int i = start; i < end; i++) {
//              int idx = i - start;
//              blocks[b].h_rows[idx] = all_rows[i];
//              blocks[b].h_cols[idx] = all_cols[i];
//              blocks[b].h_local_A_rows[idx] = row_map[all_rows[i]];
//              blocks[b].h_local_B_cols[idx] = col_map[all_cols[i]];
//          }
//      }
//  }
//
//  void execute_sddmm(BlockInfo *blocks, int num_blocks, int K, int N, float **d_results,
//                    const vector<int> &high_freq_rows, const vector<int> &high_freq_cols,
//                    float &elapsed_time, int TILE_K) {  // 添加 TILE_K 参数
//      const int num_buffers = 2;
//      cudaStream_t streams[num_buffers];
//      cudaEvent_t events[num_buffers];
//      cudaEvent_t kernel_start, kernel_stop;
//      CUDA_CHECK(cudaEventCreate(&kernel_start));
//      CUDA_CHECK(cudaEventCreate(&kernel_stop));
//      float kernel_time = 0.0f;
//
//      for (int i = 0; i < num_buffers; i++) {
//          CUDA_CHECK(cudaStreamCreate(&streams[i]));
//          CUDA_CHECK(cudaEventCreate(&events[i]));
//      }
//
//      struct DeviceBuffers {
//          float *A_block;
//          float *B_block;
//          int *local_A_rows;
//          int *local_B_cols;
//          float *output;
//      } d_buf[num_buffers];
//
//      for (int i = 0; i < num_buffers; i++) {
//          d_buf[i].A_block = nullptr;
//          d_buf[i].B_block = nullptr;
//          d_buf[i].local_A_rows = nullptr;
//          d_buf[i].local_B_cols = nullptr;
//          d_buf[i].output = nullptr;
//      }
//
//      unordered_map<int, int> row_idx_map;
//      for (size_t i = 0; i < high_freq_rows.size(); i++) {
//          row_idx_map[high_freq_rows[i]] = i;
//      }
//      unordered_map<int, int> col_idx_map;
//      for (size_t i = 0; i < high_freq_cols.size(); i++) {
//          col_idx_map[high_freq_cols[i]] = i;
//      }
//
//      int current = 0;
//      for (int b = 0; b < num_blocks; b++) {
//          int next = (current + 1) % num_buffers;
//
//          // 释放前一个块的设备内存
//          if (b > 0) {
//              if (d_buf[current].A_block) {
//                  CUDA_CHECK(cudaFree(d_buf[current].A_block));
//                  d_buf[current].A_block = nullptr;
//              }
//              if (d_buf[current].B_block) {
//                  CUDA_CHECK(cudaFree(d_buf[current].B_block));
//                  d_buf[current].B_block = nullptr;
//              }
//              if (d_buf[current].local_A_rows) {
//                  CUDA_CHECK(cudaFree(d_buf[current].local_A_rows));
//                  d_buf[current].local_A_rows = nullptr;
//              }
//              if (d_buf[current].local_B_cols) {
//                  CUDA_CHECK(cudaFree(d_buf[current].local_B_cols));
//                  d_buf[current].local_B_cols = nullptr;
//              }
//              if (d_buf[current].output) {
//                  CUDA_CHECK(cudaFree(d_buf[current].output));
//                  d_buf[current].output = nullptr;
//              }
//          }
//
//          // 为当前块分配适当的内存
//          CUDA_CHECK(cudaMalloc(&d_buf[current].A_block, blocks[b].num_A_rows * K * sizeof(float)));
//          CUDA_CHECK(cudaMalloc(&d_buf[current].B_block, blocks[b].num_B_cols * K * sizeof(float)));
//          CUDA_CHECK(cudaMalloc(&d_buf[current].local_A_rows, blocks[b].nnz * sizeof(int)));
//          CUDA_CHECK(cudaMalloc(&d_buf[current].local_B_cols, blocks[b].nnz * sizeof(int)));
//          CUDA_CHECK(cudaMalloc(&d_buf[current].output, blocks[b].nnz * sizeof(float)));
//
//          // 传输A块数据：高频行使用设备内存，普通行使用主机传输
//          for (size_t i = 0; i < blocks[b].num_A_rows; i++) {
//              int r = blocks[b].h_unique_rows[i]; // 使用全局行索引
//              if (row_idx_map.count(r)) {
//                  CUDA_CHECK(cudaMemcpyAsync(d_buf[current].A_block + i * K,
//                      d_high_freq_A + row_idx_map[r] * K, K * sizeof(float), cudaMemcpyDeviceToDevice, streams[current]));
//              } else {
//                  CUDA_CHECK(cudaMemcpyAsync(d_buf[current].A_block + i * K,
//                      blocks[b].h_A_block + i * K, K * sizeof(float), cudaMemcpyHostToDevice, streams[current]));
//              }
//          }
//
//          // 传输B块数据：高频列使用设备内存，普通列使用主机传输
//          for (size_t i = 0; i < blocks[b].num_B_cols; i++) {
//              int c = blocks[b].h_unique_cols[i]; // 使用全局列索引
//              if (col_idx_map.count(c)) {
//                  CUDA_CHECK(cudaMemcpyAsync(d_buf[current].B_block + i * K,
//                      d_high_freq_B + col_idx_map[c] * K, K * sizeof(float), cudaMemcpyDeviceToDevice, streams[current]));
//              } else {
//                  CUDA_CHECK(cudaMemcpyAsync(d_buf[current].B_block + i * K,
//                      blocks[b].h_B_block + i * K, K * sizeof(float), cudaMemcpyHostToDevice, streams[current]));
//              }
//          }
//
//          CUDA_CHECK(cudaMemcpyAsync(d_buf[current].local_A_rows, blocks[b].h_local_A_rows,
//              blocks[b].nnz * sizeof(int), cudaMemcpyHostToDevice, streams[current]));
//          CUDA_CHECK(cudaMemcpyAsync(d_buf[current].local_B_cols, blocks[b].h_local_B_cols,
//              blocks[b].nnz * sizeof(int), cudaMemcpyHostToDevice, streams[current]));
//          CUDA_CHECK(cudaEventRecord(events[current], streams[current]));
//
//          CUDA_CHECK(cudaStreamWaitEvent(streams[current], events[current], 0));
//
//          dim3 block(256);
//          dim3 grid((blocks[b].nnz + block.x - 1) / block.x);
//
//
//          CUDA_CHECK(cudaEventRecord(kernel_start, streams[current]));
//
//          size_t shared_mem_size = (blocks[b].num_A_rows * TILE_K + blocks[b].num_B_cols * TILE_K) * sizeof(float);
//          sddmm_kernel<<<grid, block, shared_mem_size, streams[current]>>>(
//              d_buf[current].A_block, d_buf[current].B_block,
//              d_buf[current].local_A_rows, d_buf[current].local_B_cols,
//              d_buf[current].output, blocks[b].nnz, K,
//              blocks[b].num_A_rows,
//              blocks[b].num_B_cols,
//              TILE_K);
//
//          CUDA_CHECK(cudaEventRecord(kernel_stop, streams[current]));
//          float temp_time;
//          CUDA_CHECK(cudaEventSynchronize(kernel_stop));
//          CUDA_CHECK(cudaEventElapsedTime(&temp_time, kernel_start, kernel_stop));
//          kernel_time += temp_time;
//
//          CUDA_CHECK(cudaGetLastError());
//          CUDA_CHECK(cudaMemcpyAsync(d_results[b], d_buf[current].output,
//              blocks[b].nnz * sizeof(float), cudaMemcpyDeviceToHost, streams[current]));
//
//          if (b + 1 < num_blocks) {
//              // 为下一个块分配内存
//              CUDA_CHECK(cudaMalloc(&d_buf[next].A_block, blocks[b + 1].num_A_rows * K * sizeof(float)));
//              CUDA_CHECK(cudaMalloc(&d_buf[next].B_block, blocks[b + 1].num_B_cols * K * sizeof(float)));
//              CUDA_CHECK(cudaMalloc(&d_buf[next].local_A_rows, blocks[b + 1].nnz * sizeof(int)));
//              CUDA_CHECK(cudaMalloc(&d_buf[next].local_B_cols, blocks[b + 1].nnz * sizeof(int)));
//              CUDA_CHECK(cudaMalloc(&d_buf[next].output, blocks[b + 1].nnz * sizeof(float)));
//
//              // 检查是否是高频行和列，如果是则使用提前传输到 GPU 的数据
//              for (size_t i = 0; i < blocks[b + 1].num_A_rows; i++) {
//                  int r = blocks[b + 1].h_local_A_rows[i];
//                  if (row_idx_map.count(r)) {
//                      CUDA_CHECK(cudaMemcpyAsync(d_buf[next].A_block + i * K, d_high_freq_A + row_idx_map[r] * K,
//                          K * sizeof(float), cudaMemcpyDeviceToDevice, streams[next]));
//                  } else {
//                      CUDA_CHECK(cudaMemcpyAsync(d_buf[next].A_block + i * K, blocks[b + 1].h_A_block + i * K,
//                          K * sizeof(float), cudaMemcpyHostToDevice, streams[next]));
//                  }
//              }
//              for (size_t i = 0; i < blocks[b + 1].num_B_cols; i++) {
//                  int c = blocks[b + 1].h_local_B_cols[i];
//                  if (col_idx_map.count(c)) {
//                      CUDA_CHECK(cudaMemcpyAsync(d_buf[next].B_block + i * K, d_high_freq_B + col_idx_map[c] * K,
//                          K * sizeof(float), cudaMemcpyDeviceToDevice, streams[next]));
//                  } else {
//                      CUDA_CHECK(cudaMemcpyAsync(d_buf[next].B_block + i * K, blocks[b + 1].h_B_block + i * K,
//                          K * sizeof(float), cudaMemcpyHostToDevice, streams[next]));
//                  }
//              }
//              CUDA_CHECK(cudaMemcpyAsync(d_buf[next].local_A_rows, blocks[b + 1].h_local_A_rows,
//                  blocks[b + 1].nnz * sizeof(int), cudaMemcpyHostToDevice, streams[next]));
//              CUDA_CHECK(cudaMemcpyAsync(d_buf[next].local_B_cols, blocks[b + 1].h_local_B_cols,
//                  blocks[b + 1].nnz * sizeof(int), cudaMemcpyHostToDevice, streams[next]));
//              CUDA_CHECK(cudaEventRecord(events[next], streams[next]));
//          }
//
//          current = next;
//      }
//
//      // 释放最后一个块的设备内存
//      if (d_buf[current].A_block) {
//          CUDA_CHECK(cudaFree(d_buf[current].A_block));
//          d_buf[current].A_block = nullptr;
//      }
//      if (d_buf[current].B_block) {
//          CUDA_CHECK(cudaFree(d_buf[current].B_block));
//          d_buf[current].B_block = nullptr;
//      }
//      if (d_buf[current].local_A_rows) {
//          CUDA_CHECK(cudaFree(d_buf[current].local_A_rows));
//          d_buf[current].local_A_rows = nullptr;
//      }
//      if (d_buf[current].local_B_cols) {
//          CUDA_CHECK(cudaFree(d_buf[current].local_B_cols));
//          d_buf[current].local_B_cols = nullptr;
//      }
//      if (d_buf[current].output) {
//          CUDA_CHECK(cudaFree(d_buf[current].output));
//          d_buf[current].output = nullptr;
//      }
//
//      // 释放提前传输到 GPU 的高频数据
//      if (d_high_freq_A) {
//          CUDA_CHECK(cudaFree(d_high_freq_A));
//      }
//      if (d_high_freq_B) {
//          CUDA_CHECK(cudaFree(d_high_freq_B));
//      }
//
//      // CUDA_CHECK(cudaEventRecord(stop));
//      // CUDA_CHECK(cudaEventSynchronize(stop));
//      // CUDA_CHECK(cudaEventElapsedTime(&elapsed_time, start, stop));
//      elapsed_time = kernel_time; // 将总耗时替换为纯核函数时间
//
//      CUDA_CHECK(cudaDeviceSynchronize());
//      for (int i = 0; i < num_buffers; i++) {
//          CUDA_CHECK(cudaStreamDestroy(streams[i]));
//          CUDA_CHECK(cudaEventDestroy(events[i]));
//      }
//      CUDA_CHECK(cudaEventDestroy(kernel_start));
//      CUDA_CHECK(cudaEventDestroy(kernel_stop));
//  }
//
//  void initial(float *X, long n, int k) {
//      //n是行数，k是列数，drand48()生成一个[0,1)之间的随机数
//      srand48(0L);
//      for (long r = 0; r < n; ++r) {
//          for (long t = 0; t < k; ++t)
//              X[r * k + t] = 0.1 * drand48(); //-1;
//      }
//  }
//
// vector<int> adaptive_topn_selection(const int *indices, int total_nnz, float coverage_threshold = 0.8) {
//     unordered_map<int, int> freq_map;
//     for (int i = 0; i < total_nnz; i++) freq_map[indices[i]]++;
//
//     vector<pair<int, int> > sorted(freq_map.begin(), freq_map.end());
//     // 修改 lambda 参数类型为具体类型
//     sort(sorted.begin(), sorted.end(), [](const pair<int, int> &a, const pair<int, int> &b) {
//         return a.second > b.second;
//     });
//
//     int total = total_nnz, current = 0;
//     vector<int> selected;
//     for (auto &p: sorted) {
//         current += p.second;
//         selected.push_back(p.first);
//         if ((float) current / total >= coverage_threshold) break;
//     }
//     return selected;
// }
//
//  // 修改 main 函数
//  int main(int argc, char *argv[]) {
//      // 添加 TILE_K 参数读取
//      const int TILE_K = 16;  // 新增第五个参数
//
//      ifstream fp(argv[1]);
//      const int K = atoi(argv[2]); // 稠密矩阵维度
//      const int tile_sizeX = atoi(argv[3]); // 行分块大小
//      const int tile_sizeY = atoi(argv[4]); // 列分块大小
//
//      // 读取矩阵头信息
//      string str;
//      while (getline(fp, str) && !isdigit(str[0])) {
//      } // 跳过注释行
//
//      int M, N, total_nnz;
//      istringstream header(str);
//      header >> M >> N >> total_nnz;
//
//      float *A = new float[M * K];
//      initial(A, M, K);
//      float *B = new float[K * N];
//      initial(B, K, N);
//
//
//      // 读取非零元素数据
//      int *all_rows = new int[total_nnz];
//      int *all_cols = new int[total_nnz];
//
//      for (int idx = 0; idx < total_nnz; ++idx) {
//          int rid, cid;
//          float val;
//          fp >> rid >> cid;
//          all_rows[idx] = rid - 1;
//          all_cols[idx] = cid - 1;
//      }
//
//      // 高频行列统计参数
//      const int top_n = 2;
//      const int num_blocks = (total_nnz + tile_sizeX - 1) / tile_sizeX;
//      printf("Matrix info: rows=%d, cols=%d, nnz=%d\n", M, N, total_nnz);
//      printf("number of blocks: %d\n", num_blocks);
//
//      BlockInfo *blocks = new BlockInfo[num_blocks];
//      float **d_results = new float *[num_blocks];
//      for (int b = 0; b < num_blocks; b++) {
//          d_results[b] = new float[total_nnz];
//      }
//
//      // 统计高频行列
//      // vector<int> high_freq_rows = find_high_freq_indices(all_rows, total_nnz, top_n);
//      // vector<int> high_freq_cols = find_high_freq_indices(all_cols, total_nnz, top_n);
//
//      vector<int> high_freq_rows = adaptive_topn_selection(all_rows, total_nnz, 0.8);
//      vector<int> high_freq_cols = adaptive_topn_selection(all_cols, total_nnz, 0.8);
//
//      float tot_ms;
//      cudaEvent_t event1,event2;
//      cudaEventCreate(&event1);
//      cudaEventCreate(&event2);
//
//      cudaDeviceSynchronize();
//      cudaEventRecord(event1,0);
//
//      preprocess_blocks(blocks, num_blocks, A, B, all_rows, all_cols, total_nnz, K, N,
//                        high_freq_rows, high_freq_cols);
//
//      cudaEventRecord(event2,0);
//
//      cudaEventSynchronize(event1);
//      cudaEventSynchronize(event2);
//      cudaEventElapsedTime(&tot_ms, event1, event2);
//      cudaDeviceSynchronize();
//
//      printf("preprocess time: %f ms\n", tot_ms);
//      float elapsed_time;
//      // 修改函数调用
//      execute_sddmm(blocks, num_blocks, K, N, d_results, high_freq_rows, high_freq_cols,
//                   elapsed_time, TILE_K);  // 添加 TILE_K 参数
//
//      // 同步等待所有计算完成
//      CUDA_CHECK(cudaDeviceSynchronize());
//
//      // 合并所有块的结果
//      float *final_result = new float[total_nnz];
//      int offset = 0;
//      for (int b = 0; b < num_blocks; b++) {
//          memcpy(final_result + offset, d_results[b], blocks[b].nnz * sizeof(float));
//          offset += blocks[b].nnz;
//      }
//
//      // 生成CPU预期结果
//      float *expected = new float[total_nnz];
//      for (int i = 0; i < total_nnz; ++i) {
//          int row = all_rows[i];
//          int col = all_cols[i];
//          float sum = 0.0f;
//          for (int k = 0; k < K; ++k) {
//              sum += A[row * K + k] * B[k * N + col];
//          }
//          expected[i] = sum;
//      }
//
//      // 正确性验证
//      printf("\n****** 正确性检验 *******\n");
//
//      // 打印首尾各3个元素对比
//      for (int i = 0; i < 3; ++i) {
//          printf("索引 %4d: GPU=%-8.5f  CPU=%-8.5f\n",
//                 i, final_result[i], expected[i]);
//      }
//      for (int i = total_nnz - 3; i < total_nnz; ++i) {
//          if (i >= 0)
//              printf("索引 %4d: GPU=%-8.5f  CPU=%-8.5f\n",
//                     i, final_result[i], expected[i]);
//      }
//
//      // 统计差异数量
//      long diff_count = 0;
//      const float epsilon = 1e-5;
//      for (int i = 0; i < total_nnz; ++i) {
//          if (fabs(final_result[i] - expected[i]) > epsilon) {
//              if (diff_count < 5) {
//                  // 打印前5个差异
//                  printf("差异索引 %d: GPU=%.6f  CPU=%.6f 差值=%.6f\n",
//                         i, final_result[i], expected[i],
//                         final_result[i] - expected[i]);
//              }
//              diff_count++;
//          }
//      }
//
//      // 输出统计结果
//      if (diff_count == 0) {
//          printf("\n结果完全一致!\n");
//      } else {
//          printf("\n发现差异: 总差异元素数量 = %ld (容差=%.0e)\n",
//                 diff_count, epsilon);
//      }
//
//      // 释放内存
//      delete[] expected;
//
//
//      // 打印时间
//      printf("Time for SDDMM with K = 256: %.4f ms\n", elapsed_time);
//
//      // 释放资源
//      for (int b = 0; b < num_blocks; b++) {
//          delete[] blocks[b].h_A_block;
//          delete[] blocks[b].h_B_block;
//          delete[] blocks[b].h_local_A_rows;
//          delete[] blocks[b].h_local_B_cols;
//          delete[] blocks[b].h_rows;
//          delete[] blocks[b].h_cols;
//          delete[] blocks[b].h_unique_rows;
//          delete[] blocks[b].h_unique_cols;
//          delete[] d_results[b];
//      }
//      delete[] blocks;
//      delete[] d_results;
//      delete[] final_result;
//
//      return 0;
//  }
