// #include <iostream>
// #include <fstream>
// #include <stdio.h>
// #include <vector>
// #include <algorithm>
// #include <iterator>
// #include <utility>
// #include <math.h>
// #include <omp.h>
// #include <cuda.h>
// #include "util.h"
// //#include "kernel.h"
// #include <bits/stdc++.h>
//
// using namespace std;
//
// // long n_rows, n_cols, nnz;
//
// int tile_sizeX = 256;
// int tile_sizeY = 25000;
// int k = 100;
//
//
// inline cudaError_t checkCuda(cudaError_t result, int s) {
//     if (result != cudaSuccess) {
//         fprintf(stderr, "CUDA Runtime Error in line : %s - %d\n", cudaGetErrorString(result), s);
//         assert(result == cudaSuccess);
//     }
//     return result;
// }
//
// __global__ void comp_kernel_COO(int const *__restrict__ row_ind, int const *__restrict__ col_ind, float *val,
//                                 const float *__restrict__ u, const float *__restrict__ v, int nnz, int n_rows,
//                                 int n_cols, int k,
//                                 int tile_stIdx, int tile_limIdx, int *d_last_blockIdx, int *active_row, int tile_no,
//                                 int t_st, int act_rlimit, int sh_tile, int k_slc) {
//     unsigned int tId = threadIdx.x;  //当前线程索引
//     unsigned int laneId = tId & 1;   //当前线程在其所在warp内的索引
//     unsigned int c = (blockIdx.x * blockDim.x + tId);  //全局索引，确定当前线程应该处理哪个非零元素
//     int block_st = 0;  //初始化当前块的起始索引
//     if (blockIdx.x == 0) block_st = tile_stIdx;  // 如果是第一个块，起始索引是tile_stIdx，否则是前一个块的终止索引
//     else block_st = d_last_blockIdx[blockIdx.x - 1];
//     int block_lim = d_last_blockIdx[blockIdx.x];  //获取当前块的终止索引
//
//     __shared__ float sh_r[32 * 192];  //声明共享内存数组，用于存储每个线程块处理的行向量
//     int WARP_ID = tId >> 5;  //计算当前线程所在的warp的ID，比如说tId=63，那么就在第1号warp里面
//     int tid_in_WARP = tId & 31;  //计算当前线程在warp的索引，比如说tID=63，那么就是第1号warp里面的第31号线程
//     int WARP_SIZE = 32;  //warp的大小
//
//     int step = blockDim.x >> 5;  //每个warp处理的元素步长，即线程块的总数除以32
//
//     int t = tid_in_WARP;  //初始化线程在warp内的索引
//
//     //将输入向量u的相关部分加载到共享内存sh_r中
//     //每个线程块加载sh_tile行的数据，act_rlimit是总的活动行数
//     //step是每个warp处理的元素步长
//     for (int i = WARP_ID; i < sh_tile && (blockIdx.x * sh_tile + i) < act_rlimit; i += step) {
//         for (int w_r = 0; w_r < k_slc; w_r += WARP_SIZE)
//             sh_r[i * k_slc + t + w_r] = u[active_row[blockIdx.x * sh_tile + i] * k + t + t_st + w_r];
//     }
//
//     __syncthreads();  //确保所有线程都完成了共享内存的写操作
//
//     //对每个非零元素进行计算
//     for (int c = block_st + (tId >> 1); c < block_lim; c += (blockDim.x >> 1)) {
//         float sm = 0, g = 0, sm1 = 0, sm2 = 0;  //初始化累加和
//         int row = row_ind[c];  //获取当前非零元素的行和列索引
//         int col = col_ind[c];
//         int sh_row = row - blockIdx.x * sh_tile;  //计算当前行在共享内存中的索引
//
//         //for (int t = laneId*16; t < (laneId+1)*16; t+=8){
//         for (int t = laneId * k_slc / 2; t < (laneId + 1) * k_slc / 2; t += 8) {
//             float4 rtmp1 = *((float4 *) &sh_r[sh_row * k_slc + t]);
//             float4 ctmp1 = *((float4 *) &v[col * k + t_st + t]);
//             sm1 += rtmp1.x * ctmp1.x + rtmp1.y * ctmp1.y + rtmp1.z * ctmp1.z + rtmp1.w * ctmp1.w;
//
//             float4 rtmp2 = *((float4 *) &sh_r[sh_row * k_slc + t + 4]);
//             float4 ctmp2 = *((float4 *) &v[col * k + t_st + t + 4]);
//             sm2 += rtmp2.x * ctmp2.x + rtmp2.y * ctmp2.y + rtmp2.z * ctmp2.z + rtmp2.w * ctmp2.w;
//         }
//         //在warp内进行跨线程的规约操作，计算最终的部分和
//         sm1 += __shfl_xor(sm1, 1);
//         sm2 += __shfl_xor(sm2, 1);
//         val[c] = sm1 + sm2;
//         //val[c] += (sm1 + sm2);
//
//     }
//     __syncthreads();
// }
//
// void sddmm_GPU(const Matrix S, const TiledMatrix tS, float *P, vector<float> W, vector<float> H) {
//     // W和H表示两个稠密矩阵，S是稀疏矩阵，tS表示分块后的稀疏矩阵，P是存储CPU计算结果的数组
//     float *d_val, *d_W, *d_H, *d_W_t;
//     int *d_row_ptr, *d_col_ind, *d_row_ind, *d_tiled_ind, *d_lastIdx, *d_active_row, *d_lastIdx_block_tile, *
//             d_passive_row;
//
//     // 添加时间测量事件
//     cudaEvent_t h2d_start, h2d_stop;
//     float h2d_time = 0;
//     cudaEventCreate(&h2d_start);
//     cudaEventCreate(&h2d_stop);
//
//     //***********Starting GPU****************
//     checkCuda(cudaMalloc((void **) &d_W, k * S.n_rows * sizeof(float)), 0);
//     checkCuda(cudaMalloc((void **) &d_H, k * S.n_cols * sizeof(float)), 1);
//
//     // 记录H2D传输开始
//     cudaEventRecord(h2d_start);
//
//     // checkCuda(cudaMalloc((void**)&d_row_ptr, (n_rows+1) * sizeof (int)),2);
//     checkCuda(cudaMalloc((void **) &d_row_ind, tS.nnz * sizeof(int)), 4);
//     checkCuda(cudaMalloc((void **) &d_col_ind, tS.nnz * sizeof(int)), 4);
//     checkCuda(cudaMalloc((void **) &d_val, tS.nnz * sizeof(float)), 4);
//     checkCuda(cudaMalloc((void **) &d_lastIdx, (tS.ntile_c + 1) * sizeof(float)), 4);
//     checkCuda(cudaMalloc((void **) &d_active_row, tS.ntile_c * tS.max_active_row * sizeof(int)), 4);
//     checkCuda(cudaMalloc((void **) &d_lastIdx_block_tile, tS.ntile_c * tS.max_active_block * sizeof(int)), 4);
//
//
//     // checkCuda(cudaMemcpy(d_row_ptr,  &(row_ptr[0]), (n_rows+1) * sizeof (int), cudaMemcpyHostToDevice),4);
//     checkCuda(cudaMemcpy(d_row_ind, &(tS.rows[0]), tS.nnz * sizeof(int), cudaMemcpyHostToDevice), 4);
//     checkCuda(cudaMemcpy(d_col_ind, &(tS.cols[0]), tS.nnz * sizeof(int), cudaMemcpyHostToDevice), 4);
//     //checkCuda(cudaMemcpy(d_val, &(new_vals[0]), tS.nnz * sizeof (float), cudaMemcpyHostToDevice),4);
//     cudaMemset(d_val, 0, S.nnz * sizeof(float));
//     checkCuda(cudaMemcpy(d_lastIdx, &(tS.lastIdx_tile[0]), (tS.ntile_c + 1) * sizeof(int), cudaMemcpyHostToDevice), 4);
//     for (int i = 0; i < tS.ntile_c; ++i) {
//         checkCuda(cudaMemcpy(d_lastIdx_block_tile + i * tS.max_active_block,
//                              &(tS.lastIdx_block_tile[i * tS.max_active_block]), tS.max_active_block * sizeof(int),
//                              cudaMemcpyHostToDevice), 4);
//         //cout <<i<<" "<< tS.lastIdx_tile[i]<<" "<<tS.lastIdx_block_tile[i*tS.max_active_block]<< endl;
//     }
//
//     int sum = 0; //sum用于累加每个分块的活动行数
//     for (int i = 0; i < tS.ntile_c; ++i) {
//         checkCuda(cudaMemcpy(d_active_row + sum, &(tS.active_row[i * S.n_rows]), tS.n_actv_row[i] * sizeof(int),
//                              cudaMemcpyHostToDevice), 4);
//         sum += tS.n_actv_row[i];
//     }
//     // sum=0;
//     // for (int i = 0; i < tS.ntile_c; ++i){
//     //     checkCuda(cudaMemcpy(d_passive_row+sum, &(passive_row[i*S.n_rows]), S.n_rows * sizeof (int), cudaMemcpyHostToDevice),4);
//     //     sum += S.n_rows;
//     // }
//
//     cudaMemcpy(d_W, &(W[0]), S.n_rows * k * sizeof(float), cudaMemcpyHostToDevice);
//     //cudaMemcpy(d_W, &(W_t[0]),  S.n_rows * k  * sizeof (float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_H, &(H[0]), S.n_cols * k * sizeof(float), cudaMemcpyHostToDevice);
//     //cudaMemcpy(d_H, &(H_t[0]),  S.n_cols * k  * sizeof (float), cudaMemcpyHostToDevice);
//
//     // 记录H2D传输结束并计算时间
//     cudaEventRecord(h2d_stop);
//     cudaEventSynchronize(h2d_stop);
//     cudaEventElapsedTime(&h2d_time, h2d_start, h2d_stop);
//
//
//     int n_tile = tS.ntile_c; //S.n_cols/tile_sizeX + 1;
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//     cudaStream_t stream[n_tile];
//     for (int i = 0; i < n_tile; i++) {
//         cudaStreamCreate(&(stream[i]));
//     }
//
//     float mili = 0, copyTime = 0;
//
//     dim3 block(BLOCKSIZE, 1, 1), grid(1, 1, 1); //每个线程块的大小为BLOCKSIZE个线程，每个网格包含1个线程块
//     sum = 0;
//     int t_st = 0;
//
//     int k_slice = SM_CAPACITY / actv_row_size; //k_slice 表示每个线程块最多能处理的行数，SM_CAPACITY表示共享内存的大小
//
//     checkCuda(cudaEventRecord(start), __LINE__);
//
//
//     for (int tile = 0; tile < n_tile; ++tile) {
//         //把稀疏矩阵分块，tile用于遍历每一个分块，n_tile是这个稀疏矩阵被分的块数
//         //nnz_tile 表示当前分块中的非零元素数量。
//         //tS.lastIdx_tile 是一个数组，记录每个分块的起始和结束位置。
//         //tS.lastIdx_tile[tile] 是当前分块的起始位置。
//         //tS.lastIdx_tile[tile + 1] 是下一个分块的起始位置，也就是当前分块的结束位置。
//         //两个相减就是这个分块的非零元素的数量
//         int nnz_tile = tS.lastIdx_tile[tile + 1] - tS.lastIdx_tile[tile];
//         //grid.x = (nnz_tile + BLOCKSIZE - 1) / BLOCKSIZE;
//         int active_block_this_tile = tS.n_actv_row[tile] / actv_row_size + 1;
//
//         grid.x = active_block_this_tile; //每个网格包含active_block_this_tile个线程块
//         for (int t_st = 0; t_st < k; t_st += k_slice) {
//             //k表示稠密矩阵的列数，k_slice表示每次计算的列数，k_slice=SM_CAPACITY/actv_row_size
//             comp_kernel_COO<<<grid, block, 0, stream[0]>>>(d_row_ind, d_col_ind, d_val, d_W, d_H,
//                                                            S.nnz, S.n_rows, S.n_cols, k, tS.lastIdx_tile[tile],
//                                                            tS.lastIdx_tile[tile + 1],
//                                                            &(d_lastIdx_block_tile[(tile) * tS.max_active_block]),
//                                                            d_active_row + sum, tile, t_st, tS.n_actv_row[tile],
//                                                            actv_row_size, k_slice);
//         }
//         sum += tS.n_actv_row[tile]; // 累加每个分块的活动行数，ts.n_actv_row[tile]是当前分块的活动行数
//     }
//     checkCuda(cudaEventRecord(stop), __LINE__);
//     cudaEventSynchronize(stop);
//     //cudaDeviceSynchronize();
//     checkCuda(cudaEventElapsedTime(&mili, start, stop), __LINE__);
//     cudaDeviceSynchronize();
//     // 在原有时间输出后添加传输时间显示
//     cout << "\nTime for SDDMM with K = " << k << " : " << mili << " ms" << endl;
//     cout << "Host->Device 传输时间: " << h2d_time << " ms" << endl;
//     cout << "每个非零元素平均传输量: "
//          << (tS.nnz * sizeof(int)*2 + S.n_rows*k*sizeof(float) + S.n_cols*k*sizeof(float)) / (tS.nnz * 1e6f)
//          << " MB/nnz" << endl;
// }
//
// //void sddmm_CPU_CSR(int *row_ptr, int *col_ind, float *val_ind, vector<float> W, vector<float> H, float *p_ind,
// //                   const Matrix S) {
// //    // reduction(+:rmse)
// //    long tot = 0;
// //#pragma omp parallel for reduction(+:tot)
// //    for (int r = 0; r < S.n_rows; ++r) {
// //        tot += row_ptr[r + 1] - row_ptr[r];
// //        float sm = 0;
// //        for (int ind = row_ptr[r]; ind < row_ptr[r + 1]; ++ind) {
// //            int row = r;
// //            int col = col_ind[ind];
// //            int nnz = row_ptr[r + 1] - row_ptr[r];
// //
// //            float val = val_ind[ind];
// //            sm = 0;
// //            for (int t = 0; t < k; ++t) {
// //                sm += W[row * k + t] * H[col * k + t];
// //                // cout <<W[row * k + t] <<" "<<H[col * k + t]<< endl;
// //            }
// //            p_ind[ind] = sm * val_ind[ind];
// //            // cout << "ind " << row<<" "<<col << ":: "  <<" "<< p_ind[ind] << " = " << sm <<" * "<< val_ind[ind]<< endl;
// //
// //
// //        }
// //    }
// //}
// // vector<int> rows, vector<int> cols, vector<float> vals
//
// //void sddmm_CPU_COO(vector<int> row_ind, vector<int> col_ind, vector<float> val_ind, vector<float> W, vector<float> H,
// //                   float *p_ind, const Matrix S) {
// //    // reduction(+:rmse)
// //    double start_time = omp_get_wtime();
// //    omp_set_dynamic(0);
// //    omp_set_num_threads(28);
// //#pragma omp parallel for //reduction(+:tot)
// //    for (int ind = 0; ind < S.nnz; ind++) {
// //        float sm = 0;
// //        int row = row_ind[ind];
// //        int col = col_ind[ind];
// //        for (int t = 0; t < k; ++t)
// //            sm += W[row * k + t] * H[col * k + t];
// //        p_ind[ind] = sm;//* val_ind[ind];
// //        // cout << "ind " << row<<" "<<col << ":: "  <<" "<< p_ind[ind] << " = " << sm <<" * "<< val_ind[ind]<< endl;
// //        // }
// //    }
// //    double CPU_time = omp_get_wtime() - start_time;
// //    //correctness check
// //
// //    // printf("\nomp time CPU : %.4f \n\n", CPU_time*1000);
// //}
//
//
// void preprocessing(const Matrix S) {
//     int *row_ptr = new int[S.n_rows + 1]; //稀疏矩阵的行指针
//     float *P = new float[S.nnz]; // output Matrix
//
//     TiledMatrix tiledS(S, tile_sizeX, tile_sizeY); //将稀疏矩阵分块
//     int ntile_c = tiledS.ntile_c;
//     int ntile_r = tiledS.ntile_r;
//
//     /* Populate rhs dense matrices */
//     vector<float> W(S.n_rows * k); //1500*256
//     vector<float> W_t(S.n_rows * k); //转置
//     vector<float> H(S.n_cols * k); //12419*256
//     vector<float> H_t(S.n_cols * k); //转置
//
//     initial(W, S.n_rows, k);
//     initial(H, S.n_cols, k);
//     make_HTasH(H, H_t, S.n_cols, k); //构造转置矩阵
//     make_HTasH(W, W_t, S.n_rows, k);
//
//     int *row_holder = new int[S.n_rows];
//
//     tiledS.nnz = 0;
//
//     /*converting col sorted matrix to row sorted */
//     //unsorted_make_CSR(rows, cols, vals, S.nnz, S.n_rows, S.n_cols, row_ptr);
//
//     //assuming sorted
//     make_CSR(S.rows, S.cols, S.vals, S.nnz, S.n_rows, row_ptr, row_holder);
//
//     tiledS.max_active_row = rewrite_matrix_1D(S, tiledS, row_ptr, tile_sizeX, row_holder);
//
//     // rewrite_col_sorted_matrix(row_ptr, rows, cols, vals, tS.rows, tS.cols, new_vals, S.nnz,
//     //S.n_rows, S.n_cols, tile_sizeX, tiled_ind, lastIdx_tile, BLOCKSIZE, tS.nnz);
//
//     /* CPU call */
//     // sddmm_CPU_CSR(row_ptr, cols, vals, W, H, P);
//     // sddmm_CPU_COO(S.rows, S.cols, S.vals, W, H, P, S);
//
//     /* GPU call */
//     sddmm_GPU(S, tiledS, P, W, H);
// }
//
// int main(int argc, char *argv[]) {
//     Matrix S; //1500*12419，非零元素的数量为746316
//
//     ifstream fp(argv[1]);
//     k = atoi(argv[2]); //稠密矩阵的列数
//     tile_sizeY = atoi(argv[3]); //矩阵在行方向上的分块大小，192
//     tile_sizeX = atoi(argv[4]); //矩阵在列方向上的分块大小，50000
//     actv_row_size = tile_sizeY; //每个分块中的最大活动行数
//
//     string str;
//     fp >> str;
//     while (!isdigit(str[0])) {
//         getline(fp, str);
//     }
//
//     istringstream is(str);
//     is >> S.n_rows; //稀疏矩阵行数
//     is >> S.n_cols; //稀疏矩阵列数
//     is >> S.nnz; //稀疏矩阵非零元个数
//     cout << "\nMatrix info: " << " rows: " << S.n_rows << ", cols: " << S.n_cols << ", nnz: " << S.nnz << endl;
//     long orig_nnz = S.nnz, rid = 0, cid = 0;
//     float vid = 0;
//
//     S.rows.resize(S.nnz);
//     S.cols.resize(S.nnz);
//     S.vals.resize(S.nnz);
//
//     long idx = 0;
//     for (long o_idx = 0; o_idx < orig_nnz; ++o_idx) {
//         //将稀疏矩阵非零元素的行、列索引以及值存储到相应数组中
//         fp >> rid >> cid >> vid;
//         S.rows[idx] = rid - 1;
//         S.cols[idx] = cid - 1;
//         S.vals[idx] = vid;
//         idx++;
//     }
//     cout << "\nTilesize: X = " << tile_sizeX << ", tilesize: Y = " << tile_sizeY << ", TB: " << BLOCKSIZE << " " << idx
//             << endl; //BLOCKSIZE=512，在util.h中已设置
//     S.nnz = idx; //稀疏矩阵非零元个数
//
//     preprocessing(S);
// }
