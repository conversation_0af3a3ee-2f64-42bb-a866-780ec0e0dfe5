#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <chrono>
#include <algorithm>
#include <cmath>
#include <limits>
#include <omp.h>
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <thrust/device_vector.h>
#include <thrust/scan.h>
#include <thrust/execution_policy.h>

// CUDA错误检查宏
#define CUDA_CHECK(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        std::cerr << "CUDA错误 " << __FILE__ << ":" << __LINE__ << " " << cudaGetErrorString(err) << std::endl; \
        exit(1); \
    } \
} while(0)

// 常量定义
const int WARP_SIZE = 32;
const int BANK_SIZE = 32;
const int MAX_STREAMS = 8;
const int TILE_K = 128;
const int CHUNK_SIZE = 256;

// 密度阈值
const int LOW_DENSITY_THRESHOLD = 32;
const int HIGH_DENSITY_THRESHOLD = 256;

// 块大小配置
const int LOW_DENSITY_BLOCK_SIZE = 256;
const int MEDIUM_DENSITY_BLOCK_SIZE = 256;
const int CHUNK_PER_BLOCK_KERNEL_SIZE = 256;

// CSR矩阵结构
struct CSRMatrix {
    int *row_ptr;
    int *col_idx;
    float *values;
    int rows, cols, nnz;
};

// =================================================================
// 核函数定义
// =================================================================

// 行分类核函数
__global__ void classify_rows_multi_level_kernel(
    const int *row_ptr, int num_rows,
    int *low_rows, int *medium_rows, int *high_rows,
    int *low_count, int *medium_count, int *high_count) {
    int row = blockIdx.x * blockDim.x + threadIdx.x;
    if (row >= num_rows) return;

    int nnz_in_row = row_ptr[row + 1] - row_ptr[row];

    if (nnz_in_row <= LOW_DENSITY_THRESHOLD) {
        int idx = atomicAdd(low_count, 1);
        low_rows[idx] = row;
    } else if (nnz_in_row <= HIGH_DENSITY_THRESHOLD) {
        int idx = atomicAdd(medium_count, 1);
        medium_rows[idx] = row;
    } else {
        int idx = atomicAdd(high_count, 1);
        high_rows[idx] = row;
    }
}

// 低密度区向量化核函数（保留warp shuffle优化）
__global__ void sddmm_low_density_vectorized_kernel(
    const float *A, const float *B, const int *row_ptr, const int *col_idx,
    const int *row_indices, float *values, int num_rows, int K) {
    extern __shared__ float shared_mem[];

    int warp_id = blockIdx.x * (blockDim.x / WARP_SIZE) + threadIdx.x / WARP_SIZE;
    int lane_id = threadIdx.x % WARP_SIZE;
    int local_warp_id = threadIdx.x / WARP_SIZE;

    if (warp_id >= num_rows) return;

    int row = row_indices[warp_id];
    int start = row_ptr[row];
    int end = row_ptr[row + 1];
    int nnz_in_row = end - start;

    // 共享内存布局：每个warp有自己的缓存区
    float *warp_cache = shared_mem + local_warp_id * (K + BANK_SIZE);

    // 向量化加载A行数据
    for (int k = lane_id; k < K; k += WARP_SIZE) {
        warp_cache[k] = A[(size_t) row * K + k];
    }
    __syncwarp();

    // 每个warp处理行内的一部分非零元
    for (int i = 0; i < nnz_in_row; i++) {
        int global_idx = start + i;
        int col = col_idx[global_idx];
        float partial_sum = 0.0f;

        // Warp内线程并行计算点积
        for (int k = lane_id; k < K; k += WARP_SIZE) {
            partial_sum += warp_cache[k] * B[(size_t) col * K + k];
        }

        // 使用Warp Shuffle进行高效归约
        for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        // Warp的0号线程将最终结果写回
        if (lane_id == 0) {
            values[global_idx] = partial_sum;
        }
    }
}

// 中密度区核函数（保留warp shuffle优化）
__global__ void sddmm_row_per_block_2d_kernel(
    const float *A, const float *B, const int *row_ptr, const int *col_idx,
    const int *row_indices, float *values, int num_rows, int K) {
    extern __shared__ float shared_A[];

    int row_idx = blockIdx.x;
    if (row_idx >= num_rows) return;

    int row = row_indices[row_idx];
    int start = row_ptr[row];
    int end = row_ptr[row + 1];
    int nnz_in_row = end - start;

    int tid = threadIdx.y * blockDim.x + threadIdx.x;
    int block_size = blockDim.x * blockDim.y;

    // 协作加载A行到共享内存
    for (int k = tid; k < K; k += block_size) {
        shared_A[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    // Warp级别的处理
    int warp_id = tid / WARP_SIZE;
    int lane_id = tid % WARP_SIZE;
    int warps_in_block = block_size / WARP_SIZE;

    // 每个Warp处理行内的一部分非零元
    for (int i = warp_id; i < nnz_in_row; i += warps_in_block) {
        int global_idx = start + i;
        int col = col_idx[global_idx];
        float partial_sum = 0.0f;

        // Warp内线程并行计算点积
        for (int k = lane_id; k < K; k += WARP_SIZE) {
            partial_sum += shared_A[k] * B[(size_t) col * K + k];
        }

        // 使用Warp Shuffle进行高效归约
        for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        // Warp的0号线程将最终结果写回
        if (lane_id == 0) {
            values[global_idx] = partial_sum;
        }
    }
}

// 高密度区chunk准备核函数
__global__ void get_chunk_counts_per_row_kernel(
    const int *row_ptr, const int *row_indices, int *chunk_counts, int num_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_rows) return;

    int row = row_indices[idx];
    int nnz_in_row = row_ptr[row + 1] - row_ptr[row];
    chunk_counts[idx] = (nnz_in_row + CHUNK_SIZE - 1) / CHUNK_SIZE;
}

__global__ void populate_chunks_kernel(
    const int *row_indices, const int *chunk_counts, const int *write_offsets,
    int *chunk_rows, int *chunk_offsets, int num_rows) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_rows) return;

    int row = row_indices[idx];
    int num_chunks = chunk_counts[idx];
    int write_start = write_offsets[idx];

    for (int c = 0; c < num_chunks; ++c) {
        chunk_rows[write_start + c] = row;
        chunk_offsets[write_start + c] = c * CHUNK_SIZE;
    }
}

// 高密度区2D核函数（保留warp shuffle优化）
__global__ void sddmm_chunk_per_block_2d_kernel(
    const float *A, const float *B, const int *row_ptr, const int *col_idx,
    const int *chunk_rows, const int *chunk_offsets, float *values,
    int num_chunks, int K) {
    extern __shared__ float shared_A[];

    int chunk_idx = blockIdx.x;
    if (chunk_idx >= num_chunks) return;

    int row = chunk_rows[chunk_idx];
    int chunk_offset = chunk_offsets[chunk_idx];
    int start = row_ptr[row] + chunk_offset;
    int end = min(start + CHUNK_SIZE, row_ptr[row + 1]);
    int chunk_nnz = end - start;

    int tid = threadIdx.y * blockDim.x + threadIdx.x;
    int block_size = blockDim.x * blockDim.y;

    // 协作加载A行到共享内存
    for (int k = tid; k < K; k += block_size) {
        shared_A[k] = A[(size_t) row * K + k];
    }
    __syncthreads();

    // Warp级别的处理
    int warp_id = tid / WARP_SIZE;
    int lane_id = tid % WARP_SIZE;
    int warps_in_block = block_size / WARP_SIZE;

    // 每个Warp处理chunk中的一部分非零元
    for (int i = warp_id; i < chunk_nnz; i += warps_in_block) {
        int global_idx = start + i;
        int col = col_idx[global_idx];
        float partial_sum = 0.0f;

        // Warp内线程并行计算点积
        for (int k = lane_id; k < K; k += WARP_SIZE) {
            partial_sum += shared_A[k] * B[(size_t) col * K + k];
        }

        // 使用Warp Shuffle进行高效归约
        for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
            partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
        }

        // Warp的0号线程将最终结果写回
        if (lane_id == 0) {
            values[global_idx] = partial_sum;
        }
    }
}

// 高密度区双缓冲核函数（保留warp shuffle优化）
__global__ void sddmm_chunk_double_buffer_kernel(
    const float *A, const float *B, const int *row_ptr, const int *col_idx,
    const int *chunk_rows, const int *chunk_offsets, float *values,
    int num_chunks, int K) {
    extern __shared__ float shared_mem[];

    int chunk_idx = blockIdx.x;
    if (chunk_idx >= num_chunks) return;

    int row = chunk_rows[chunk_idx];
    int chunk_offset = chunk_offsets[chunk_idx];
    int start = row_ptr[row] + chunk_offset;
    int end = min(start + CHUNK_SIZE, row_ptr[row + 1]);
    int chunk_nnz = end - start;

    int tid = threadIdx.y * blockDim.x + threadIdx.x;
    int block_size = blockDim.x * blockDim.y;

    // 共享内存布局：双缓冲A
    float *buffer_A0 = shared_mem;
    float *buffer_A1 = buffer_A0 + TILE_K + BANK_SIZE;

    // Warp级别的处理
    int warp_id = tid / WARP_SIZE;
    int lane_id = tid % WARP_SIZE;
    int warps_in_block = block_size / WARP_SIZE;

    // 分块处理K维度
    for (int k_start = 0; k_start < K; k_start += TILE_K) {
        int k_end = min(k_start + TILE_K, K);
        int k_size = k_end - k_start;

        // 当前缓冲区选择
        float *current_buffer = (k_start / TILE_K) % 2 == 0 ? buffer_A0 : buffer_A1;

        // 协作加载A的当前tile到共享内存
        for (int k = tid; k < k_size; k += block_size) {
            current_buffer[k] = A[(size_t) row * K + k_start + k];
        }
        __syncthreads();

        // 每个Warp处理chunk中的一部分非零元
        for (int i = warp_id; i < chunk_nnz; i += warps_in_block) {
            int global_idx = start + i;
            int col = col_idx[global_idx];
            float partial_sum = 0.0f;

            // Warp内线程并行计算当前tile的点积
            for (int k = lane_id; k < k_size; k += WARP_SIZE) {
                partial_sum += current_buffer[k] * B[(size_t) col * K + k_start + k];
            }

            // 使用Warp Shuffle进行高效归约
            for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }

            // Warp的0号线程累加结果
            if (lane_id == 0) {
                if (k_start == 0) {
                    values[global_idx] = partial_sum;
                } else {
                    values[global_idx] += partial_sum;
                }
            }
        }
        __syncthreads();
    }
}

// 高密度区高级优化核函数（保留warp shuffle + 向量化）
__global__ void sddmm_high_density_advanced_kernel(
    const float *A, const float *B, const int *row_ptr, const int *col_idx,
    const int *chunk_rows, const int *chunk_offsets, float *values,
    int num_chunks, int K) {
    extern __shared__ float shared_mem[];

    int chunk_idx = blockIdx.x;
    if (chunk_idx >= num_chunks) return;

    int row = chunk_rows[chunk_idx];
    int chunk_offset = chunk_offsets[chunk_idx];
    int start = row_ptr[row] + chunk_offset;
    int end = min(start + CHUNK_SIZE, row_ptr[row + 1]);
    int chunk_nnz = end - start;

    int tid = threadIdx.y * blockDim.x + threadIdx.x;
    int block_size = blockDim.x * blockDim.y;

    // 共享内存布局：避免bank冲突的A缓存
    float *a_tile = shared_mem;
    float *b_cache = shared_mem + TILE_K + BANK_SIZE;

    // Warp级别的处理
    int warp_id = tid / WARP_SIZE;
    int lane_id = tid % WARP_SIZE;
    int warps_in_block = block_size / WARP_SIZE;

    // K维度分块处理
    for (int k_start = 0; k_start < K; k_start += TILE_K) {
        int k_end = min(k_start + TILE_K, K);
        int tile_k_size = k_end - k_start;

        // 向量化加载A分块到共享内存，避免bank冲突
        for (int k = tid; k < tile_k_size; k += block_size) {
            if (k_start + k < K) {
                // 使用padding避免bank冲突
                int padded_idx = k + (k / BANK_SIZE);
                a_tile[padded_idx] = A[(size_t) row * K + k_start + k];
            }
        }
        __syncthreads();

        // 每个Warp处理chunk中的一部分非零元
        for (int i = warp_id; i < chunk_nnz; i += warps_in_block) {
            int global_idx = start + i;
            int col = col_idx[global_idx];

            // 多级累加器，减少寄存器压力
            float4 acc = make_float4(0.0f, 0.0f, 0.0f, 0.0f);

            // 向量化计算，4个元素一组
            int vector_size = 4;
            for (int k = lane_id * vector_size; k < tile_k_size; k += WARP_SIZE * vector_size) {
                if (k + vector_size <= tile_k_size) {
                    // 从共享内存读取A（考虑padding）
                    int padded_k = k + (k / BANK_SIZE);
                    float4 a_vec = make_float4(
                        a_tile[padded_k],
                        a_tile[padded_k + 1],
                        a_tile[padded_k + 2],
                        a_tile[padded_k + 3]
                    );

                    // 从全局内存读取B（合并访问）
                    float4 b_vec = make_float4(
                        B[(size_t) col * K + k_start + k],
                        B[(size_t) col * K + k_start + k + 1],
                        B[(size_t) col * K + k_start + k + 2],
                        B[(size_t) col * K + k_start + k + 3]
                    );

                    // FMA操作
                    acc.x += a_vec.x * b_vec.x;
                    acc.y += a_vec.y * b_vec.y;
                    acc.z += a_vec.z * b_vec.z;
                    acc.w += a_vec.w * b_vec.w;
                }
            }

            // 归约向量结果
            float partial_sum = acc.x + acc.y + acc.z + acc.w;

            // 处理剩余元素
            for (int k = (tile_k_size / (WARP_SIZE * vector_size)) * (WARP_SIZE * vector_size) + lane_id;
                 k < tile_k_size; k += WARP_SIZE) {
                int padded_k = k + (k / BANK_SIZE);
                partial_sum += a_tile[padded_k] * B[(size_t) col * K + k_start + k];
            }

            // Warp shuffle归约（保留原有优化）
            for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
                partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
            }

            // 累加到全局结果
            if (lane_id == 0) {
                if (k_start == 0) {
                    values[global_idx] = partial_sum;
                } else {
                    values[global_idx] += partial_sum;
                }
            }
        }
        __syncthreads();
    }
}

// =================================================================
// 主优化函数
// =================================================================

void sddmm_multi_kernel_optimized(
    const float *d_A, const float *d_B, CSRMatrix &sparse,
    const std::vector<int> &h_csr_row_ptr, int K,
    size_t shared_mem_per_block,
    float &time_low, float &time_med, float &time_high) {
    // 初始化时间
    time_low = time_med = time_high = 0.0f;

    // 分配内存
    int *d_low_rows, *d_medium_rows, *d_high_rows;
    int *d_low_count, *d_medium_count, *d_high_count;
    CUDA_CHECK(cudaMalloc(&d_low_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_rows, (size_t)sparse.rows * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_low_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_medium_count, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_high_count, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_low_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_medium_count, 0, sizeof(int)));
    CUDA_CHECK(cudaMemset(d_high_count, 0, sizeof(int)));

    // 创建流
    cudaStream_t streams[MAX_STREAMS];
    for (int i = 0; i < MAX_STREAMS; ++i) {
        CUDA_CHECK(cudaStreamCreate(&streams[i]));
    }

    // 创建事件
    cudaEvent_t start_low, stop_low, start_med, stop_med, start_high, stop_high;
    CUDA_CHECK(cudaEventCreate(&start_low));
    CUDA_CHECK(cudaEventCreate(&stop_low));
    CUDA_CHECK(cudaEventCreate(&start_med));
    CUDA_CHECK(cudaEventCreate(&stop_med));
    CUDA_CHECK(cudaEventCreate(&start_high));
    CUDA_CHECK(cudaEventCreate(&stop_high));

    // 行分类
    dim3 block_classify(256);
    dim3 grid_classify((sparse.rows + block_classify.x - 1) / block_classify.x);
    classify_rows_multi_level_kernel<<<grid_classify, block_classify, 0, streams[0]>>>(
        sparse.row_ptr, sparse.rows, d_low_rows, d_medium_rows, d_high_rows,
        d_low_count, d_medium_count, d_high_count);

    // 获取分类结果
    int h_counts[3] = {0, 0, 0};
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[0], d_low_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[1], d_medium_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaMemcpyAsync(&h_counts[2], d_high_count, sizeof(int), cudaMemcpyDeviceToHost, streams[0]));
    CUDA_CHECK(cudaStreamSynchronize(streams[0]));

    printf("行分类结果: 低密度=%d, 中密度=%d, 高密度=%d\n", h_counts[0], h_counts[1], h_counts[2]);

    // === 低密度区处理（保留warp shuffle优化）===
    if (h_counts[0] > 0) {
        size_t required_mem_low = (size_t) (LOW_DENSITY_BLOCK_SIZE / 32) * (K + BANK_SIZE) * sizeof(float);
        if (required_mem_low > shared_mem_per_block) {
            printf("警告: 低密度区需要共享内存 %zu bytes，超出限制 %zu bytes\n",
                   required_mem_low, shared_mem_per_block);
            time_low = -1.0f;
        } else {
            CUDA_CHECK(cudaEventRecord(start_low, streams[1]));

            int streams_for_low = std::min(MAX_STREAMS - 2, 4);
            int rows_per_stream = (h_counts[0] + streams_for_low - 1) / streams_for_low;

            for (int stream_id = 0; stream_id < streams_for_low; stream_id++) {
                int start_row = stream_id * rows_per_stream;
                int end_row = std::min(start_row + rows_per_stream, h_counts[0]);
                if (start_row >= end_row) break;

                int rows_in_stream = end_row - start_row;
                const int warps_per_block_low = LOW_DENSITY_BLOCK_SIZE / 32;
                dim3 grid_low((rows_in_stream + warps_per_block_low - 1) / warps_per_block_low);
                dim3 block_low(LOW_DENSITY_BLOCK_SIZE);

                sddmm_low_density_vectorized_kernel<<<grid_low, block_low, required_mem_low, streams[stream_id + 1]>>>(
                    d_A, d_B, sparse.row_ptr, sparse.col_idx,
                    d_low_rows + start_row, sparse.values, rows_in_stream, K);
            }

            for (int i = 1; i <= streams_for_low; i++) {
                CUDA_CHECK(cudaStreamSynchronize(streams[i]));
            }

            CUDA_CHECK(cudaEventRecord(stop_low, streams[1]));
        }
    }

    // === 中密度区处理（保留warp shuffle优化）===
    if (h_counts[1] > 0) {
        size_t required_mem_medium = (size_t) K * sizeof(float);
        if (required_mem_medium > shared_mem_per_block) {
            printf("警告: 中密度区需要共享内存 %zu bytes，超出限制 %zu bytes\n",
                   required_mem_medium, shared_mem_per_block);
            time_med = -1.0f;
        } else {
            CUDA_CHECK(cudaEventRecord(start_med, streams[MAX_STREAMS - 2]));
            const int Y_DIM_MED = MEDIUM_DENSITY_BLOCK_SIZE / WARP_SIZE;
            dim3 block_med_2d(WARP_SIZE, Y_DIM_MED);
            sddmm_row_per_block_2d_kernel<<<h_counts[1], block_med_2d, required_mem_medium, streams[MAX_STREAMS - 2]>>>(
                d_A, d_B, sparse.row_ptr, sparse.col_idx, d_medium_rows, sparse.values, h_counts[1], K);
            CUDA_CHECK(cudaEventRecord(stop_med, streams[MAX_STREAMS - 2]));
        }
    }

    // === 高密度区处理（保留warp shuffle优化）===
    if (h_counts[2] > 0) {
        // 动态计算chunks
        std::vector<int> h_high_rows(h_counts[2]);
        CUDA_CHECK(cudaMemcpy(h_high_rows.data(), d_high_rows, h_counts[2] * sizeof(int), cudaMemcpyDeviceToHost));

        int max_chunks_per_row = 0;
        int total_chunks = 0;
        for (int i = 0; i < h_counts[2]; i++) {
            int row = h_high_rows[i];
            int nnz_in_row = h_csr_row_ptr[row + 1] - h_csr_row_ptr[row];
            int chunks_for_row = (nnz_in_row + CHUNK_SIZE - 1) / CHUNK_SIZE;
            max_chunks_per_row = std::max(max_chunks_per_row, chunks_for_row);
            total_chunks += chunks_for_row;
        }

        printf("高密度区统计: 最大chunks/行=%d, 总chunks=%d\n", max_chunks_per_row, total_chunks);

        // 分配chunk内存
        int *d_chunk_rows_high, *d_chunk_offsets_high;
        CUDA_CHECK(cudaMalloc(&d_chunk_rows_high, total_chunks * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_offsets_high, total_chunks * sizeof(int)));

        int *d_chunk_counts_per_row, *d_chunk_write_offsets;
        CUDA_CHECK(cudaMalloc(&d_chunk_counts_per_row, h_counts[2] * sizeof(int)));
        CUDA_CHECK(cudaMalloc(&d_chunk_write_offsets, h_counts[2] * sizeof(int)));

        // 准备chunks
        dim3 grid_prep((h_counts[2] + 255) / 256);
        dim3 block_prep(256);
        get_chunk_counts_per_row_kernel<<<grid_prep, block_prep, 0, streams[MAX_STREAMS - 1]>>>(
            sparse.row_ptr, d_high_rows, d_chunk_counts_per_row, h_counts[2]);

        thrust::exclusive_scan(thrust::cuda::par.on(streams[MAX_STREAMS - 1]),
                               thrust::device_ptr<int>(d_chunk_counts_per_row),
                               thrust::device_ptr<int>(d_chunk_counts_per_row + h_counts[2]),
                               thrust::device_ptr<int>(d_chunk_write_offsets));

        populate_chunks_kernel<<<grid_prep, block_prep, 0, streams[MAX_STREAMS - 1]>>>(
            d_high_rows, d_chunk_counts_per_row, d_chunk_write_offsets,
            d_chunk_rows_high, d_chunk_offsets_high, h_counts[2]);

        // 执行高密度区计算
        if (total_chunks > 0) {
            if (K <= TILE_K) {
                // 小K值：使用基础2D核函数（保留warp shuffle）
                size_t required_mem_high = (size_t) K * sizeof(float);
                if (required_mem_high <= shared_mem_per_block) {
                    CUDA_CHECK(cudaEventRecord(start_high, streams[MAX_STREAMS - 1]));
                    const int Y_DIM_HIGH = CHUNK_PER_BLOCK_KERNEL_SIZE / WARP_SIZE;
                    dim3 block_high_2d(WARP_SIZE, Y_DIM_HIGH);
                    sddmm_chunk_per_block_2d_kernel<<<total_chunks, block_high_2d, required_mem_high, streams[
                        MAX_STREAMS - 1]>>>(
                        d_A, d_B, sparse.row_ptr, sparse.col_idx, d_chunk_rows_high,
                        d_chunk_offsets_high, sparse.values, total_chunks, K);
                    CUDA_CHECK(cudaEventRecord(stop_high, streams[MAX_STREAMS - 1]));
                } else {
                    printf("警告: 高密度区需要共享内存 %zu bytes，超出限制 %zu bytes\n",
                           required_mem_high, shared_mem_per_block);
                    time_high = -1.0f;
                }
            } else if (K <= TILE_K * 2) {
                // 中等K值：使用双缓冲核函数（保留warp shuffle）
                size_t required_mem_high = (2 * (TILE_K + BANK_SIZE)) * sizeof(float);
                if (required_mem_high <= shared_mem_per_block) {
                    CUDA_CHECK(cudaEventRecord(start_high, streams[MAX_STREAMS - 1]));
                    const int Y_DIM_HIGH = CHUNK_PER_BLOCK_KERNEL_SIZE / WARP_SIZE;
                    dim3 block_high_2d(WARP_SIZE, Y_DIM_HIGH);
                    sddmm_chunk_double_buffer_kernel<<<total_chunks, block_high_2d, required_mem_high, streams[
                        MAX_STREAMS - 1]>>>(
                        d_A, d_B, sparse.row_ptr, sparse.col_idx, d_chunk_rows_high,
                        d_chunk_offsets_high, sparse.values, total_chunks, K);
                    CUDA_CHECK(cudaEventRecord(stop_high, streams[MAX_STREAMS - 1]));
                } else {
                    printf("警告: 高密度区双缓冲需要共享内存 %zu bytes，超出限制 %zu bytes\n",
                           required_mem_high, shared_mem_per_block);
                    time_high = -1.0f;
                }
            } else {
                // 大K值：使用高级优化核函数（保留warp shuffle + 向量化）
                size_t required_mem_high = (2 * (TILE_K + BANK_SIZE)) * sizeof(float);
                if (required_mem_high <= shared_mem_per_block) {
                    CUDA_CHECK(cudaEventRecord(start_high, streams[MAX_STREAMS - 1]));
                    const int Y_DIM_HIGH = CHUNK_PER_BLOCK_KERNEL_SIZE / WARP_SIZE;
                    dim3 block_high_2d(WARP_SIZE, Y_DIM_HIGH);
                    sddmm_high_density_advanced_kernel<<<total_chunks, block_high_2d, required_mem_high, streams[
                        MAX_STREAMS - 1]>>>(
                        d_A, d_B, sparse.row_ptr, sparse.col_idx, d_chunk_rows_high,
                        d_chunk_offsets_high, sparse.values, total_chunks, K);
                    CUDA_CHECK(cudaEventRecord(stop_high, streams[MAX_STREAMS - 1]));
                } else {
                    printf("警告: 高密度区高级优化需要共享内存 %zu bytes，超出限制 %zu bytes\n",
                           required_mem_high, shared_mem_per_block);
                    time_high = -1.0f;
                }
            }
        }

        // 清理高密度区内存
        CUDA_CHECK(cudaFree(d_chunk_counts_per_row));
        CUDA_CHECK(cudaFree(d_chunk_write_offsets));
        CUDA_CHECK(cudaFree(d_chunk_rows_high));
        CUDA_CHECK(cudaFree(d_chunk_offsets_high));
    }

    // 同步所有流
    for (int i = 0; i < MAX_STREAMS; ++i) {
        CUDA_CHECK(cudaStreamSynchronize(streams[i]));
    }

    // 计算执行时间
    if (h_counts[0] > 0 && time_low >= 0) {
        CUDA_CHECK(cudaEventElapsedTime(&time_low, start_low, stop_low));
    }
    if (h_counts[1] > 0 && time_med >= 0) {
        CUDA_CHECK(cudaEventElapsedTime(&time_med, start_med, stop_med));
    }
    if (h_counts[2] > 0 && time_high >= 0) {
        CUDA_CHECK(cudaEventElapsedTime(&time_high, start_high, stop_high));
    }

    // 清理资源
    CUDA_CHECK(cudaFree(d_low_rows));
    CUDA_CHECK(cudaFree(d_medium_rows));
    CUDA_CHECK(cudaFree(d_high_rows));
    CUDA_CHECK(cudaFree(d_low_count));
    CUDA_CHECK(cudaFree(d_medium_count));
    CUDA_CHECK(cudaFree(d_high_count));

    for (int i = 0; i < MAX_STREAMS; ++i) {
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
    }

    CUDA_CHECK(cudaEventDestroy(start_low));
    CUDA_CHECK(cudaEventDestroy(stop_low));
    CUDA_CHECK(cudaEventDestroy(start_med));
    CUDA_CHECK(cudaEventDestroy(stop_med));
    CUDA_CHECK(cudaEventDestroy(start_high));
    CUDA_CHECK(cudaEventDestroy(stop_high));
}

// =================================================================
// 辅助函数
// =================================================================

void sddmm_cpu_reference(const float *A, const float *B, const int *row_ptr, const int *col_idx,
                         float *values, int M, int N, int K) {
#pragma omp parallel for schedule(dynamic)
    for (int row = 0; row < M; ++row) {
        int start = row_ptr[row];
        int end = row_ptr[row + 1];
        for (int idx = start; idx < end; ++idx) {
            int col = col_idx[idx];
            float sum = 0.0f;
#pragma GCC ivdep
            for (int k = 0; k < K; ++k) {
                sum += A[(size_t) row * K + k] * B[(size_t) col * K + k];
            }
            values[idx] = sum;
        }
    }
}

void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
                     std::vector<int> &coo_rows, std::vector<int> &coo_cols) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "错误: 无法打开矩阵文件: " << filename << std::endl;
        exit(1);
    }

    // 跳过注释行
    while (file.peek() == '%') {
        file.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    }

    file >> M >> N >> nnz;
    coo_rows.resize(nnz);
    coo_cols.resize(nnz);

    for (int i = 0; i < nnz; ++i) {
        int r, c;
        file >> r >> c;
        coo_rows[i] = r - 1; // 转换为0-based索引
        coo_cols[i] = c - 1;
    }

    file.close();
}

void coo_to_csr(const std::vector<int> &coo_rows, const std::vector<int> &coo_cols,
                int M, int nnz, std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
    csr_row_ptr.resize(M + 1, 0);
    csr_col_idx.resize(nnz);

    // 计算每行的非零元素数量
    for (int i = 0; i < nnz; ++i) {
        csr_row_ptr[coo_rows[i] + 1]++;
    }

    // 计算累积和得到行指针
    for (int i = 1; i <= M; ++i) {
        csr_row_ptr[i] += csr_row_ptr[i - 1];
    }

    // 填充列索引
    std::vector<int> temp_ptr = csr_row_ptr;
    for (int i = 0; i < nnz; ++i) {
        int row = coo_rows[i];
        csr_col_idx[temp_ptr[row]++] = coo_cols[i];
    }
}

bool verify_result(const float *gpu_result, const float *cpu_result, int nnz, float tolerance = 1e-4f) {
    int errors = 0;
    float max_error = 0.0f;

    for (int i = 0; i < nnz; ++i) {
        float error = std::abs(gpu_result[i] - cpu_result[i]);
        max_error = std::max(max_error, error);

        if (error > tolerance) {
            errors++;
            if (errors <= 10) {
                // 只打印前10个错误
                printf("错误 [%d]: GPU=%.6f, CPU=%.6f, 差值=%.6f\n",
                       i, gpu_result[i], cpu_result[i], error);
            }
        }
    }

    printf("验证结果: 错误数=%d/%d, 最大误差=%.6e\n", errors, nnz, max_error);
    return errors == 0;
}

// =================================================================
// 主函数
// =================================================================

int main(int argc, char *argv[]) {
    if (argc != 3) {
        std::cout << "用法: " << argv[0] << " <matrix.mtx> <K>" << std::endl;
        return 1;
    }

    std::string filename = argv[1];
    int K = std::atoi(argv[2]);

    printf("========================================\n");
    printf("SDDMM 多核函数优化版本（保留Warp Shuffle）\n");
    printf("========================================\n");
    printf("矩阵文件: %s\n", filename.c_str());
    printf("K维度: %d\n", K);

    // 检查CUDA设备
    int device_count;
    CUDA_CHECK(cudaGetDeviceCount(&device_count));
    if (device_count == 0) {
        std::cerr << "错误: 未找到CUDA设备" << std::endl;
        return 1;
    }

    cudaDeviceProp prop;
    CUDA_CHECK(cudaGetDeviceProperties(&prop, 0));
    printf("GPU: %s\n", prop.name);
    printf("共享内存/块: %zu bytes\n", prop.sharedMemPerBlock);
    printf("========================================\n");

    // 加载矩阵
    int M, N, nnz;
    std::vector<int> coo_rows, coo_cols;
    load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);

    printf("矩阵规模: %d x %d, 非零元素: %d\n", M, N, nnz);
    printf("稀疏度: %.4f%%\n", 100.0 * nnz / ((double) M * N));

    // 转换为CSR格式
    std::vector<int> h_csr_row_ptr, h_csr_col_idx;
    coo_to_csr(coo_rows, coo_cols, M, nnz, h_csr_row_ptr, h_csr_col_idx);

    // 生成随机矩阵A和B
    std::vector<float> h_A(M * K), h_B(N * K);
    std::srand(42);
    for (int i = 0; i < M * K; ++i) {
        h_A[i] = static_cast<float>(std::rand()) / RAND_MAX;
    }
    for (int i = 0; i < N * K; ++i) {
        h_B[i] = static_cast<float>(std::rand()) / RAND_MAX;
    }

    // 分配GPU内存
    float *d_A, *d_B;
    CSRMatrix d_sparse;

    CUDA_CHECK(cudaMalloc(&d_A, M * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_B, N * K * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_sparse.row_ptr, (M + 1) * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_sparse.col_idx, nnz * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_sparse.values, nnz * sizeof(float)));

    d_sparse.rows = M;
    d_sparse.cols = N;
    d_sparse.nnz = nnz;

    // 复制数据到GPU
    CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice));

    // 运行优化版本
    printf("\n运行GPU优化版本（保留Warp Shuffle）...\n");
    float time_low, time_med, time_high;

    auto start_time = std::chrono::high_resolution_clock::now();
    sddmm_multi_kernel_optimized(d_A, d_B, d_sparse, h_csr_row_ptr, K,
                                 prop.sharedMemPerBlock, time_low, time_med, time_high);
    auto end_time = std::chrono::high_resolution_clock::now();

    float total_gpu_time = std::chrono::duration<float, std::milli>(end_time - start_time).count();

    // 复制结果回CPU
    std::vector<float> h_gpu_result(nnz);
    CUDA_CHECK(cudaMemcpy(h_gpu_result.data(), d_sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost));

    // 运行CPU参考版本
    printf("\n运行CPU参考版本...\n");
    std::vector<float> h_cpu_result(nnz);

    start_time = std::chrono::high_resolution_clock::now();
    sddmm_cpu_reference(h_A.data(), h_B.data(), h_csr_row_ptr.data(), h_csr_col_idx.data(),
                        h_cpu_result.data(), M, N, K);
    end_time = std::chrono::high_resolution_clock::now();

    float cpu_time = std::chrono::duration<float, std::milli>(end_time - start_time).count();

    // 验证结果
    printf("\n验证计算结果...\n");
    bool correct = verify_result(h_gpu_result.data(), h_cpu_result.data(), nnz);

    // 性能统计
    printf("\n========================================\n");
    printf("性能统计（保留Warp Shuffle优化）\n");
    printf("========================================\n");
    printf("CPU时间: %.2f ms\n", cpu_time);
    printf("GPU总时间: %.2f ms\n", total_gpu_time);
    printf("  - 低密度区: %.2f ms\n", time_low);
    printf("  - 中密度区: %.2f ms\n", time_med);
    printf("  - 高密度区: %.2f ms\n", time_high);
    printf("加速比: %.2fx\n", cpu_time / total_gpu_time);

    // 计算GFLOPS
    double flops = 2.0 * nnz * K; // 每个非零元素需要K次乘法和K次加法
    double gpu_gflops = flops / (total_gpu_time * 1e6);
    double cpu_gflops = flops / (cpu_time * 1e6);

    printf("GPU GFLOPS: %.2f\n", gpu_gflops);
    printf("CPU GFLOPS: %.2f\n", cpu_gflops);
    printf("计算正确性: %s\n", correct ? "✓ 通过" : "✗ 失败");
    printf("========================================\n");

    // 清理内存
    CUDA_CHECK(cudaFree(d_A));
    CUDA_CHECK(cudaFree(d_B));
    CUDA_CHECK(cudaFree(d_sparse.row_ptr));
    CUDA_CHECK(cudaFree(d_sparse.col_idx));
    CUDA_CHECK(cudaFree(d_sparse.values));

    return 0;
}
