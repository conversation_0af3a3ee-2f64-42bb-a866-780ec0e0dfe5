// #include <cuda_runtime.h>
// #include <cooperative_groups.h>
// #include <cub/cub.cuh>
// #include <iostream>
// #include <cmath>
// #include <fstream>       // 文件读写操作
// #include <sstream>       // istringstream
// #include <cstdlib>       // atoi/exit
// #include <string>        // string类
// #include <unordered_map> // 高频行列统计
// #include <vector>        // 容器类
// #include <algorithm>     // 排序算法
// namespace cg = cooperative_groups;
// using namespace std;
//
// const int BLOCK_SIZE = 32; // 线程块大小
// const int WARP_SIZE = 32; // Warp大小
// const int TILE_SIZE = 16; // Tensor Core操作尺寸
// const int NUM_ELEMENTS_PER_THREAD = 4; // 每个线程处理的非零元素数
//
// __global__ void sddmm_linear_layout_kernel(
//     const float *__restrict__ B, // 稠密矩阵 B (M x K)
//     const float *__restrict__ C, // 稠密矩阵 C (K x N)
//     const int *__restrict__ row_indices, // 非零元素行索引
//     const int *__restrict__ col_indices, // 非零元素列索引
//     float *__restrict__ values, // 输出值 (A ⊙ (B·C))
//     int M, int N, int K, // 矩阵维度
//     int nnz) {
//     // 非零元素总数
//
//     cg::thread_block block = cg::this_thread_block();
//     cg::thread_block_tile<WARP_SIZE> warp = cg::tiled_partition<WARP_SIZE>(block);
//
//     // 动态共享内存分配 (B数据 + C数据)
//     extern __shared__ float shared_memory[];
//     const int shm_B_offset = 0;
//     const int shm_C_offset = NUM_ELEMENTS_PER_THREAD * WARP_SIZE * TILE_SIZE;
//     float *shm_B = &shared_memory[shm_B_offset];
//     float *shm_C = &shared_memory[shm_C_offset];
//
//     // 每个warp处理一个32x32分块
//     const int block_id = blockIdx.x;
//     const int warp_id = threadIdx.x / WARP_SIZE;
//     const int lane_id = threadIdx.x % WARP_SIZE;
//
//     // 计算当前warp处理的非零元素范围
//     const int nnz_per_block = (nnz + gridDim.x - 1) / gridDim.x;
//     const int warps_per_block = blockDim.x / WARP_SIZE;
//     const int nnz_per_warp = (nnz_per_block + warps_per_block - 1) / warps_per_block;
//     const int start_idx = min(block_id * nnz_per_block + warp_id * nnz_per_warp, nnz);
//     const int end_idx = min(start_idx + nnz_per_warp, nnz);
//     const int valid_nnz = end_idx - start_idx;
//
//     // 寄存器存储局部结果
//     float local_results[NUM_ELEMENTS_PER_THREAD] = {0.0f};
//     int local_rows[NUM_ELEMENTS_PER_THREAD] = {-1};
//     int local_cols[NUM_ELEMENTS_PER_THREAD] = {-1};
//
//     // 加载非零元素坐标到寄存器
//     for (int i = 0; i < NUM_ELEMENTS_PER_THREAD; i++) {
//         const int idx = start_idx + lane_id + i * WARP_SIZE;
//         if (idx < end_idx) {
//             local_rows[i] = row_indices[idx];
//             local_cols[i] = col_indices[idx];
//         }
//     }
//
//     // 主计算循环 (K维度分块)
//     for (int k_base = 0; k_base < K; k_base += TILE_SIZE) {
//         const int k_end = min(k_base + TILE_SIZE, K);
//         const int tile_size = k_end - k_base;
//
//         // 初始化共享内存
//         for (int i = 0; i < NUM_ELEMENTS_PER_THREAD; i++) {
//             const int elem_idx = i * WARP_SIZE + lane_id;
//             if (elem_idx < valid_nnz) {
//                 for (int kk = 0; kk < TILE_SIZE; kk++) {
//                     shm_B[elem_idx * TILE_SIZE + kk] = 0.0f;
//                     shm_C[elem_idx * TILE_SIZE + kk] = 0.0f;
//                 }
//             }
//         }
//         block.sync();
//
//         // 协作加载B和C的分块数据到共享内存
//         for (int k = k_base; k < k_end; k++) {
//             const int kk = k - k_base;
//
//             for (int i = 0; i < NUM_ELEMENTS_PER_THREAD; i++) {
//                 const int elem_idx = i * WARP_SIZE + lane_id;
//                 const int global_idx = start_idx + elem_idx;
//
//                 if (global_idx < end_idx) {
//                     const int row = local_rows[i];
//                     const int col = local_cols[i];
//
//                     // 所有线程都加载数据，避免只有lane 0加载的问题
//                     shm_B[elem_idx * TILE_SIZE + kk] = B[row * K + k];
//                     shm_C[elem_idx * TILE_SIZE + kk] = C[k * N + col];
//                 }
//             }
//         }
//         block.sync(); // 确保所有数据加载完成
//
//         // 点积计算
//         for (int i = 0; i < NUM_ELEMENTS_PER_THREAD; i++) {
//             const int elem_idx = i * WARP_SIZE + lane_id;
//             const int global_idx = start_idx + elem_idx;
//
//             if (global_idx < end_idx) {
//                 float sum = 0.0f;
//                 for (int kk = 0; kk < tile_size; kk++) {
//                     sum += shm_B[elem_idx * TILE_SIZE + kk] * shm_C[elem_idx * TILE_SIZE + kk];
//                 }
//                 local_results[i] += sum;
//             }
//         }
//     }
//
//     // 写回结果
//     for (int i = 0; i < NUM_ELEMENTS_PER_THREAD; i++) {
//         const int idx = start_idx + lane_id + i * WARP_SIZE;
//         if (idx < end_idx) {
//             values[idx] = local_results[i];
//         }
//     }
// }
//
// void launch_sddmm_linear_layout(
//     const float *B, const float *C,
//     const int *row_indices, const int *col_indices,
//     float *values, int M, int N, int K, int nnz) {
//     // 计算执行配置
//     const int num_warps_per_block = BLOCK_SIZE / WARP_SIZE;
//     const int nnz_per_warp = NUM_ELEMENTS_PER_THREAD * WARP_SIZE;
//     const int num_blocks = (nnz + (nnz_per_warp * num_warps_per_block) - 1)
//                            / (nnz_per_warp * num_warps_per_block);
//
//     // 计算共享内存大小 (B数据 + C数据)
//     size_t shared_mem_size =
//             2 * (NUM_ELEMENTS_PER_THREAD * WARP_SIZE * TILE_SIZE) * sizeof(float);
//
//     // 启动内核
//     sddmm_linear_layout_kernel<<<num_blocks, BLOCK_SIZE, shared_mem_size>>>(
//         B, C, row_indices, col_indices, values, M, N, K, nnz);
//
//     cudaError_t err = cudaGetLastError();
//     if (err != cudaSuccess) {
//         printf("Kernel launch error: %s\n", cudaGetErrorString(err));
//     }
//     cudaDeviceSynchronize();
// }
//
// int main(int argc, char *argv[]) {
//     if (argc < 5) {
//         printf("Usage: %s <matrix_file> <K> <tile_sizeX> <tile_sizeY>\n", argv[0]);
//         return 1;
//     }
//
//     ifstream fp(argv[1]);
//     const int K = atoi(argv[2]);
//
//     // 读取矩阵头信息
//     string str;
//     while (getline(fp, str) && !isdigit(str[0])) {}
//     int M, N, nnz;
//     istringstream header(str);
//     header >> M >> N >> nnz;
//     printf("M = %d, N = %d, total_nnz = %d\n", M, N, nnz);
//
//     // 初始化稠密矩阵
//     float *h_B = new float[M * K];
//     float *h_C = new float[K * N];
//
//     // 读取非零元素坐标
//     int *h_rows = new int[nnz];
//     int *h_cols = new int[nnz];
//     for (int idx = 0; idx < nnz; ++idx) {
//         int rid, cid;
//         fp >> rid >> cid;
//         h_rows[idx] = rid - 1;  // 转换为0-based索引
//         h_cols[idx] = cid - 1;
//     }
//     fp.close();
//
//     // 生成测试数据
//     float *h_values = new float[nnz];
//     float *h_values_gpu = new float[nnz];
//
//     // 初始化随机数种子
//     srand(42); // 使用固定种子以便结果可重现
//
//     // 初始化稠密矩阵（随机值）
//     for (int i = 0; i < M * K; i++) h_B[i] = rand() / float(RAND_MAX);
//     for (int i = 0; i < K * N; i++) h_C[i] = rand() / float(RAND_MAX);
//
//     // 生成随机稀疏矩阵坐标
//     for (int i = 0; i < nnz; i++) {
//         h_rows[i] = rand() % M;
//         h_cols[i] = rand() % N;
//     }
//
//
//     // 分配设备内存
//     float *d_B, *d_C, *d_values;
//     int *d_rows, *d_cols;
//     cudaMalloc(&d_B, M * K * sizeof(float));
//     cudaMalloc(&d_C, K * N * sizeof(float));
//     cudaMalloc(&d_rows, nnz * sizeof(int));
//     cudaMalloc(&d_cols, nnz * sizeof(int));
//     cudaMalloc(&d_values, nnz * sizeof(float));
//
//     // 拷贝数据到设备
//     cudaMemcpy(d_B, h_B, M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_C, h_C, K * N * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_rows, h_rows, nnz * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_cols, h_cols, nnz * sizeof(int), cudaMemcpyHostToDevice);
//
//     float tot_ms;
//     cudaEvent_t event1, event2;
//     cudaEventCreate(&event1);
//     cudaEventCreate(&event2);
//     cudaDeviceSynchronize();
//     cudaEventRecord(event1, 0);
//
//     // 执行SDDMM计算
//     launch_sddmm_linear_layout(d_B, d_C, d_rows, d_cols, d_values, M, N, K, nnz);
//
//     cudaEventRecord(event2, 0);
//
//     cudaEventSynchronize(event1);
//     cudaEventSynchronize(event2);
//     cudaEventElapsedTime(&tot_ms, event1, event2);
//     cudaDeviceSynchronize();
//     printf("time: %f ms\n", tot_ms);
//
//     // 检查错误
//     cudaError_t err = cudaGetLastError();
//     if (err != cudaSuccess) {
//         printf("CUDA error: %s\n", cudaGetErrorString(err));
//     }
//
//     // 拷贝结果回主机
//     cudaMemcpy(h_values_gpu, d_values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     // 打印前5和后5个结果
//     printf("\nFirst 5 elements:\n");
//     for(int i=0; i<5 && i<nnz; i++) {
//         float cpu = 0.0f;
//         for(int k=0; k<K; k++) {
//             cpu += h_B[h_rows[i]*K + k] * h_C[k*N + h_cols[i]];
//         }
//         printf("[%4d] CPU: %-9.6f  GPU: %-9.6f\n", i, cpu, h_values_gpu[i]);
//     }
//
//     printf("\nLast 5 elements:\n");
//     for(int i=max(nnz-5, 0); i<nnz; i++) {
//         float cpu = 0.0f;
//         for(int k=0; k<K; k++) {
//             cpu += h_B[h_rows[i]*K + k] * h_C[k*N + h_cols[i]];
//         }
//         printf("[%4d] CPU: %-9.6f  GPU: %-9.6f\n", i, cpu, h_values_gpu[i]);
//     }
//
//
//     // 验证结果（检查所有点）
//     int correct = 0;
//     int total_tests = min(10, nnz);
//     for (int test = 0; test < total_tests; test++) {
//         int idx = test; // 使用固定索引以便结果可重现
//         int row = h_rows[idx];
//         int col = h_cols[idx];
//
//         // CPU计算结果
//         float cpu_result = 0.0f;
//         for (int k = 0; k < K; k++) {
//             cpu_result += h_B[row * K + k] * h_C[k * N + col];
//         }
//
//         // 比较结果（允许1e-3误差）
//         float gpu_result = h_values_gpu[idx];
//         float diff = fabs(gpu_result - cpu_result);
//         bool match = (diff < 1e-3) || (fabs(gpu_result) < 1e-6 && fabs(cpu_result) < 1e-6);
//
//         if (match) {
//             correct++;
//         } else {
//             printf("Mismatch at %d: GPU=%.6f CPU=%.6f (row=%d, col=%d), diff=%.6f\n",
//                    idx, gpu_result, cpu_result, row, col, diff);
//         }
//     }
//
//     std::cout << "Validation: " << correct << "/" << total_tests << " points correct\n";
//
//     // 清理资源
//     delete[] h_B;
//     delete[] h_C;
//     delete[] h_rows;
//     delete[] h_cols;
//     delete[] h_values;
//     delete[] h_values_gpu;
//     cudaFree(d_B);
//     cudaFree(d_C);
//     cudaFree(d_rows);
//     cudaFree(d_cols);
//     cudaFree(d_values);
//
//     return 0;
// }
