// #include <cuda_runtime.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
// #include <cstdint>
// #include <cassert>
//
// // 优化后的常量定义
// const int HIGH_DENSITY_THREADS = 256;
// const int WARP_SIZE = 32;
// const int MAX_K_SHARED = 128;
//
// // 新增优化常量
// const int TILE_K = 32;           // K维度分块大小
// const int TILE_N = 64;           // N维度分块大小
// const int VECTOR_SIZE = 4;       // 向量化大小
// const int PREFETCH_DISTANCE = 2; // 预取距离
// const int MAX_STREAMS = 4;       // 最大流数量
//
//
// struct CSRMatrix {
//     int *row_ptr;
//     int *col_idx;
//     float *values;
//     int rows;
//     int cols;
//     int nnz;
// };
//
// // 内核1：计算每行非零元数量并对行进行高/低密度分类
// __global__ void compute_row_nnz_and_classify_kernel(
//     const int *row_ptr,
//     int *d_high_rows,
//     int *d_low_rows,
//     int *high_count,
//     int *low_count,
//     int rows,
//     int threshold,
//     int rows_per_thread
// ) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     for (int row = tid * rows_per_thread; row < min((tid + 1) * rows_per_thread, rows); ++row) {
//         int nnz = row_ptr[row + 1] - row_ptr[row];
//         if (nnz > threshold) {
//             int pos = atomicAdd(high_count, 1);
//             if (pos < rows) d_high_rows[pos] = row;
//         } else if (nnz > 0) {
//             // 只处理非空行
//             int pos = atomicAdd(low_count, 1);
//             if (pos < rows) d_low_rows[pos] = row;
//         }
//     }
// }
//
//
// // 优化版本1：支持大K值的分块高密度核函数
// __global__ void sddmm_high_density_tiled_kernel(
//     const float *A,
//     const float *B,
//     const int *row_ptr,
//     const int *col_idx,
//     const int *d_high_rows,
//     float *result,
//     int h_high_count,
//     int K) {
//
//     int high_row_idx = blockIdx.x;
//     if (high_row_idx >= h_high_count) return;
//
//     // 动态共享内存分配
//     extern __shared__ float shared_mem[];
//     float *a_tile = shared_mem;
//     float *b_tile = shared_mem + TILE_K;
//
//     int row = d_high_rows[high_row_idx];
//     int tid = threadIdx.x;
//     int warp_id = tid / WARP_SIZE;
//     int lane_id = tid % WARP_SIZE;
//     int warps_in_block = blockDim.x / WARP_SIZE;
//
//     int row_start = row_ptr[row];
//     int row_end = row_ptr[row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // K维度分块处理
//     for (int k_start = 0; k_start < K; k_start += TILE_K) {
//         int k_end = min(k_start + TILE_K, K);
//         int tile_k_size = k_end - k_start;
//
//         // 协作加载A的分块到共享内存
//         for (int k = tid; k < tile_k_size; k += blockDim.x) {
//             if (k_start + k < K) {
//                 a_tile[k] = A[row * K + k_start + k];
//             }
//         }
//         __syncthreads();
//
//         // 每个Warp处理行内的一部分非零元
//         for (int i = warp_id; i < nnz_in_row; i += warps_in_block) {
//             int global_idx = row_start + i;
//             int col = col_idx[global_idx];
//
//             // 使用向量化加载B矩阵数据
//             float4 partial_sum4 = make_float4(0.0f, 0.0f, 0.0f, 0.0f);
//
//             // 向量化计算点积
//             for (int k = lane_id * VECTOR_SIZE; k < tile_k_size; k += WARP_SIZE * VECTOR_SIZE) {
//                 if (k + VECTOR_SIZE <= tile_k_size) {
//                     float4 a_vec = *reinterpret_cast<const float4*>(&a_tile[k]);
//                     float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k_start + k]);
//
//                     partial_sum4.x += a_vec.x * b_vec.x;
//                     partial_sum4.y += a_vec.y * b_vec.y;
//                     partial_sum4.z += a_vec.z * b_vec.z;
//                     partial_sum4.w += a_vec.w * b_vec.w;
//                 }
//             }
//
//             // 归约向量结果
//             float partial_sum = partial_sum4.x + partial_sum4.y + partial_sum4.z + partial_sum4.w;
//
//             // Warp shuffle归约
//             for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
//                 partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
//             }
//
//             // 累加到全局结果（第一个K分块时初始化，后续分块累加）
//             if (lane_id == 0) {
//                 if (k_start == 0) {
//                     result[global_idx] = partial_sum;
//                 } else {
//                     result[global_idx] += partial_sum;
//                 }
//             }
//         }
//         __syncthreads();
//     }
// }
//
// // 原始的高密度区核函数（保留warp shuffle优化）
// __global__ void sddmm_high_density_shared_mem_kernel(
//     const float *A,
//     const float *B,
//     const int *row_ptr,
//     const int *col_idx,
//     const int *d_high_rows,
//     float *result,
//     int h_high_count,
//     int K) {
//
//     int high_row_idx = blockIdx.x;
//     if (high_row_idx >= h_high_count) return;
//
//     if (K > MAX_K_SHARED) return;
//
//     __shared__ float a_row_s[MAX_K_SHARED];
//
//     int row = d_high_rows[high_row_idx];
//     int tid_in_block = threadIdx.x;
//     int block_dim = blockDim.x;
//
//     // 协作加载A的行向量到共享内存
//     for (int k = tid_in_block; k < K; k += block_dim) {
//         a_row_s[k] = A[row * K + k];
//     }
//     __syncthreads();
//
//     int warp_id = threadIdx.x / WARP_SIZE;
//     int lane_id = threadIdx.x % WARP_SIZE;
//     int warps_in_block = blockDim.x / WARP_SIZE;
//
//     int row_start = row_ptr[row];
//     int row_end = row_ptr[row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 每个Warp处理行内的一部分非零元
//     for (int i = warp_id; i < nnz_in_row; i += warps_in_block) {
//         int global_idx = row_start + i;
//         int col = col_idx[global_idx];
//         float partial_sum = 0.0f;
//
//         // Warp内线程并行计算点积
//         for (int k = lane_id; k < K; k += WARP_SIZE) {
//             partial_sum += a_row_s[k] * B[col * K + k];
//         }
//
//         // 使用Warp Shuffle进行高效归约
//         for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
//             partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
//         }
//
//         // Warp的0号线程将最终结果写回
//         if (lane_id == 0) {
//             result[global_idx] = partial_sum;
//         }
//     }
// }
//
// // 低密度区核函数 (保持不变)
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     const int VECTOR_SIZE = 4;
//     int index = blockIdx.x * blockDim.x + threadIdx.x;
//     if (index >= num_rows) return;
//
//     int orig_row = row_indices[index];
//     const int row_start = row_ptr[orig_row];
//     const int row_end = row_ptr[orig_row + 1];
//     const int nnz_in_row = row_end - row_start;
//
//     float a_reg[MAX_K_SHARED]; // 使用与高密度区相同的常量
//     if (K > MAX_K_SHARED) return; // 安全检查
//
//     for (int k = 0; k < K; k++) {
//         a_reg[k] = A[orig_row * K + k];
//     }
//
//     for (int idx = 0; idx < nnz_in_row; idx++) {
//         int col = col_idx[row_start + idx];
//         if (col < 0 || col >= N) continue;
//
//         float sum = 0.0f;
//         int k = 0;
//
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec, b_vec;
//             memcpy(&a_vec, &a_reg[k], sizeof(float4));
//             memcpy(&b_vec, &B[col * K + k], sizeof(float4));
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y
//                     + a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//
//         for (; k < K; ++k) {
//             sum += a_reg[k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 优化版本2：双缓冲预取高密度核函数
// __global__ void sddmm_high_density_prefetch_kernel(
//     const float *A,
//     const float *B,
//     const int *row_ptr,
//     const int *col_idx,
//     const int *d_high_rows,
//     float *result,
//     int h_high_count,
//     int K) {
//
//     int high_row_idx = blockIdx.x;
//     if (high_row_idx >= h_high_count) return;
//
//     extern __shared__ float shared_mem[];
//     float *a_tile = shared_mem;
//     float *b_cache = shared_mem + TILE_K;
//
//     int row = d_high_rows[high_row_idx];
//     int tid = threadIdx.x;
//     int warp_id = tid / WARP_SIZE;
//     int lane_id = tid % WARP_SIZE;
//     int warps_in_block = blockDim.x / WARP_SIZE;
//
//     int row_start = row_ptr[row];
//     int row_end = row_ptr[row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 预取第一个A分块
//     for (int k = tid; k < min(TILE_K, K); k += blockDim.x) {
//         a_tile[k] = A[row * K + k];
//     }
//     __syncthreads();
//
//     // 双缓冲流水线处理
//     for (int k_start = 0; k_start < K; k_start += TILE_K) {
//         int k_end = min(k_start + TILE_K, K);
//         int tile_k_size = k_end - k_start;
//
//         // 预取下一个A分块（流水线）
//         int next_k_start = k_start + TILE_K;
//         if (next_k_start < K && tid < min(TILE_K, K - next_k_start)) {
//             // 使用寄存器预取下一分块
//             float prefetch_val = A[row * K + next_k_start + tid];
//
//             // 处理当前分块的同时预取下一分块
//             for (int i = warp_id; i < nnz_in_row; i += warps_in_block) {
//                 int global_idx = row_start + i;
//                 int col = col_idx[global_idx];
//
//                 float partial_sum = 0.0f;
//
//                 // 向量化点积计算
//                 for (int k = lane_id; k < tile_k_size; k += WARP_SIZE) {
//                     partial_sum += a_tile[k] * B[col * K + k_start + k];
//                 }
//
//                 // Warp shuffle归约
//                 for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
//                     partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
//                 }
//
//                 if (lane_id == 0) {
//                     if (k_start == 0) {
//                         result[global_idx] = partial_sum;
//                     } else {
//                         result[global_idx] += partial_sum;
//                     }
//                 }
//             }
//
//             // 将预取的数据写入共享内存
//             __syncthreads();
//             if (next_k_start < K) {
//                 a_tile[tid] = prefetch_val;
//             }
//         } else {
//             // 正常处理当前分块
//             for (int i = warp_id; i < nnz_in_row; i += warps_in_block) {
//                 int global_idx = row_start + i;
//                 int col = col_idx[global_idx];
//
//                 float partial_sum = 0.0f;
//
//                 for (int k = lane_id; k < tile_k_size; k += WARP_SIZE) {
//                     partial_sum += a_tile[k] * B[col * K + k_start + k];
//                 }
//
//                 for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
//                     partial_sum += __shfl_down_sync(0xFFFFFFFF, partial_sum, offset);
//                 }
//
//                 if (lane_id == 0) {
//                     if (k_start == 0) {
//                         result[global_idx] = partial_sum;
//                     } else {
//                         result[global_idx] += partial_sum;
//                     }
//                 }
//             }
//         }
//         __syncthreads();
//     }
// }
//
// // 主控制函数，调用CUDA核函数
// void sddmm_csr(
//     const float *d_A,
//     const float *d_B,
//     CSRMatrix &sparse,
//     int K
// ) {
//     int *d_high_rows, *d_low_rows;
//     int *d_high_count, *d_low_count;
//     int h_high_count = 0, h_low_count = 0;
//     int threshold = 32; // 行非零元数阈值，用于划分高/低密度
//     const int ROWS_PER_THREAD = 4;
//
//     cudaMalloc(&d_high_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_low_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_count, sizeof(int));
//     cudaMalloc(&d_low_count, sizeof(int));
//     cudaMemset(d_high_count, 0, sizeof(int));
//     cudaMemset(d_low_count, 0, sizeof(int));
//
//     // 创建多个流用于更细粒度的并行
//     cudaStream_t streams[MAX_STREAMS];
//     for (int i = 0; i < MAX_STREAMS; i++) {
//         cudaStreamCreate(&streams[i]);
//     }
//
//     cudaEvent_t step_start, step_stop;
//     cudaEventCreate(&step_start);
//     cudaEventCreate(&step_stop);
//     cudaEventRecord(step_start);
//
//     // 步骤1: 行分类
//     dim3 block(256);
//     dim3 grid((sparse.rows / ROWS_PER_THREAD + block.x - 1) / block.x);
//     compute_row_nnz_and_classify_kernel<<<grid, block, 0, streams[0]>>>(
//         sparse.row_ptr, d_high_rows, d_low_rows,
//         d_high_count, d_low_count,
//         sparse.rows, threshold, ROWS_PER_THREAD
//     );
//
//     cudaEventRecord(step_stop);
//     cudaEventSynchronize(step_stop);
//     float step_ms;
//     cudaEventElapsedTime(&step_ms, step_start, step_stop);
//     printf("[步骤1] 行分类计算耗时: %.3f ms\n", step_ms);
//
//     cudaMemcpy(&h_high_count, d_high_count, sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(&h_low_count, d_low_count, sizeof(int), cudaMemcpyDeviceToHost);
//
//     // 步骤2: 高密度区计算 - 使用多种优化策略
//     cudaEvent_t step2_start, step2_stop;
//     cudaEventCreate(&step2_start);
//     cudaEventCreate(&step2_stop);
//     cudaEventRecord(step2_start, streams[0]);
//
//     if (h_high_count > 0) {
//         dim3 block_hd(HIGH_DENSITY_THREADS);
//         dim3 grid_hd(h_high_count);
//
//         // 根据K值选择不同的优化策略
//         if (K <= MAX_K_SHARED) {
//             // 小K值：使用原始共享内存方法（保留warp shuffle优化）
//             sddmm_high_density_shared_mem_kernel<<<grid_hd, block_hd, 0, streams[0]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx,
//                 d_high_rows, sparse.values, h_high_count, K
//             );
//         } else {
//             // 大K值：使用分块策略
//             size_t shared_mem_size = (TILE_K + TILE_N) * sizeof(float);
//             sddmm_high_density_tiled_kernel<<<grid_hd, block_hd, shared_mem_size, streams[0]>>>(
//                 d_A, d_B, sparse.row_ptr, sparse.col_idx,
//                 d_high_rows, sparse.values, h_high_count, K
//             );
//         }
//     }
//
//     cudaEventRecord(step2_stop, streams[0]);
//     cudaEventSynchronize(step2_stop);
//     float step2_ms;
//     cudaEventElapsedTime(&step2_ms, step2_start, step2_stop);
//     printf("[步骤2] 高密度区计算耗时: %.3f ms\n", step2_ms);
//
//     // 步骤3: 低密度区计算 - 使用多流并行
//     cudaEvent_t step3_start, step3_stop;
//     cudaEventCreate(&step3_start);
//     cudaEventCreate(&step3_stop);
//     cudaEventRecord(step3_start, streams[1]);
//
//     if (h_low_count > 0) {
//         // 将低密度行分配到多个流中并行处理
//         int rows_per_stream = (h_low_count + MAX_STREAMS - 2) / (MAX_STREAMS - 1);
//
//         for (int stream_id = 1; stream_id < MAX_STREAMS; stream_id++) {
//             int start_row = (stream_id - 1) * rows_per_stream;
//             int end_row = min(start_row + rows_per_stream, h_low_count);
//
//             if (start_row < end_row) {
//                 dim3 block_ld(256);
//                 dim3 grid_ld((end_row - start_row + block_ld.x - 1) / block_ld.x);
//
//                 // 使用偏移指针处理不同的行段
//                 sddmm_low_density_kernel<<<grid_ld, block_ld, 0, streams[stream_id]>>>(
//                     d_A, d_B, sparse.row_ptr, sparse.col_idx,
//                     d_low_rows + start_row, sparse.values,
//                     sparse.rows, K, sparse.cols, end_row - start_row
//                 );
//             }
//         }
//     }
//
//     // 同步所有流
//     for (int i = 1; i < MAX_STREAMS; i++) {
//         cudaStreamSynchronize(streams[i]);
//     }
//
//     cudaEventRecord(step3_stop, streams[1]);
//     cudaEventSynchronize(step3_stop);
//     float step3_ms;
//     cudaEventElapsedTime(&step3_ms, step3_start, step3_stop);
//     printf("[步骤3] 低密度区计算耗时: %.3f ms\n", step3_ms);
//
//     // 释放资源
//     cudaFree(d_high_rows);
//     cudaFree(d_low_rows);
//     cudaFree(d_high_count);
//     cudaFree(d_low_count);
//
//     for (int i = 0; i < MAX_STREAMS; i++) {
//         cudaStreamDestroy(streams[i]);
//     }
// }
//
// // CPU参考实现 (用于验证)
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// // 从.mtx文件加载矩阵
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     while (std::getline(file, line) && line[0] == '%');
//
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         // .mtx文件可能包含值，也可能不包含，我们只关心结构
//         file >> row >> col;
//         std::getline(file, line); // 读取行尾的任何内容
//         rows[i] = row - 1; // 1-based to 0-based
//         cols[i] = col - 1; // 1-based to 0-based
//     }
//     file.close();
// }
//
// // COO转CSR格式
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
//     csr_row_ptr.assign(M + 1, 0);
//     csr_col_idx.resize(nnz);
//     std::vector<int> temp_cols(nnz);
//
//     // 计算每行的非零元个数
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[rows[i] + 1]++;
//     }
//
//     // 计算行指针前缀和
//     for (int i = 0; i < M; ++i) {
//         csr_row_ptr[i + 1] += csr_row_ptr[i];
//     }
//
//     // 填充col_idx
//     std::vector<int> row_count = csr_row_ptr;
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int index = row_count[row];
//         csr_col_idx[index] = cols[i];
//         row_count[row]++;
//     }
// }
//
//
// __global__ void warmup_kernel() {
// }
//
// // 主函数
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "Usage: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr));
//     std::string filename = argv[1];
//     int K = (argc > 2) ? std::atoi(argv[2]) : 128;
//
//     if (K > MAX_K_SHARED) {
//         std::cerr << "Error: K=" << K << " exceeds MAX_K_SHARED=" << MAX_K_SHARED
//                 << ". Please recompile with a larger MAX_K_SHARED or choose a smaller K." << std::endl;
//         return 1;
//     }
//
//     int M, N, nnz;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, nnz, h_csr_row_ptr, h_csr_col_idx);
//
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     for (int i = 0; i < M * K; ++i) h_A[i] = (rand() % 100) / 100.0f;
//     for (int i = 0; i < N * K; ++i) h_B[i] = (rand() % 100) / 100.0f;
//
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//
//     cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 预热GPU
//     std::cout << "预热GPU..." << std::endl;
//     warmup_kernel<<<1, 1>>>();
//     cudaDeviceSynchronize();
//
//     std::cout << "开始执行SDDMM..." << std::endl;
//     printf("Matrix: %s, M=%d, N=%d, K=%d, nnz=%d\n", filename.c_str(), M, N, K, nnz);
//
//
//     cudaEvent_t start_total, stop_total;
//     cudaEventCreate(&start_total);
//     cudaEventCreate(&stop_total);
//     cudaEventRecord(start_total);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop_total);
//     cudaEventSynchronize(stop_total);
//     float ms_total;
//     cudaEventElapsedTime(&ms_total, start_total, stop_total);
//     printf("SDDMM总计算时间: %.3f ms\n", ms_total);
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     std::vector<float> h_values_gpu(nnz);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     std::vector<float> h_values_cpu(nnz, 0.0f);
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//
//     int correct = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_cpu[i] - h_values_gpu[i]);
//         if (diff < 1e-4) {
//             correct++;
//         }
//         if (diff > max_error) {
//             max_error = diff;
//         }
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct / nnz) << "%" << std::endl;
//
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//
//     return 0;
// }
//
// // warp shuffle+共享内存
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 预热GPU...
// // 开始执行SDDMM...
// // Matrix: /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx, M=12471, N=872622, K=128, nnz=22624727
// // [步骤1] 行分类计算耗时: 0.065 ms
// // [步骤2] 高密度区计算耗时: 29.570 ms
// // [步骤3] 低密度区计算耗时: 1.890 ms
// // SDDMM总计算时间: 31.924 ms
// // SDDMM执行完成
// // 验证结果:
// // 最大绝对误差: 3.05176e-05
// // 正确率: 100%
// //
// // 进程已结束，退出代码为 0
