#!/bin/bash

echo "=========================================="
echo "SDDMM 优化版本编译和运行脚本"
echo "=========================================="

# 检查CUDA环境
if ! command -v nvcc &> /dev/null; then
    echo "错误: 未找到nvcc编译器，请确保CUDA已正确安装"
    exit 1
fi

echo "CUDA版本:"
nvcc --version | grep "release"

# 编译程序
echo ""
echo "编译SDDMM优化版本..."
nvcc -o sddmm_optimized sddmm_complete_optimized.cu -O3 -arch=sm_70 -std=c++11

if [ $? -eq 0 ]; then
    echo "✓ 编译成功"
else
    echo "✗ 编译失败"
    exit 1
fi

# 检查测试数据
echo ""
echo "检查测试数据..."
TEST_MATRIX=""
if [ -f "dataset/12month1.mtx" ]; then
    TEST_MATRIX="dataset/12month1.mtx"
    echo "✓ 找到测试矩阵: $TEST_MATRIX"
elif [ -f "dataset/nips.mtx" ]; then
    TEST_MATRIX="dataset/nips.mtx"
    echo "✓ 找到测试矩阵: $TEST_MATRIX"
else
    echo "创建小型测试矩阵..."
    mkdir -p dataset
    
    # 创建一个小的测试矩阵
    cat > dataset/test_small.mtx << 'EOF'
%%MatrixMarket matrix coordinate real general
1000 1000 10000
EOF
    
    # 生成随机稀疏矩阵数据
    for i in {1..10000}; do
        row=$((RANDOM % 1000 + 1))
        col=$((RANDOM % 1000 + 1))
        val="1.0"
        echo "$row $col $val" >> dataset/test_small.mtx
    done
    
    TEST_MATRIX="dataset/test_small.mtx"
    echo "✓ 创建了测试矩阵: $TEST_MATRIX"
fi

# 运行测试
echo ""
echo "运行SDDMM优化版本测试..."
echo "=========================================="

# 测试不同的K值
K_VALUES=(64 128 256)

for K in "${K_VALUES[@]}"; do
    echo ""
    echo "测试 K=$K:"
    echo "----------------------------------------"
    ./sddmm_optimized "$TEST_MATRIX" $K
    echo ""
done

echo "=========================================="
echo "测试完成"
echo "=========================================="

# 使用说明
echo ""
echo "使用说明:"
echo "1. 直接运行: ./sddmm_optimized <matrix_file.mtx> [K]"
echo "2. 示例: ./sddmm_optimized dataset/12month1.mtx 128"
echo "3. 可以从 https://sparse.tamu.edu/ 下载更多测试矩阵"
echo ""
echo "优化特性:"
echo "- 保留原有warp shuffle优化"
echo "- 支持大K值的分块策略"
echo "- 向量化计算(float4)"
echo "- 多流并行处理"
echo "- 自适应策略选择"
