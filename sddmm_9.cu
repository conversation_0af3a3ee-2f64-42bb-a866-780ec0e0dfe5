// #include <cuda_runtime.h>
// #include <cublas_v2.h>
// #include <vector>
// #include <unordered_map>
// #include <algorithm>
// #include <iostream>
// #include <cuda_fp16.h>
//
// // 计算64位整数的汉明距离
// __device__ __host__ inline int hamming_distance(uint64_t a, uint64_t b) {
//     uint64_t x = a ^ b;
// #ifdef __CUDA_ARCH__ // 设备端使用内置函数
//     return __popcll(x);
// #else // 主机端使用位操作
//     int dist = 0;
//     while (x) {
//         dist++;
//         x &= x - 1;
//     }
//     return dist;
// #endif
// }
//
// // GPU核函数：初始化矩阵为负无穷大
// __global__ void init_matrix_kernel(float *matrix, int rows, int cols, float value) {
//     int row = blockIdx.y * blockDim.y + threadIdx.y;
//     int col = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows && col < cols) {
//         matrix[row * cols + col] = value;
//     }
// }
//
// // GPU核函数：将块结果散射到全局矩阵
// __global__ void scatter_results_kernel(
//     const float *block_result,
//     float *global_matrix,
//     const int *q_indices,
//     const int *k_indices,
//     int block_rows,
//     int block_cols,
//     int global_cols
// ) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     int idy = blockIdx.y * blockDim.y + threadIdx.y;
//
//     if (idx < block_cols && idy < block_rows) {
//         int global_row = q_indices[idy];
//         int global_col = k_indices[idx];
//         float value = block_result[idy * block_cols + idx];
//         global_matrix[global_row * global_cols + global_col] = value;
//     }
// }
//
// // 优化的GEMM核函数（使用共享内存和向量化）
// __global__ void gemv_kernel(
//     const float *Q, // [num_queries, dim]
//     const float *K, // [num_keys, dim]
//     float *output, // [q_size, k_size]
//     const int *q_indices, // [q_size]
//     const int *k_indices, // [k_size]
//     int dim, int q_size, int k_size
// ) {
//     extern __shared__ float shared_mem[]; // 动态共享内存
//
//     // 共享内存布局：
//     // 前半部分: 当前线程块处理的keys [blockDim.y * dim]
//     // 后半部分: 当前线程块处理的query [blockDim.x * dim]
//     float *shared_keys = shared_mem;
//     float *shared_query = shared_mem + blockDim.y * dim;
//
//     int local_q_idx = threadIdx.x; // 每个线程处理一个query
//     int local_k_idx = threadIdx.y; // 每个线程处理多个keys
//
//     int global_q_idx = (blockIdx.x * blockDim.x + threadIdx.x);
//     if (global_q_idx >= q_size) return;
//
//     // 获取全局query索引和指针
//     int q_global_idx = q_indices[global_q_idx];
//     const float *q_ptr = Q + q_global_idx * dim;
//
//     // 协作加载当前query到共享内存
//     for (int d = local_k_idx; d < dim; d += blockDim.y) {
//         shared_query[local_q_idx * dim + d] = q_ptr[d];
//     }
//
//     // 结果累加器
//     float results[8] = {0}; // 每个线程计算8个key的结果
//
//     // 遍历key的block
//     for (int kb = 0; kb < k_size; kb += blockDim.y) {
//         int global_k_idx = kb + local_k_idx;
//         __syncthreads();
//
//         // 协作加载当前key块到共享内存
//         if (global_k_idx < k_size) {
//             int k_global_idx = k_indices[global_k_idx];
//             const float *k_ptr = K + k_global_idx * dim;
//             for (int d = threadIdx.x; d < dim; d += blockDim.x) {
//                 shared_keys[local_k_idx * dim + d] = k_ptr[d];
//             }
//         }
//         __syncthreads();
//
//         // 计算当前key块的点积
//         for (int i = 0; i < min(blockDim.y, k_size - kb); i++) {
//             float sum = 0.0f;
// #pragma unroll(4)
//             for (int d = 0; d < dim; d++) {
//                 sum += shared_query[local_q_idx * dim + d] *
//                         shared_keys[i * dim + d];
//             }
//
//             if (kb + i < k_size) {
//                 results[i] = sum;
//             }
//         }
//
//         // 存储当前key块的结果
//         for (int i = 0; i < min(blockDim.y, k_size - kb); i++) {
//             if (kb + i < k_size) {
//                 output[global_q_idx * k_size + (kb + i)] = results[i];
//             }
//         }
//     }
// }
//
// // 半精度优化的GEMM核函数（支持Tensor Core）
// #if __CUDA_ARCH__ >= 700
// __global__ void gemv_kernel_half(
//     const __half* Q,          // [num_queries, dim]
//     const __half* K,          // [num_keys, dim]
//     __half* output,           // [q_size, k_size]
//     const int* q_indices,    // [q_size]
//     const int* k_indices,    // [k_size]
//     int dim, int q_size, int k_size
// ) {
//     extern __shared__ __half shared_mem[];
//     __half* shared_keys = shared_mem;
//     __half* shared_query = shared_mem + blockDim.y * dim;
//
//     int local_q_idx = threadIdx.x;
//     int local_k_idx = threadIdx.y;
//     int global_q_idx = blockIdx.x * blockDim.x + threadIdx.x;
//
//     if (global_q_idx >= q_size) return;
//
//     // 获取全局query索引和指针
//     int q_global_idx = q_indices[global_q_idx];
//     const __half* q_ptr = Q + q_global_idx * dim;
//
//     // 协作加载当前query到共享内存
//     for (int d = local_k_idx; d < dim; d += blockDim.y) {
//         shared_query[local_q_idx * dim + d] = q_ptr[d];
//     }
//
//     // 结果累加器
//     __half results[8] = {__float2half(0.0f)};
//
//     // 使用half2向量化加载
//     const int vec_dim = dim / 2;
//
//     // 遍历key的block
//     for (int kb = 0; kb < k_size; kb += blockDim.y) {
//         int global_k_idx = kb + local_k_idx;
//         __syncthreads();
//
//         // 协作加载当前key块到共享内存
//         if (global_k_idx < k_size) {
//             int k_global_idx = k_indices[global_k_idx];
//             const __half* k_ptr = K + k_global_idx * dim;
//
//             // 使用half2向量化加载
//             for (int d = threadIdx.x; d < vec_dim; d += blockDim.x) {
//                 int idx = d * 2;
//                 half2 val = *reinterpret_cast<const half2*>(k_ptr + idx);
//                 *reinterpret_cast<half2*>(&shared_keys[local_k_idx * dim + idx]) = val;
//             }
//         }
//         __syncthreads();
//
//         // 使用向量化计算点积
//         for (int i = 0; i < min(blockDim.y, k_size - kb); i++) {
//             __half sum = __float2half(0.0f);
//             for (int d = 0; d < vec_dim; d++) {
//                 int idx = d * 2;
//                 half2 q_val = *reinterpret_cast<half2*>(&shared_query[local_q_idx * dim + idx]);
//                 half2 k_val = *reinterpret_cast<half2*>(&shared_keys[i * dim + idx]);
//                 sum = __hadd(sum, __hadd(__hmul2(q_val, k_val).x);
//                 sum = __hadd(sum, __hadd(__hmul2(q_val, k_val).y);
//             }
//
//             if (kb + i < k_size) {
//                 results[i] = sum;
//             }
//         }
//
//         // 存储当前key块的结果
//         for (int i = 0; i < min(blockDim.y, k_size - kb); i++) {
//             if (kb + i < k_size) {
//                 output[global_q_idx * k_size + (kb + i)] = results[i];
//             }
//         }
//     }
// }
// #endif
//
// // 哈希桶结构
// struct HashBucket {
//     std::vector<int> indices;
//     uint64_t hash_value;
// };
//
// // 小桶核函数（简单实现）
// __global__ void small_bucket_kernel(
//     const float *Q,
//     const float *K,
//     float *output,
//     const int *q_indices,
//     const int *k_indices,
//     int dim, int q_size, int k_size
// ) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     int stride = blockDim.x * gridDim.x;
//
//     for (int idx = tid; idx < q_size * k_size; idx += stride) {
//         int q_idx = idx / k_size;
//         int k_idx = idx % k_size;
//
//         int global_q_idx = q_indices[q_idx];
//         int global_k_idx = k_indices[k_idx];
//
//         float sum = 0.0f;
//         for (int d = 0; d < dim; d++) {
//             sum += Q[global_q_idx * dim + d] * K[global_k_idx * dim + d];
//         }
//         output[idx] = sum;
//     }
// }
//
//
// // 主函数：哈希引导的SDDMM
// cudaError_t hashed_sddmm(
//     const float *d_Q, // GPU上的Q矩阵 [num_queries, dim]
//     const float *d_K, // GPU上的K矩阵 [num_keys, dim]
//     const uint64_t *q_hashes, // CPU上的Q哈希值
//     const uint64_t *k_hashes, // CPU上的K哈希值
//     float *d_output, // GPU输出矩阵 [num_queries, num_keys]
//     int num_queries,
//     int num_keys,
//     int dim,
//     int hash_length, // 哈希值长度（比特）
//     int hamming_threshold, // 最大汉明距离阈值
//     float negative_infinity, // 用于填充的值（如-1e9）
//     bool use_half_precision = false, // 是否使用半精度
//     int min_bucket_size = 32 // 最小桶大小（使用GEMM的最小规模）
// ) {
//     cublasHandle_t cublas_handle;
//     cublasStatus_t status = cublasCreate(&cublas_handle);
//     if (status != CUBLAS_STATUS_SUCCESS) {
//         return cudaErrorInitializationError;
//     }
//
//     // 1. 初始化输出矩阵为负无穷大
//     dim3 blockDim(16, 16);
//     dim3 gridDim((num_keys + 15) / 16, (num_queries + 15) / 16);
//     init_matrix_kernel<<<gridDim, blockDim>>>(d_output, num_queries, num_keys, negative_infinity);
//     cudaError_t err = cudaDeviceSynchronize();
//     if (err != cudaSuccess) return err;
//
//     // 2. CPU端创建哈希桶（Q）
//     std::unordered_map<uint64_t, HashBucket> q_buckets_map;
//     for (int i = 0; i < num_queries; ++i) {
//         q_buckets_map[q_hashes[i]].indices.push_back(i);
//         q_buckets_map[q_hashes[i]].hash_value = q_hashes[i];
//     }
//
//     // 3. CPU端创建哈希桶（K）
//     std::unordered_map<uint64_t, HashBucket> k_buckets_map;
//     for (int j = 0; j < num_keys; ++j) {
//         k_buckets_map[k_hashes[j]].indices.push_back(j);
//         k_buckets_map[k_hashes[j]].hash_value = k_hashes[j];
//     }
//
//     // 4. 提取桶到向量
//     std::vector<HashBucket> q_buckets;
//     for (auto &pair: q_buckets_map) {
//         q_buckets.push_back(std::move(pair.second));
//     }
//
//     std::vector<HashBucket> k_buckets;
//     for (auto &pair: k_buckets_map) {
//         k_buckets.push_back(std::move(pair.second));
//     }
//
//     // 5. 计算桶之间的相似关系
//     std::vector<std::pair<int, int> > similar_buckets;
//     for (int i = 0; i < q_buckets.size(); ++i) {
//         for (int j = 0; j < k_buckets.size(); ++j) {
//             if (hamming_distance(q_buckets[i].hash_value,
//                                  k_buckets[j].hash_value) <= hamming_threshold) {
//                 similar_buckets.emplace_back(i, j);
//             }
//         }
//     }
//
//     // 6. 为每个相似桶对执行GEMM
//     for (const auto &bucket_pair: similar_buckets) {
//         const auto &q_bucket = q_buckets[bucket_pair.first];
//         const auto &k_bucket = k_buckets[bucket_pair.second];
//
//         int q_size = q_bucket.indices.size();
//         int k_size = k_bucket.indices.size();
//
//         if (q_size == 0 || k_size == 0) continue;
//
//         // 7. 在GPU上分配临时内存
//         float *d_block_result = nullptr;
//         __half *d_block_result_half = nullptr;
//         int *d_q_indices = nullptr;
//         int *d_k_indices = nullptr;
//
//         cudaMalloc(&d_q_indices, q_size * sizeof(int));
//         cudaMemcpyAsync(d_q_indices, q_bucket.indices.data(),
//                         q_size * sizeof(int), cudaMemcpyHostToDevice);
//
//         cudaMalloc(&d_k_indices, k_size * sizeof(int));
//         cudaMemcpyAsync(d_k_indices, k_bucket.indices.data(),
//                         k_size * sizeof(int), cudaMemcpyHostToDevice);
//
//         if (use_half_precision) {
// #if __CUDA_ARCH__ >= 700
//             cudaMalloc(&d_block_result_half, q_size * k_size * sizeof(__half));
// #else
//             use_half_precision = false; // 回退到单精度
// #endif
//         }
//
//         if (!use_half_precision) {
//             cudaMalloc(&d_block_result, q_size * k_size * sizeof(float));
//         }
//
//         // 8. 选择核函数类型
//         if (q_size >= min_bucket_size && k_size >= min_bucket_size) {
//             // 大桶：使用优化的GEMM核函数
//             dim3 gemmBlock(32, 8); // 256 threads
//             dim3 gemmGrid((q_size + 31) / 32);
//             size_t shared_mem_size = (32 * dim + 8 * dim) * sizeof(float);
//
//             if (use_half_precision) {
// #if __CUDA_ARCH__ >= 700
//                 shared_mem_size = (32 * dim + 8 * dim) * sizeof(__half);
//                 gemv_kernel_half<<<gemmGrid, gemmBlock, shared_mem_size>>>(
//                     reinterpret_cast<const __half*>(d_Q),
//                     reinterpret_cast<const __half*>(d_K),
//                     d_block_result_half,
//                     d_q_indices, d_k_indices,
//                     dim, q_size, k_size
//                 );
// #endif
//             } else {
//                 gemv_kernel<<<gemmGrid, gemmBlock, shared_mem_size>>>(
//                     d_Q, d_K, d_block_result,
//                     d_q_indices, d_k_indices,
//                     dim, q_size, k_size
//                 );
//             }
//         } else {
//             // 小桶：使用简单实现
//             dim3 gemmBlock(256);
//             dim3 gemmGrid((q_size + 255) / 256);
//
//             if (use_half_precision) {
// #if __CUDA_ARCH__ >= 700
//                 small_bucket_kernel_half<<<gemmGrid, gemmBlock>>>(
//                     reinterpret_cast<const __half*>(d_Q),
//                     reinterpret_cast<const __half*>(d_K),
//                     d_block_result_half,
//                     d_q_indices, d_k_indices,
//                     dim, q_size, k_size
//                 );
// #endif
//             } else {
//                 small_bucket_kernel<<<gemmGrid, gemmBlock>>>(
//                     d_Q, d_K, d_block_result,
//                     d_q_indices, d_k_indices,
//                     dim, q_size, k_size
//                 );
//             }
//         }
//
//         err = cudaGetLastError();
//         if (err != cudaSuccess) {
//             cudaFree(d_q_indices);
//             cudaFree(d_k_indices);
//             if (d_block_result) cudaFree(d_block_result);
//             if (d_block_result_half) cudaFree(d_block_result_half);
//             return err;
//         }
//
//         // 9. 将结果散射到全局矩阵
//         dim3 scatterBlocks(16, 16);
//         dim3 scatterGrid(
//             (k_size + 15) / 16,
//             (q_size + 15) / 16
//         );
//
//         if (use_half_precision) {
// #if __CUDA_ARCH__ >= 700
//             // 转换半精度结果到单精度
//             float* d_temp_float;
//             cudaMalloc(&d_temp_float, q_size * k_size * sizeof(float));
//             convert_half_to_float<<<(q_size*k_size+255)/256, 256>>>(
//                 d_block_result_half, d_temp_float, q_size*k_size);
//             scatter_results_kernel<<<scatterGrid, scatterBlocks>>>(
//                 d_temp_float, d_output,
//                 d_q_indices, d_k_indices,
//                 q_size, k_size, num_keys
//             );
//             cudaFree(d_temp_float);
// #endif
//         } else {
//             scatter_results_kernel<<<scatterGrid, scatterBlocks>>>(
//                 d_block_result, d_output,
//                 d_q_indices, d_k_indices,
//                 q_size, k_size, num_keys
//             );
//         }
//
//         // 10. 清理临时内存
//         cudaFree(d_q_indices);
//         cudaFree(d_k_indices);
//         if (d_block_result) cudaFree(d_block_result);
//         if (d_block_result_half) cudaFree(d_block_result_half);
//     }
//
//     cublasDestroy(cublas_handle);
//     return cudaSuccess;
// }
//
//
// // 半精度转换核函数
// #if __CUDA_ARCH__ >= 700
// __global__ void convert_half_to_float(const __half* input, float* output, int n) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (idx < n) {
//         output[idx] = __half2float(input[idx]);
//     }
// }
//
// __global__ void small_bucket_kernel_half(
//     const __half* Q,
//     const __half* K,
//     __half* output,
//     const int* q_indices,
//     const int* k_indices,
//     int dim, int q_size, int k_size
// ) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     int stride = blockDim.x * gridDim.x;
//
//     for (int idx = tid; idx < q_size * k_size; idx += stride) {
//         int q_idx = idx / k_size;
//         int k_idx = idx % k_size;
//
//         int global_q_idx = q_indices[q_idx];
//         int global_k_idx = k_indices[k_idx];
//
//         __half sum = __float2half(0.0f);
//         for (int d = 0; d < dim; d++) {
//             sum = __hadd(sum, __hmul(
//                 Q[global_q_idx * dim + d],
//                 K[global_k_idx * dim + d]
//             ));
//         }
//         output[idx] = sum;
//     }
// }
// #endif
//
// int main() {
//     // 参数设置
//     const int num_queries = 10000;
//     const int num_keys = 10000;
//     const int dim = 128;
//     const int hash_length = 64;
//     const int hamming_threshold = 3;
//     const float negative_infinity = -1e9f;
//     const bool use_half_precision = true;
//     const int min_bucket_size = 32;
//
//     // 分配GPU内存
//     float *d_Q, *d_K, *d_output;
//     cudaMalloc(&d_Q, num_queries * dim * sizeof(float));
//     cudaMalloc(&d_K, num_keys * dim * sizeof(float));
//     cudaMalloc(&d_output, num_queries * num_keys * sizeof(float));
//
//     // 生成随机哈希值（实际应从spherical hashing获取）
//     std::vector<uint64_t> q_hashes(num_queries);
//     std::vector<uint64_t> k_hashes(num_keys);
//     // ... 填充哈希值 ...
//
//     float tot_ms, total_d2h_time;
//     cudaEvent_t event1, event2;
//     cudaEventCreate(&event1);
//     cudaEventCreate(&event2);
//     cudaDeviceSynchronize();
//     cudaEventRecord(event1, 0);
//
//     // 执行优化后的SDDMM
//     cudaError_t err = hashed_sddmm(
//         d_Q, d_K,
//         q_hashes.data(), k_hashes.data(),
//         d_output,
//         num_queries, num_keys, dim,
//         hash_length, hamming_threshold,
//         negative_infinity,
//         use_half_precision,
//         min_bucket_size
//     );
//
//     cudaEventRecord(event2, 0);
//
//     cudaEventSynchronize(event1);
//     cudaEventSynchronize(event2);
//     cudaEventElapsedTime(&tot_ms, event1, event2);
//     cudaDeviceSynchronize();
//     printf("time: %f ms\n", tot_ms);
//
//     if (err != cudaSuccess) {
//         std::cerr << "SDDMM failed: " << cudaGetErrorString(err) << std::endl;
//     }
//
//     // 清理内存
//     cudaFree(d_Q);
//     cudaFree(d_K);
//     cudaFree(d_output);
//
//     return 0;
// }
