// #include <cuda_runtime.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
//
// // 常量定义
// const int TILE_SIZE = 32; // 分块大小
// const int VECTOR_SIZE = 4; // 向量化加载大小(float4)
// const int HIGH_DENSITY_THREADS = 256; // 高密度区线程数/块
//
// // 稀疏矩阵结构体 (CSR格式)
// struct CSRMatrix {
//     int *row_ptr; // 行指针
//     int *col_idx; // 列索引
//     float *values; // 非零值 (SDDMM结果存放位置)
//     int rows; // 矩阵行数
//     int cols; // 矩阵列数
//     int nnz; // 非零元数量
// };
//
// // GPU端行非零元统计内核
// __global__ void compute_row_nnz_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int rows
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         row_nnz[row] = row_ptr[row + 1] - row_ptr[row];
//     }
// }
//
// // 高密度区SDDMM内核 (每行非零元 > 32) - 使用共享内存优化
// __global__ void sddmm_high_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     extern __shared__ float shared_A[]; // 动态共享内存
//
//     int row_in_group = blockIdx.x;
//     if (row_in_group >= num_rows) return;
//
//     int orig_row = row_indices[row_in_group];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     int tid = threadIdx.x;
//     int num_threads = blockDim.x;
//
//     // 协作加载A矩阵行到共享内存
//     for (int k = tid; k < K; k += num_threads) {
//         shared_A[k] = A[orig_row * K + k];
//     }
//     __syncthreads(); // 确保所有线程完成加载
//
//     // 处理该行的所有非零元素
//     for (int idx = tid; idx < nnz_in_row; idx += num_threads) {
//         int col = col_idx[row_start + idx];
//         float sum = 0.0f;
//         int k = 0;
//
//         // 向量化处理主循环
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4 *>(&shared_A[k]);
//             float4 b_vec = *reinterpret_cast<const float4 *>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x;
//             sum += a_vec.y * b_vec.y;
//             sum += a_vec.z * b_vec.z;
//             sum += a_vec.w * b_vec.w;
//         }
//
//         // 标量处理剩余部分
//         for (; k < K; ++k) {
//             sum += shared_A[k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 低密度区SDDMM内核 (每行非零元 ≤ 32)
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices, // 低密度行索引
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int index = blockIdx.x * blockDim.x + threadIdx.x;
//     if (index >= num_rows) return;
//
//     int orig_row = row_indices[index];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 寄存器缓存A矩阵行
//     float a_reg[32];
//     for (int k = 0; k < K; k++) {
//         a_reg[k] = A[orig_row * K + k];
//     }
//
//     for (int idx = 0; idx < nnz_in_row; idx++) {
//         int col = col_idx[row_start + idx];
//         float sum = 0.0f;
//         int k = 0;
//
//         // 向量化处理
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4 *>(&a_reg[k]);
//             float4 b_vec = *reinterpret_cast<const float4 *>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x;
//             sum += a_vec.y * b_vec.y;
//             sum += a_vec.z * b_vec.z;
//             sum += a_vec.w * b_vec.w;
//         }
//
//         // 标量处理剩余部分
//         for (; k < K; ++k) {
//             sum += a_reg[k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 主SDDMM函数
// void sddmm_csr(
//     const float *d_A, // 设备端稠密矩阵A (MxK)
//     const float *d_B, // 设备端稠密矩阵B (KxN)
//     CSRMatrix &sparse, // 稀疏矩阵结构 (CSR格式)
//     int K // 公共维度
// ) {
//     // 步骤1计时事件
//     cudaEvent_t step1_start, step1_stop;
//     cudaEventCreate(&step1_start);
//     cudaEventCreate(&step1_stop);
//     cudaEventRecord(step1_start);
//
//     // 1. 统计每行非零元素数量
//     int *d_row_nnz;
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//
//     dim3 block(256);
//     dim3 grid((sparse.rows + block.x - 1) / block.x);
//     compute_row_nnz_kernel<<<grid, block>>>(sparse.row_ptr, d_row_nnz, sparse.rows);
//     cudaDeviceSynchronize();
//
//     cudaEventRecord(step1_stop);
//     cudaEventSynchronize(step1_stop);
//     float step1_ms;
//     cudaEventElapsedTime(&step1_ms, step1_start, step1_stop);
//     printf("[步骤1] 行非零元统计耗时: %.3f ms\n", step1_ms);
//
//     // 步骤2计时事件
//     cudaEvent_t step2_start, step2_stop;
//     cudaEventCreate(&step2_start);
//     cudaEventCreate(&step2_stop);
//     cudaEventRecord(step2_start);
//
//     // 2. 动态分区: 将行分为高密度区(>32)和低密度区(<=32)
//     int *h_row_nnz = new int[sparse.rows];
//     cudaMemcpy(h_row_nnz, d_row_nnz, sparse.rows * sizeof(int), cudaMemcpyDeviceToHost);
//
//     std::vector<int> high_density_rows;
//     std::vector<int> low_density_rows;
//     int threshold = 256;
//
//     for (int i = 0; i < sparse.rows; ++i) {
//         if (h_row_nnz[i] > threshold) {
//             high_density_rows.push_back(i);
//         } else {
//             low_density_rows.push_back(i);
//         }
//     }
//
//     int high_row_count = high_density_rows.size();
//     int low_row_count = low_density_rows.size();
//
//     // 复制行索引到设备端
//     int *d_high_density_rows = nullptr;
//     int *d_low_density_rows = nullptr;
//
//     if (high_row_count > 0) {
//         cudaMalloc(&d_high_density_rows, high_row_count * sizeof(int));
//         cudaMemcpy(d_high_density_rows, high_density_rows.data(), high_row_count * sizeof(int), cudaMemcpyHostToDevice);
//     }
//     if (low_row_count > 0) {
//         cudaMalloc(&d_low_density_rows, low_row_count * sizeof(int));
//         cudaMemcpy(d_low_density_rows, low_density_rows.data(), low_row_count * sizeof(int), cudaMemcpyHostToDevice);
//     }
//
//     delete[] h_row_nnz;
//     cudaFree(d_row_nnz);
//
//     cudaEventRecord(step2_stop);
//     cudaEventSynchronize(step2_stop);
//     float step2_ms;
//     cudaEventElapsedTime(&step2_ms, step2_start, step2_stop);
//     printf("[步骤2] 动态分区耗时: %.3f ms (高密度行: %d, 低密度行: %d)\n", step2_ms, high_row_count, low_row_count);
//
//     // 步骤3+4: 并行执行高/低密度区计算
//     cudaEvent_t step3_start, step3_stop, step4_start, step4_stop;
//     cudaEventCreate(&step3_start);
//     cudaEventCreate(&step3_stop);
//     cudaEventCreate(&step4_start);
//     cudaEventCreate(&step4_stop);
//
//     cudaStream_t stream_high, stream_low;
//     cudaStreamCreate(&stream_high);
//     cudaStreamCreate(&stream_low);
//
//     // 高密度区计算 (异步)
//     if (high_row_count > 0) {
//         dim3 block_hd(HIGH_DENSITY_THREADS);
//         dim3 grid_hd(high_row_count);
//         size_t shared_mem_size = K * sizeof(float);
//
//         cudaEventRecord(step3_start, stream_high);
//         sddmm_high_density_kernel<<<grid_hd, block_hd, shared_mem_size, stream_high>>>(
//             d_A, d_B,
//             sparse.row_ptr,
//             sparse.col_idx,
//             d_high_density_rows,
//             sparse.values,
//             sparse.rows, K, sparse.cols,
//             high_row_count
//         );
//         cudaEventRecord(step3_stop, stream_high);
//     }
//
//     // 低密度区计算 (异步)
//     if (low_row_count > 0) {
//         dim3 block_ld(256);
//         dim3 grid_ld((low_row_count + block_ld.x - 1) / block_ld.x);
//
//         cudaEventRecord(step4_start, stream_low);
//         sddmm_low_density_kernel<<<grid_ld, block_ld, 0, stream_low>>>(
//             d_A, d_B,
//             sparse.row_ptr,
//             sparse.col_idx,
//             d_low_density_rows,
//             sparse.values,
//             sparse.rows, K, sparse.cols,
//             low_row_count
//         );
//         cudaEventRecord(step4_stop, stream_low);
//     }
//
//     // 等待两个流完成
//     if (high_row_count > 0) cudaStreamSynchronize(stream_high);
//     if (low_row_count > 0) cudaStreamSynchronize(stream_low);
//
//     // 计算各区域耗时
//     float step3_ms = 0, step4_ms = 0;
//     if (high_row_count > 0) cudaEventElapsedTime(&step3_ms, step3_start, step3_stop);
//     if (low_row_count > 0) cudaEventElapsedTime(&step4_ms, step4_start, step4_stop);
//
//     // 计算实际并发时间
//     cudaEvent_t step3_4_start, step3_4_stop;
//     cudaEventCreate(&step3_4_start);
//     cudaEventCreate(&step3_4_stop);
//     cudaEventRecord(step3_4_start);
//     // 确保事件记录在所有操作前
//     if (high_row_count > 0) cudaEventRecord(step3_start, stream_high);
//     if (low_row_count > 0) cudaEventRecord(step4_start, stream_low);
//     cudaEventRecord(step3_4_stop);
//     cudaEventSynchronize(step3_4_stop);
//     float step3_4_ms;
//     cudaEventElapsedTime(&step3_4_ms, step3_4_start, step3_4_stop);
//
//     printf("[步骤3] 高密度区计算耗时: %.3f ms\n", step3_ms);
//     printf("[步骤4] 低密度区计算耗时: %.3f ms\n", step4_ms);
//     printf("[步骤3+4] 实际并发耗时: %.3f ms (加速比: %.2fx)\n",
//            step3_4_ms, (step3_ms + step4_ms) / step3_4_ms);
//
//     // 资源清理
//     cudaEventDestroy(step3_start);
//     cudaEventDestroy(step3_stop);
//     cudaEventDestroy(step4_start);
//     cudaEventDestroy(step4_stop);
//     cudaEventDestroy(step3_4_start);
//     cudaEventDestroy(step3_4_stop);
//     cudaStreamDestroy(stream_high);
//     cudaStreamDestroy(stream_low);
//
//     // 释放临时内存
//     if (d_high_density_rows) cudaFree(d_high_density_rows);
//     if (d_low_density_rows) cudaFree(d_low_density_rows);
// }
//
//
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// // 从Matrix Market文件加载COO格式稀疏矩阵
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     // 跳过注释行
//     while (std::getline(file, line) && line[0] == '%');
//
//     // 读取矩阵元数据
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     // 调整向量大小
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     // 读取非零元素
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value; // 忽略值，只关心位置
//         file >> row >> col;
//         if (file.peek() != '\n') {
//             file >> value; // 如果有值则读取
//         }
//         // 转换为0-based索引
//         rows[i] = row - 1;
//         cols[i] = col - 1;
//     }
//     file.close();
// }
//
// // 将COO格式转换为CSR格式
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
//     // 初始化行指针
//     csr_row_ptr.resize(M + 1, 0);
//
//     // 计算每行非零元素数量
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[rows[i]]++;
//     }
//
//     // 前缀和计算行指针
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     // 创建临时行计数器和列索引数组
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     // 填充列索引
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int col = cols[i];
//         int index = csr_row_ptr[row] + row_counter[row];
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr)); // 初始化随机种子
//
//     std::string filename = argv[1];
//     int K = 128; // 默认公共维度
//     if (argc > 2) K = std::atoi(argv[2]);
//
//     // 1. 从文件加载稀疏矩阵
//     int M, N, nnz;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//     std::cout << "加载矩阵: " << filename << std::endl;
//     std::cout << "行数: " << M << ", 列数: " << N << ", 非零元数: " << nnz << std::endl;
//
//     // 2. 转换为CSR格式
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx);
//     std::cout << "CSR转换完成" << std::endl;
//
//     // 3. 分配主机内存并初始化稠密矩阵
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     std::generate(h_A.begin(), h_A.end(), []() { return (rand() % 100) / 100.0f; });
//     std::generate(h_B.begin(), h_B.end(), []() { return (rand() % 100) / 100.0f; });
//
//     // 4. 分配设备内存
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//
//     // 5. 设置稀疏矩阵结构
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//
//     // 分配设备内存并复制CSR结构
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//
//     cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//
//     // 初始化结果值为0
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 6. 创建计时事件
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//
//     // 7. 执行SDDMM
//     std::cout << "开始执行SDDMM..." << std::endl;
//     cudaEventRecord(start);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     // 8. 计算性能
//     float milliseconds = 0;
//     cudaEventElapsedTime(&milliseconds, start, stop);
//     double gflops = (2.0 * nnz * K) / (milliseconds * 1e6);
//     printf("SDDMM执行时间: %.3f ms\n", milliseconds);
//     printf("GFLOPS: %.2f\n", gflops);
//
//     // 9. 验证结果
//     std::vector<float> h_values_gpu(nnz);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     std::vector<float> h_values_cpu(nnz);
//     std::cout << "开始CPU参考计算..." << std::endl;
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//     std::cout << "CPU参考计算完成" << std::endl;
//
//     // 10. 结果对比
//     int correct_count = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_gpu[i] - h_values_cpu[i]);
//         max_error = fmax(max_error, diff);
//         if (diff < 1e-4) correct_count++;
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct_count / nnz) << "%" << std::endl;
//
//     // 11. 资源释放
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//     cudaEventDestroy(start);
//     cudaEventDestroy(stop);
//
//     return 0;
// }
//
// //流水线（高密度区+低密度区）