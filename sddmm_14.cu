// #include <cuda_runtime.h>
// #include <thrust/sort.h>
// #include <thrust/device_ptr.h>
// #include <thrust/gather.h>
// #include <iostream>
// #include <cmath>
//
// // 常量定义
// const int TILE_SIZE = 32; // 分块大小
// const int VECTOR_SIZE = 4; // 向量化加载大小(float4)
// const int HIGH_DENSITY_THREADS = 128; // 高密度区线程数/块
//
// // 稀疏矩阵结构体 (CSR格式)
// struct CSRMatrix {
//     int *row_ptr; // 行指针
//     int *col_idx; // 列索引
//     float *values; // 非零值 (SDDMM结果存放位置)
//     int rows; // 矩阵行数
//     int cols; // 矩阵列数
//     int nnz; // 非零元数量
// };
//
// // GPU端行非零元统计内核
// __global__ void compute_row_nnz_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int rows
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         row_nnz[row] = row_ptr[row + 1] - row_ptr[row];
//     }
// }
//
// __global__ void reorder_col_idx_kernel(
//     const int *row_permutation, // 行重排映射
//     const int *orig_row_ptr, // 原始行指针
//     const int *orig_col_idx, // 原始列索引
//     const int *sorted_row_ptr, // 排序后行指针
//     int *sorted_col_idx, // 排序后列索引
//     int rows // 总行数
// ) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     if (tid >= rows) return;
//
//     // 获取原始行号
//     int orig_row = row_permutation[tid];
//
//     // 原始行数据范围
//     int orig_start = orig_row_ptr[orig_row];
//     int orig_end = orig_row_ptr[orig_row + 1];
//     int nnz_in_row = orig_end - orig_start;
//
//     // 排序后行数据范围
//     int sorted_start = sorted_row_ptr[tid];
//
//     // 复制列索引
//     for (int i = 0; i < nnz_in_row; i++) {
//         sorted_col_idx[sorted_start + i] = orig_col_idx[orig_start + i];
//     }
// }
//
// // 高密度区SDDMM内核 (每行非零元 > 阈值)
// __global__ void sddmm_high_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ sorted_row_ptr,
//     const int *__restrict__ sorted_col_idx,
//     const int *__restrict__ row_permutation, // 行映射
//     float *__restrict__ result,
//     int M, int K, int N,
//     int start_row, int end_row
// ) {
//     // 共享内存声明
//     __shared__ float As[TILE_SIZE][TILE_SIZE];
//     __shared__ float Bs[TILE_SIZE][TILE_SIZE];
//
//     int row_id = blockIdx.y;
//     int global_row = start_row + row_id;
//     if (global_row >= end_row) return;
//
//     // 通过映射获取原始行号
//     const int orig_row = row_permutation[global_row];
//     const int row_start = sorted_row_ptr[global_row];
//     const int row_end = sorted_row_ptr[global_row + 1];
//     const int nnz_in_row = row_end - row_start;
//
//     int tid = threadIdx.y * blockDim.x + threadIdx.x;
//     int threads_per_block = blockDim.x * blockDim.y;
//
//     for (int idx = tid; idx < nnz_in_row; idx += threads_per_block) {
//         const int col = sorted_col_idx[row_start + idx];
//         float sum = 0.0f;
//
//         for (int t = 0; t < K; t += TILE_SIZE) {
//             // 协作加载A的分块
//             if (threadIdx.y == 0 && threadIdx.x < TILE_SIZE) {
//                 int load_idx = t + threadIdx.x;
//                 if (load_idx < K) {
//                     As[0][threadIdx.x] = A[orig_row * K + load_idx];
//                 }
//             }
//
//             // 协作加载B的分块
//             if (threadIdx.x == 0 && threadIdx.y < TILE_SIZE) {
//                 int load_idx = t + threadIdx.y;
//                 if (load_idx < K) {
//                     Bs[threadIdx.y][0] = B[col * K + load_idx];
//                 }
//             }
//             __syncthreads();
//
//             // 计算分块乘积
//             int steps = min(TILE_SIZE, K - t);
//             for (int k = 0; k < steps; ++k) {
//                 sum += As[0][k] * Bs[k][0];
//             }
//             __syncthreads();
//         }
//         result[row_start + idx] = sum;
//     }
// }
//
// // 低密度区SDDMM内核 (每行非零元 ≤ 阈值)
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ sorted_row_ptr,
//     const int *__restrict__ sorted_col_idx,
//     const int *__restrict__ row_permutation, // 行映射
//     float *__restrict__ result,
//     int M, int K, int N,
//     int start_row, int end_row
// ) {
//     int global_idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (global_idx >= (end_row - start_row)) return;
//
//     int global_row = start_row + global_idx;
//     int orig_row = row_permutation[global_row]; // 通过映射获取
//     int row_start = sorted_row_ptr[global_row];
//     int row_end = sorted_row_ptr[global_row + 1];
//
//     for (int idx = row_start; idx < row_end; ++idx) {
//         int col = sorted_col_idx[idx];
//         float sum = 0.0f;
//
//         // 处理完整向量
//         int k = 0;
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4 *>(&A[orig_row * K + k]);
//             float4 b_vec = *reinterpret_cast<const float4 *>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x;
//             sum += a_vec.y * b_vec.y;
//             sum += a_vec.z * b_vec.z;
//             sum += a_vec.w * b_vec.w;
//         }
//
//         // 处理剩余元素
//         for (; k < K; ++k) {
//             sum += A[orig_row * K + k] * B[col * K + k];
//         }
//         result[idx] = sum;
//     }
// }
//
// // 主SDDMM函数
// void sddmm_csr(
//     const float *d_A, // 设备端稠密矩阵A (MxK)
//     const float *d_B, // 设备端稠密矩阵B (KxN)
//     CSRMatrix &sparse, // 稀疏矩阵结构 (CSR格式)
//     int K // 公共维度
// ) {
//     // 1. 统计每行非零元素数量
//     int *d_row_nnz;
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//
//     dim3 block(256);
//     dim3 grid((sparse.rows + block.x - 1) / block.x);
//     compute_row_nnz_kernel<<<grid, block>>>(sparse.row_ptr, d_row_nnz, sparse.rows);
//     cudaDeviceSynchronize();
//
//     // 保存原始行非零元数据用于分区计算
//     int *d_orig_row_nnz;
//     cudaMalloc(&d_orig_row_nnz, sparse.rows * sizeof(int));
//     cudaMemcpy(d_orig_row_nnz, d_row_nnz, sparse.rows * sizeof(int), cudaMemcpyDeviceToDevice);
//
//     // 2. 生成行重排索引
//     int *d_row_permutation;
//     cudaMalloc(&d_row_permutation, sparse.rows * sizeof(int));
//     thrust::sequence(thrust::device, d_row_permutation, d_row_permutation + sparse.rows);
//
//     // 3. 按非零元素数量降序排序
//     thrust::sort_by_key(
//         thrust::device,
//         d_row_nnz, d_row_nnz + sparse.rows,
//         d_row_permutation,
//         thrust::greater<int>()
//     );
//
//     // 4. 构建排序后的行指针数组 (前缀和)
//     int *d_sorted_row_ptr;
//     cudaMalloc(&d_sorted_row_ptr, (sparse.rows + 1) * sizeof(int));
//
//     thrust::device_ptr<int> d_nnz_ptr(d_row_nnz);
//     thrust::device_ptr<int> d_sorted_ptr(d_sorted_row_ptr);
//
//     d_sorted_ptr[0] = 0;
//     thrust::inclusive_scan(
//         thrust::device,
//         d_nnz_ptr, d_nnz_ptr + sparse.rows,
//         d_sorted_ptr + 1
//     );
//     cudaDeviceSynchronize();
//
//     // 5. 重排列索引
//     int *d_sorted_col_idx;
//     cudaMalloc(&d_sorted_col_idx, sparse.nnz * sizeof(int));
//
//     // 内核重排列索引
//     dim3 reorder_block(256);
//     dim3 reorder_grid((sparse.rows + reorder_block.x - 1) / reorder_block.x);
//     reorder_col_idx_kernel<<<reorder_grid, reorder_block>>>(
//         d_row_permutation,
//         sparse.row_ptr,
//         sparse.col_idx,
//         d_sorted_row_ptr,
//         d_sorted_col_idx,
//         sparse.rows
//     );
//     cudaDeviceSynchronize();
//
//     // 6. 动态分区计算
//     int avg_nnz = sparse.nnz / sparse.rows;
//     int threshold = avg_nnz * 2; // 经验阈值
//
//     // 使用原始行非零元数据查找分区点
//     int *h_orig_row_nnz = new int[sparse.rows];
//     cudaMemcpy(h_orig_row_nnz, d_orig_row_nnz, sparse.rows * sizeof(int), cudaMemcpyDeviceToHost);
//
//     int split_index = sparse.rows;
//     for (int i = 0; i < sparse.rows; i++) {
//         if (h_orig_row_nnz[i] <= threshold) {
//             split_index = i;
//             break;
//         }
//     }
//     delete[] h_orig_row_nnz;
//
//     // 7. 高密度区计算
//     if (split_index > 0) {
//         dim3 block_hd(32, 4); // 128线程/块
//         dim3 grid_hd(1, split_index);
//
//         sddmm_high_density_kernel<<<grid_hd, block_hd>>>(
//             d_A, d_B,
//             d_sorted_row_ptr,
//             d_sorted_col_idx,
//             d_row_permutation, // 添加映射
//             sparse.values,
//             sparse.rows, K, sparse.cols,
//             0, split_index
//         );
//         cudaDeviceSynchronize();
//     }
//
//     // 8. 低密度区计算
//     if (split_index < sparse.rows) {
//         int low_rows = sparse.rows - split_index;
//         dim3 block_ld(256);
//         dim3 grid_ld((low_rows + block_ld.x - 1) / block_ld.x);
//
//         sddmm_low_density_kernel<<<grid_ld, block_ld>>>(
//             d_A, d_B,
//             d_sorted_row_ptr,
//             d_sorted_col_idx,
//             d_row_permutation, // 添加映射
//             sparse.values,
//             sparse.rows, K, sparse.cols,
//             split_index, sparse.rows
//         );
//         cudaDeviceSynchronize();
//     }
//
//     // 9. 释放临时内存
//     cudaFree(d_row_nnz);
//     cudaFree(d_orig_row_nnz);
//     cudaFree(d_row_permutation);
//     cudaFree(d_sorted_row_ptr);
//     cudaFree(d_sorted_col_idx);
// }
//
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// int main() {
//     // 矩阵维度配置
//     const int M = 12471; // A矩阵行数
//     const int N = 872622; // B矩阵列数
//     const int K = 128; // 公共维度
//     const int nnz = 22624727; // 10%稀疏度
//
//     // 分配主机内存
//     float *h_A = new float[M * K];
//     float *h_B = new float[K * N];
//
//     // 初始化稠密矩阵 (示例)
//     for (int i = 0; i < M * K; ++i) h_A[i] = (i % 10 == 0) ? 1.0f : 0.0f;
//     for (int i = 0; i < K * N; ++i) h_B[i] = (i % 5 == 0) ? 2.0f : 0.0f;
//
//     // 分配设备内存
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, K * N * sizeof(float));
//
//     // 数据传输到设备
//     cudaMemcpy(d_A, h_A, M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B, K * N * sizeof(float), cudaMemcpyHostToDevice);
//
//     // 创建稀疏矩阵结构 (CSR格式)
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//
//     // 生成合理的稀疏矩阵模式
//     thrust::device_ptr<int> row_ptr(sparse.row_ptr);
//     thrust::sequence(thrust::device, row_ptr, row_ptr + M + 1, 0, nnz / M); // 均匀分布行指针
//
//     // 生成有效列索引 (0~N-1)
//     thrust::device_ptr<int> col_idx(sparse.col_idx);
//     thrust::transform(
//         thrust::device,
//         thrust::counting_iterator<int>(0),
//         thrust::counting_iterator<int>(nnz),
//         col_idx,
//         [N] __device__ (int i) { return i % N; }
//     );
//
//     // 创建事件用于计时
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//
//     // 开始计时
//     cudaEventRecord(start);
//
//     // 执行GPU计算
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     // 结束计时
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//
//     float milliseconds = 0;
//     cudaEventElapsedTime(&milliseconds, start, stop);
//     printf("SDDMM execution time: %.3f ms\n", milliseconds);
//     printf("GFLOPS: %.2f\n", (2.0 * nnz * K) / (milliseconds * 1e6));
//
//
//     float *h_values_gpu = new float[nnz];
//     cudaMemcpy(h_values_gpu, sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//
//     // CPU参考计算
//     float *h_values_cpu = new float[nnz];
//     int *h_row_ptr = new int[M + 1];
//     int *h_col_idx = new int[nnz];
//
//     cudaMemcpy(h_row_ptr, sparse.row_ptr, (M + 1) * sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(h_col_idx, sparse.col_idx, nnz * sizeof(int), cudaMemcpyDeviceToHost);
//     sddmm_cpu_reference(h_A, h_B, h_row_ptr, h_col_idx, h_values_cpu, M, N, K);
//
//     // 结果对比
//     int correct_count = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_gpu[i] - h_values_cpu[i]);
//         max_error = fmax(max_error, diff);
//         if (diff < 1e-4) correct_count++;
//     }
//
//     std::cout << "Validation result:\n";
//     std::cout << "Max absolute error: " << max_error << "\n";
//     std::cout << "Correctness rate: " << (100.0f * correct_count / nnz) << "%\n";
//
//     // 资源释放
//     delete[] h_values_cpu;
//     delete[] h_values_gpu;
//     delete[] h_row_ptr;
//     delete[] h_col_idx;
//
//     // 资源释放
//     delete[] h_A;
//     delete[] h_B;
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//
//     return 0;
// }
