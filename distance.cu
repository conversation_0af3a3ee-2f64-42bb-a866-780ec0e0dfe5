// #include <cuda_runtime.h>
// #include <iostream>
// #include <cstdlib>
// #include <ctime>
//
// #define HASH_SIZE 64  // 假设哈希大小为64位
// #define NUM_Q 10      // 假设查询矩阵有10行
// #define NUM_K 5       // 假设键矩阵有5行
//
// __global__ void compute_spherical_hash_distances_kernel(
//     const int *q_hashes,
//     const int *k_hashes,
//     float *spherical_distance,
//     int num_q, int num_k, int hash_size) {
//     int q_idx = blockIdx.x * blockDim.x + threadIdx.x;
//     int k_idx = blockIdx.y * blockDim.y + threadIdx.y;
//
//     if (q_idx < num_q && k_idx < num_k) {
//         int xor_sum = 0;
//         int and_sum = 0;
//         int q_base_idx = q_idx * hash_size;
//         int k_base_idx = k_idx * hash_size;
//
//         for (int i = 0; i < hash_size; ++i) {
//             int q_hash = q_hashes[q_base_idx + i];
//             int k_hash = k_hashes[k_base_idx + i];
//             xor_sum += __popc(q_hash ^ k_hash);
//             and_sum += __popc(q_hash & k_hash);
//         }
//
//         // Avoid division by zero
//         float dist = (and_sum == 0) ? 0.0f : static_cast<float>(xor_sum) / and_sum;
//         spherical_distance[q_idx * num_k + k_idx] = dist;
//     }
// }
//
// void compute_spherical_hash_distances(
//     const int *q_hashes,
//     const int *k_hashes,
//     float *spherical_distance,
//     int num_q, int num_k, int hash_size) {
//     int *d_q_hashes;
//     int *d_k_hashes;
//     float *d_spherical_distance;
//
//     cudaMalloc(&d_q_hashes, num_q * hash_size * sizeof(int));
//     cudaMalloc(&d_k_hashes, num_k * hash_size * sizeof(int));
//     cudaMalloc(&d_spherical_distance, num_q * num_k * sizeof(float));
//
//     const int num_streams = 4;
//     cudaStream_t streams[num_streams];
//     for (int i = 0; i < num_streams; ++i) {
//         cudaStreamCreate(&streams[i]);
//     }
//
//     int q_block_size = (num_q + num_streams - 1) / num_streams;
//     for (int i = 0; i < num_streams; ++i) {
//         int q_start = i * q_block_size;
//         int q_end = min((i + 1) * q_block_size, num_q);
//
//         int q_size = q_end - q_start;
//
//         cudaMemcpyAsync(d_q_hashes + q_start * hash_size, q_hashes + q_start * hash_size,
//                         q_size * hash_size * sizeof(int), cudaMemcpyHostToDevice, streams[i]);
//     }
//
//     cudaMemcpyAsync(d_k_hashes, k_hashes, num_k * hash_size * sizeof(int), cudaMemcpyHostToDevice, streams[0]);
//
//     dim3 threads_per_block(16, 16);
//     dim3 num_blocks((num_q + threads_per_block.x - 1) / threads_per_block.x,
//                     (num_k + threads_per_block.y - 1) / threads_per_block.y);
//
//     for (int i = 0; i < num_streams; ++i) {
//         int q_start = i * q_block_size;
//         int q_end = min((i + 1) * q_block_size, num_q);
//
//         int q_size = q_end - q_start;
//
//         dim3 stream_num_blocks((q_size + threads_per_block.x - 1) / threads_per_block.x, num_blocks.y);
//
//         compute_spherical_hash_distances_kernel<<<stream_num_blocks, threads_per_block, 0, streams[i]>>>(
//             d_q_hashes + q_start * hash_size, d_k_hashes, d_spherical_distance + q_start * num_k,
//             q_size, num_k, hash_size);
//     }
//
//     for (int i = 0; i < num_streams; ++i) {
//         int q_start = i * q_block_size;
//         int q_end = min((i + 1) * q_block_size, num_q);
//
//         int q_size = q_end - q_start;
//
//         cudaMemcpyAsync(spherical_distance + q_start * num_k, d_spherical_distance + q_start * num_k,
//                         q_size * num_k * sizeof(float), cudaMemcpyDeviceToHost, streams[i]);
//     }
//
//     for (int i = 0; i < num_streams; ++i) {
//         cudaStreamSynchronize(streams[i]);
//     }
//
//     for (int i = 0; i < num_streams; ++i) {
//         cudaStreamDestroy(streams[i]);
//     }
//
//     cudaFree(d_q_hashes);
//     cudaFree(d_k_hashes);
//     cudaFree(d_spherical_distance);
// }
// int main() {
//     int *q_hashes = new int[NUM_Q * HASH_SIZE];
//     int *k_hashes = new int[NUM_K * HASH_SIZE];
//     float *spherical_distance = new float[NUM_Q * NUM_K];
//
//     for (int i = 0; i < NUM_Q * HASH_SIZE; ++i) {
//         q_hashes[i] = rand() % 2;
//     }
//     for (int i = 0; i < NUM_K * HASH_SIZE; ++i) {
//         k_hashes[i] = rand() % 2;
//     }
//
//     // 打印 q_hashes 矩阵
//     std::cout << "Q Hashes Matrix:" << std::endl;
//     for (int i = 0; i < NUM_Q; ++i) {
//         for (int j = 0; j < HASH_SIZE; ++j) {
//             std::cout << q_hashes[i * HASH_SIZE + j] << "\t";
//         }
//         std::cout << std::endl; // 换行
//     }
//
//     // 打印 k_hashes 矩阵
//     std::cout << "K Hashes Matrix:" << std::endl;
//     for (int i = 0; i < NUM_K; ++i) {
//         for (int j = 0; j < HASH_SIZE; ++j) {
//             std::cout << k_hashes[i * HASH_SIZE + j] << "\t";
//         }
//         std::cout << std::endl; // 换行
//     }
//
//     compute_spherical_hash_distances(q_hashes, k_hashes, spherical_distance, NUM_Q, NUM_K, HASH_SIZE);
//
//     std::cout << "Spherical Hash Distance Matrix:" << std::endl;
//     for (int i = 0; i < NUM_Q; ++i) {
//         for (int j = 0; j < NUM_K; ++j) {
//             std::cout << spherical_distance[i * NUM_K + j] << "\t";
//         }
//         std::cout << std::endl; // 换行
//     }
//
//     // 释放内存
//     delete[] q_hashes;
//     delete[] k_hashes;
//     delete[] spherical_distance;
//
//     return 0;
// }
