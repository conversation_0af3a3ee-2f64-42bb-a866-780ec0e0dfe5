 // #include <cstdio>
 // #include <cstdlib>
 // #include <vector>
 // #include <unordered_map>
 // #include <cuda_runtime.h>
 // #include <cassert>
 //
 // #define CUDA_CHECK(call) \
 // do { \
 //     cudaError_t err = (call); \
 //     if (err != cudaSuccess) { \
 //         fprintf(stderr, "CUDA error at %s:%d code=%d(%s) \"%s\"\n", \
 //         __FILE__, __LINE__, err, cudaGetErrorString(err), #call); \
 //         exit(EXIT_FAILURE); \
 //     } \
 // } while(0)
 //
 // void print_array(const float *arr, int size, const char *name) {
 //     printf("%s: [", name);
 //     for (int i = 0; i < size; i++) {
 //         printf("%.1f%s", arr[i], (i == size - 1) ? "]\n" : ", ");
 //     }
 // }
 //
 // struct BlockInfo {
 //     int nnz;
 //     int *h_rows;
 //     int *h_cols;
 //     int *h_local_A_rows;
 //     int *h_local_B_cols;
 //     float *h_A_block;
 //     float *h_B_block;
 //     int num_A_rows;
 //     int num_B_cols;
 // };
 //
 // __global__ void sddmm_kernel(const float *A_block, const float *B_block, const int *local_A_rows,
 //                              const int *local_B_cols, float *output, int nnz, int K) {
 //     int idx = blockIdx.x * blockDim.x + threadIdx.x;
 //     if (idx >= nnz) return;
 //
 //     int local_row = local_A_rows[idx];
 //     int local_col = local_B_cols[idx];
 //
 //     const float *A_row = A_block + local_row * K;
 //     const float *B_col = B_block + local_col * K;
 //
 //     float sum = 0.0f;
 //     for (int k = 0; k < K; k++) {
 //         sum += A_row[k] * B_col[k];
 //     }
 //
 //     output[idx] = sum;
 // }
 //
 // void preprocess_blocks(BlockInfo *blocks, int num_blocks, const float *A, const float *B,
 //                        const int *all_rows, const int *all_cols, int total_nnz, int K, int N) {
 //     int nnz_per_block = (total_nnz + num_blocks - 1) / num_blocks; //每块非零元素数量
 //     for (int b = 0; b < num_blocks; b++) {
 //         int start = b * nnz_per_block; //当前块非零元素起始索引
 //         int end = std::min(start + nnz_per_block, total_nnz); //当前块非零元素结束索引
 //         blocks[b].nnz = end - start;
 //
 //         std::unordered_map<int, int> row_map, col_map;
 //         std::vector<int> unique_rows, unique_cols;
 //
 //         for (int i = start; i < end; i++) {
 //             int r = all_rows[i];
 //             if (!row_map.count(r)) {
 //                 row_map[r] = unique_rows.size();
 //                 unique_rows.push_back(r); //存储当前块的行索引
 //             }
 //
 //             int c = all_cols[i];
 //             if (!col_map.count(c)) {
 //                 col_map[c] = unique_cols.size();
 //                 unique_cols.push_back(c); //存储当前块的列索引
 //             }
 //         }
 //
 //         blocks[b].num_A_rows = unique_rows.size(); //当前块的非零元素占几行
 //         blocks[b].num_B_cols = unique_cols.size(); //当前块的非零元素占几列
 //         blocks[b].h_A_block = new float[blocks[b].num_A_rows * K]; //提取A矩阵的行
 //         blocks[b].h_B_block = new float[blocks[b].num_B_cols * K]; //提取B矩阵的列
 //         blocks[b].h_local_A_rows = new int[end - start];
 //         blocks[b].h_local_B_cols = new int[end - start];
 //         blocks[b].h_rows = new int[end - start];
 //         blocks[b].h_cols = new int[end - start];
 //
 //         for (size_t i = 0; i < unique_rows.size(); i++) {
 //             int r = unique_rows[i];
 //             memcpy(blocks[b].h_A_block + i * K, A + r * K, K * sizeof(float));
 //         }
 //         for (size_t i = 0; i < unique_cols.size(); i++) {
 //             int c = unique_cols[i];
 //             for (int k = 0; k < K; k++) {
 //                 blocks[b].h_B_block[i * K + k] = B[k * N + c];
 //             }
 //         }
 //
 //         for (int i = start; i < end; i++) {
 //             int idx = i - start;
 //             blocks[b].h_rows[idx] = all_rows[i];
 //             blocks[b].h_cols[idx] = all_cols[i];
 //             blocks[b].h_local_A_rows[idx] = row_map[all_rows[i]];
 //             blocks[b].h_local_B_cols[idx] = col_map[all_cols[i]];
 //         }
 //     }
 // }
 //
 // void execute_sddmm(BlockInfo *blocks, int num_blocks, int K, int N, float **d_results) {
 //     const int num_buffers = 2;
 //     cudaStream_t streams[num_buffers];
 //     cudaEvent_t events[num_buffers];
 //     for (int i = 0; i < num_buffers; i++) {
 //         CUDA_CHECK(cudaStreamCreate(&streams[i]));
 //         CUDA_CHECK(cudaEventCreate(&events[i]));
 //     }
 //
 //     struct DeviceBuffers {
 //         float *A_block;
 //         float *B_block;
 //         int *local_A_rows;
 //         int *local_B_cols;
 //         float *output;
 //     } d_buf[num_buffers];
 //
 //     // 初始化设备缓冲区指针为 nullptr
 //     for (int i = 0; i < num_buffers; i++) {
 //         d_buf[i].A_block = nullptr;
 //         d_buf[i].B_block = nullptr;
 //         d_buf[i].local_A_rows = nullptr;
 //         d_buf[i].local_B_cols = nullptr;
 //         d_buf[i].output = nullptr;
 //     }
 //
 //     int current = 0;
 //     for (int b = 0; b < num_blocks; b++) {
 //         int next = (current + 1) % num_buffers;
 //
 //         // 释放前一个块的设备内存
 //         if (b > 0) {
 //             if (d_buf[current].A_block) {
 //                 CUDA_CHECK(cudaFree(d_buf[current].A_block));
 //                 d_buf[current].A_block = nullptr;
 //             }
 //             if (d_buf[current].B_block) {
 //                 CUDA_CHECK(cudaFree(d_buf[current].B_block));
 //                 d_buf[current].B_block = nullptr;
 //             }
 //             if (d_buf[current].local_A_rows) {
 //                 CUDA_CHECK(cudaFree(d_buf[current].local_A_rows));
 //                 d_buf[current].local_A_rows = nullptr;
 //             }
 //             if (d_buf[current].local_B_cols) {
 //                 CUDA_CHECK(cudaFree(d_buf[current].local_B_cols));
 //                 d_buf[current].local_B_cols = nullptr;
 //             }
 //             if (d_buf[current].output) {
 //                 CUDA_CHECK(cudaFree(d_buf[current].output));
 //                 d_buf[current].output = nullptr;
 //             }
 //         }
 //
 //         // 为当前块分配适当的内存
 //         CUDA_CHECK(cudaMalloc(&d_buf[current].A_block, blocks[b].num_A_rows * K * sizeof(float)));
 //         CUDA_CHECK(cudaMalloc(&d_buf[current].B_block, blocks[b].num_B_cols * K * sizeof(float)));
 //         CUDA_CHECK(cudaMalloc(&d_buf[current].local_A_rows, blocks[b].nnz * sizeof(int)));
 //         CUDA_CHECK(cudaMalloc(&d_buf[current].local_B_cols, blocks[b].nnz * sizeof(int)));
 //         CUDA_CHECK(cudaMalloc(&d_buf[current].output, blocks[b].nnz * sizeof(float)));
 //
 //         CUDA_CHECK(cudaMemcpyAsync(d_buf[current].A_block, blocks[b].h_A_block,
 //             blocks[b].num_A_rows * K * sizeof(float), cudaMemcpyHostToDevice, streams[current]));
 //         CUDA_CHECK(cudaMemcpyAsync(d_buf[current].B_block, blocks[b].h_B_block,
 //             blocks[b].num_B_cols * K * sizeof(float), cudaMemcpyHostToDevice, streams[current]));
 //         CUDA_CHECK(cudaMemcpyAsync(d_buf[current].local_A_rows, blocks[b].h_local_A_rows,
 //             blocks[b].nnz * sizeof(int), cudaMemcpyHostToDevice, streams[current]));
 //         CUDA_CHECK(cudaMemcpyAsync(d_buf[current].local_B_cols, blocks[b].h_local_B_cols,
 //             blocks[b].nnz * sizeof(int), cudaMemcpyHostToDevice, streams[current]));
 //         CUDA_CHECK(cudaEventRecord(events[current], streams[current]));
 //
 //         CUDA_CHECK(cudaStreamWaitEvent(streams[current], events[current], 0));
 //
 //         dim3 block(256);
 //         dim3 grid((blocks[b].nnz + block.x - 1) / block.x);
 //         sddmm_kernel<<<grid, block, 0, streams[current]>>>(
 //             d_buf[current].A_block, d_buf[current].B_block,
 //             d_buf[current].local_A_rows, d_buf[current].local_B_cols,
 //             d_buf[current].output, blocks[b].nnz, K);
 //         CUDA_CHECK(cudaGetLastError());
 //
 //         CUDA_CHECK(cudaMemcpyAsync(d_results[b], d_buf[current].output,
 //             blocks[b].nnz * sizeof(float), cudaMemcpyDeviceToHost, streams[current]));
 //
 //         if (b + 1 < num_blocks) {
 //             // 为下一个块分配内存
 //             CUDA_CHECK(cudaMalloc(&d_buf[next].A_block, blocks[b + 1].num_A_rows * K * sizeof(float)));
 //             CUDA_CHECK(cudaMalloc(&d_buf[next].B_block, blocks[b + 1].num_B_cols * K * sizeof(float)));
 //             CUDA_CHECK(cudaMalloc(&d_buf[next].local_A_rows, blocks[b + 1].nnz * sizeof(int)));
 //             CUDA_CHECK(cudaMalloc(&d_buf[next].local_B_cols, blocks[b + 1].nnz * sizeof(int)));
 //             CUDA_CHECK(cudaMalloc(&d_buf[next].output, blocks[b + 1].nnz * sizeof(float)));
 //
 //             CUDA_CHECK(cudaMemcpyAsync(d_buf[next].A_block, blocks[b + 1].h_A_block,
 //                 blocks[b + 1].num_A_rows * K * sizeof(float), cudaMemcpyHostToDevice, streams[next]));
 //             CUDA_CHECK(cudaMemcpyAsync(d_buf[next].B_block, blocks[b + 1].h_B_block,
 //                 blocks[b + 1].num_B_cols * K * sizeof(float), cudaMemcpyHostToDevice, streams[next]));
 //             CUDA_CHECK(cudaMemcpyAsync(d_buf[next].local_A_rows, blocks[b + 1].h_local_A_rows,
 //                 blocks[b + 1].nnz * sizeof(int), cudaMemcpyHostToDevice, streams[next]));
 //             CUDA_CHECK(cudaMemcpyAsync(d_buf[next].local_B_cols, blocks[b + 1].h_local_B_cols,
 //                 blocks[b + 1].nnz * sizeof(int), cudaMemcpyHostToDevice, streams[next]));
 //             CUDA_CHECK(cudaEventRecord(events[next], streams[next]));
 //         }
 //
 //         current = next;
 //     }
 //
 //     // 释放最后一个块的设备内存
 //     if (d_buf[current].A_block) {
 //         CUDA_CHECK(cudaFree(d_buf[current].A_block));
 //         d_buf[current].A_block = nullptr;
 //     }
 //     if (d_buf[current].B_block) {
 //         CUDA_CHECK(cudaFree(d_buf[current].B_block));
 //         d_buf[current].B_block = nullptr;
 //     }
 //     if (d_buf[current].local_A_rows) {
 //         CUDA_CHECK(cudaFree(d_buf[current].local_A_rows));
 //         d_buf[current].local_A_rows = nullptr;
 //     }
 //     if (d_buf[current].local_B_cols) {
 //         CUDA_CHECK(cudaFree(d_buf[current].local_B_cols));
 //         d_buf[current].local_B_cols = nullptr;
 //     }
 //     if (d_buf[current].output) {
 //         CUDA_CHECK(cudaFree(d_buf[current].output));
 //         d_buf[current].output = nullptr;
 //     }
 //
 //     CUDA_CHECK(cudaDeviceSynchronize());
 //     for (int i = 0; i < num_buffers; i++) {
 //         CUDA_CHECK(cudaStreamDestroy(streams[i]));
 //         CUDA_CHECK(cudaEventDestroy(events[i]));
 //     }
 // }
 //
 // int main() {
 //     const int M = 4, N = 4, K = 3;
 //     const int total_nnz = 7;
 //     const int num_blocks = 4;
 //
 //     float A[M * K] = {
 //         1.0f, 2.0f, 3.0f,
 //         4.0f, 5.0f, 6.0f,
 //         7.0f, 8.0f, 9.0f,
 //         10.0f, 11.0f, 12.0f
 //     };
 //     float B[K * N] = {
 //         13.0f, 14.0f, 15.0f, 16.0f,
 //         17.0f, 18.0f, 19.0f, 20.0f,
 //         21.0f, 22.0f, 23.0f, 24.0f
 //     };
 //
 //     int all_rows[total_nnz] = {0, 0, 1, 1, 2, 2, 3};
 //     int all_cols[total_nnz] = {0, 2, 1, 2, 0, 3, 3};
 //
 //     float expected[total_nnz] = {110.0f, 122.0f, 278.0f, 293.0f, 416.0f, 488.0f, 668.0f};
 //
 //     BlockInfo *blocks = new BlockInfo[num_blocks];
 //     float **d_results = new float *[num_blocks];
 //     for (int b = 0; b < num_blocks; b++) {
 //         d_results[b] = new float[total_nnz];
 //     }
 //
 //     preprocess_blocks(blocks, num_blocks, A, B, all_rows, all_cols, total_nnz, K, N);
 //
 //     // 打印预处理后的B_block验证数据
 //     for (int b = 0; b < num_blocks; b++) {
 //         printf("块 %d 预处理后的B_block数据:\n", b);
 //         for (int col = 0; col < blocks[b].num_B_cols; col++) {
 //             printf("列%d: [", col);
 //             for (int k = 0; k < K; k++) {
 //                 printf("%5.1f", blocks[b].h_B_block[col * K + k]);
 //             }
 //             printf(" ]\n");
 //         }
 //     }
 //
 //     execute_sddmm(blocks, num_blocks, K, N, d_results);
 //
 //     // 同步等待所有计算完成
 //     CUDA_CHECK(cudaDeviceSynchronize());
 //
 //     // 合并所有块的结果
 //     float *final_result = new float[total_nnz];
 //     int offset = 0;
 //     for (int b = 0; b < num_blocks; b++) {
 //         memcpy(final_result + offset, d_results[b], blocks[b].nnz * sizeof(float));
 //         offset += blocks[b].nnz;
 //     }
 //
 //     // 打印并验证结果
 //     printf("\n计算结果验证:\n");
 //     print_array(expected, total_nnz, "预期结果");
 //     print_array(final_result, total_nnz, "实际结果");
 //
 //     // 数值验证
 //     for (int i = 0; i < total_nnz; i++) {
 //         assert(fabs(final_result[i] - expected[i]) < 1e-6);
 //     }
 //     printf("测试通过!\n");
 //
 //     // 释放资源
 //     for (int b = 0; b < num_blocks; b++) {
 //         delete[] blocks[b].h_A_block;
 //         delete[] blocks[b].h_B_block;
 //         delete[] blocks[b].h_local_A_rows;
 //         delete[] blocks[b].h_local_B_cols;
 //         delete[] blocks[b].h_rows;
 //         delete[] blocks[b].h_cols;
 //         delete[] d_results[b];
 //     }
 //     delete[] blocks;
 //     delete[] d_results;
 //     delete[] final_result;
 //
 //     return 0;
 // }
