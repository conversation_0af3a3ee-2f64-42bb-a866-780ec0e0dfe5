// #include <cuda_runtime.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
// #include <cooperative_groups.h>
//
// // 常量定义
// const int TILE_SIZE = 32; // 分块大小
// const int VECTOR_SIZE = 4; // 向量化加载大小(float4)
// const int HIGH_DENSITY_THREADS = 128; // 高密度区线程数/块
//
// // 向量化加载和存储辅助函数
// template<typename T>
// struct VectorTypeTraits;
//
// template<>
// struct VectorTypeTraits<float> {
//     using Type = float;
//     static constexpr int kValuesPerVector = 1;
// };
//
// template<>
// struct VectorTypeTraits<float2> {
//     using Type = float2;
//     static constexpr int kValuesPerVector = 2;
// };
//
// template<>
// struct VectorTypeTraits<float4> {
//     using Type = float4;
//     static constexpr int kValuesPerVector = 4;
// };
//
// // 加载函数：从内存地址加载向量化数据
// template<typename VectorType>
// __device__ __forceinline__ VectorType Load(const void *ptr) {
//     return *reinterpret_cast<const VectorType *>(ptr);
// }
//
// // 存储函数：将向量化数据存储到内存地址
// template<typename VectorType>
// __device__ __forceinline__ void Store(VectorType value, void *ptr) {
//     *reinterpret_cast<VectorType *>(ptr) = value;
// }
//
// // 标量存储重载：将标量数组转换为向量化存储
// template<typename VectorType>
// __device__ __forceinline__ void Store(VectorType value, float *scalar_ptr) {
//     constexpr int kValuesPerVector = VectorTypeTraits<VectorType>::kValuesPerVector;
//     float *ptr = reinterpret_cast<float *>(&value);
//
// #pragma unroll
//     for (int i = 0; i < kValuesPerVector; ++i) {
//         scalar_ptr[i] = ptr[i];
//     }
// }
//
// // 稀疏矩阵结构体 (CSR格式)
// struct CSRMatrix {
//     int *row_ptr; // 行指针
//     int *col_idx; // 列索引
//     float *values; // 非零值 (SDDMM结果存放位置)
//     int rows; // 矩阵行数
//     int cols; // 矩阵列数
//     int nnz; // 非零元数量
// };
//
// // GPU端行非零元统计内核
// __global__ void compute_row_nnz_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int rows
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         row_nnz[row] = row_ptr[row + 1] - row_ptr[row];
//     }
// }
//
// // 高密度区SDDMM内核 (每行非零元 > 32) - 使用双缓冲流水线优化
// #define VECTOR_SIZE 4
// #define VEC 4  // 每个线程处理的非零元素数量
// #define KB 32   // K维度分块大小
// #define STAGE 2 // 流水线阶段数（双缓冲）
// #define kBlockWidth 128 // 线程块宽度（HIGH_DENSITY_THREADS >= kBlockWidth）
//
// template<typename LoadType>
// __global__ void sddmm_high_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     static constexpr int kValuesPerLoad = sizeof(LoadType) / sizeof(float);
//     constexpr int kBlockItemsX = kBlockWidth * VEC;
//     constexpr int kBufferSize = KB * sizeof(float);
//     constexpr int kTotalSmem = kBlockItemsX * sizeof(int) + 2 * kBufferSize;
//
//     extern __shared__ __align__(16) char shared_memory[];
//     int *column_indices_tile = reinterpret_cast<int *>(shared_memory);
//     float *A_shared[2] = {
//         reinterpret_cast<float *>(shared_memory + kBlockItemsX * sizeof(int)),
//         reinterpret_cast<float *>(shared_memory + kBlockItemsX * sizeof(int) + kBufferSize)
//     };
//
//     int row_in_group = blockIdx.x;
//     if (row_in_group >= num_rows) return;
//
//     int orig_row = row_indices[row_in_group];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     int tid = threadIdx.x;
//     __shared__ int stage_counter;
//     __shared__ int barrier;
//
//     for (int nz_block_start = 0; nz_block_start < nnz_in_row; nz_block_start += kBlockItemsX) {
//         int nz_in_block = min(kBlockItemsX, nnz_in_row - nz_block_start);
//         int global_nz_start = row_start + nz_block_start;
//
//         // 加载列索引
//         if (tid * VEC < kBlockItemsX) {
//             for (int v = 0; v < VEC; v++) {
//                 int idx = tid * VEC + v;
//                 int col_val = (idx < nz_in_block) ? col_idx[global_nz_start + idx] : -1;
//                 column_indices_tile[idx] = col_val;
//             }
//         }
//         __syncthreads();
//
//         // 初始化累加器
//         float accumulators[VEC] = {0};
//
//         // 双缓冲流水线实现
//         int current_stage = 0;
//         int next_stage = 1;
//         int kb = 0;
//
//         // 预加载第一个K块
//         if (kb < K) {
//             int k_block_size = min(KB, K - kb);
//             for (int k = tid; k < k_block_size; k += blockDim.x) {
//                 A_shared[current_stage][k] = A[orig_row * K + kb + k];
//             }
//         }
//         __syncthreads();
//
//         // 主循环 - 双缓冲流水线
//         while (kb < K) {
//             // 启动下一阶段的加载（异步）
//             if (kb + KB < K) {
//                 int k_block_size = min(KB, K - (kb + KB));
//                 for (int k = tid; k < k_block_size; k += blockDim.x) {
//                     A_shared[next_stage][k] = A[orig_row * K + kb + KB + k];
//                 }
//             }
//
//             // 处理当前K块
//             float *current_A = A_shared[current_stage];
//             int k_block_size = min(KB, K - kb);
//
//             if (tid * VEC < nz_in_block) {
//                 for (int k = 0; k < k_block_size; k += kValuesPerLoad) {
//                     // 加载A片段
//                     float a_frag[kValuesPerLoad];
//                     for (int i = 0; i < kValuesPerLoad; i++) {
//                         if ((k + i) < k_block_size) {
//                             a_frag[i] = current_A[k + i];
//                         } else {
//                             a_frag[i] = 0.0f;
//                         }
//                     }
//
//                     // 处理每个非零元
//                     for (int v = 0; v < VEC; v++) {
//                         int idx = tid * VEC + v;
//                         if (idx >= nz_in_block) continue;
//
//                         int col = column_indices_tile[idx];
//                         float b_frag[kValuesPerLoad] = {0};
//
//                         if (col >= 0 && col < N) {
//                             int k_pos = kb + k;
//
//                             if (k_pos + kValuesPerLoad <= K) {
//                                 // 向量化加载
//                                 LoadType b_vec = *reinterpret_cast<const LoadType *>(
//                                     &B[col * K + k_pos]
//                                 );
//                                 Store(b_vec, b_frag);
//                             } else {
//                                 for (int i = 0; i < kValuesPerLoad; i++) {
//                                     if (k_pos + i < K) {
//                                         b_frag[i] = B[col * K + k_pos + i];
//                                     }
//                                 }
//                             }
//                         }
//
//                         // 计算乘累加
//                         for (int i = 0; i < kValuesPerLoad; i++) {
//                             accumulators[v] += a_frag[i] * b_frag[i];
//                         }
//                     }
//                 }
//             }
//
//             // 等待下一阶段加载完成
//             __syncthreads();
//
//             // 交换缓冲区
//             current_stage = next_stage;
//             next_stage = 1 - next_stage;
//             kb += KB;
//         }
//
//         // 存储结果
//         if (tid * VEC < nz_in_block) {
//             for (int v = 0; v < VEC; v++) {
//                 int idx = tid * VEC + v;
//                 if (idx < nz_in_block) {
//                     result[global_nz_start + idx] = accumulators[v];
//                 }
//             }
//         }
//     } // 非零元块循环结束
// }
//
// // 低密度区SDDMM内核 (每行非零元 ≤ 32)
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices, // 低密度行索引
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int index = blockIdx.x * blockDim.x + threadIdx.x;
//     if (index >= num_rows) return;
//
//     int orig_row = row_indices[index];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 寄存器缓存A矩阵行
//     float a_reg[32];
//     for (int k = 0; k < K; k++) {
//         a_reg[k] = A[orig_row * K + k];
//     }
//
//     for (int idx = 0; idx < nnz_in_row; idx++) {
//         int col = col_idx[row_start + idx];
//         float sum = 0.0f;
//         int k = 0;
//
//         // 向量化处理
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4 *>(&a_reg[k]);
//             float4 b_vec = *reinterpret_cast<const float4 *>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x;
//             sum += a_vec.y * b_vec.y;
//             sum += a_vec.z * b_vec.z;
//             sum += a_vec.w * b_vec.w;
//         }
//
//         // 标量处理剩余部分
//         for (; k < K; ++k) {
//             sum += a_reg[k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 主SDDMM函数 - 使用CUDA Stream优化
// void sddmm_csr(
//     const float *d_A, // 设备端稠密矩阵A (MxK)
//     const float *d_B, // 设备端稠密矩阵B (KxN)
//     CSRMatrix &sparse, // 稀疏矩阵结构 (CSR格式)
//     int K // 公共维度
// ) {
//     // 获取设备属性
//     cudaDeviceProp prop;
//     cudaGetDeviceProperties(&prop, 0);
//
//     // 确定流数量
//     const int num_streams = prop.asyncEngineCount > 0 ? prop.asyncEngineCount : 4;
//     //const int num_streams = 8;
//     std::vector<cudaStream_t> streams(num_streams);
//     for (int i = 0; i < num_streams; ++i) {
//         cudaStreamCreate(&streams[i]);
//     }
//     printf("使用 %d 个CUDA流进行并行优化\n", num_streams);
//
//     // 步骤1计时事件
//     cudaEvent_t step1_start, step1_stop;
//     cudaEventCreate(&step1_start);
//     cudaEventCreate(&step1_stop);
//     cudaEventRecord(step1_start, streams[0]);
//
//     // 1. 统计每行非零元素数量
//     int *d_row_nnz;
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//
//     dim3 block(256);
//     dim3 grid((sparse.rows + block.x - 1) / block.x);
//     compute_row_nnz_kernel<<<grid, block, 0, streams[0]>>>(sparse.row_ptr, d_row_nnz, sparse.rows);
//     cudaStreamSynchronize(streams[0]);
//
//     cudaEventRecord(step1_stop, streams[0]);
//     cudaEventSynchronize(step1_stop);
//     float step1_ms;
//     cudaEventElapsedTime(&step1_ms, step1_start, step1_stop);
//     printf("[步骤1] 行非零元统计耗时: %.3f ms\n", step1_ms);
//
//     // 步骤2计时事件
//     cudaEvent_t step2_start, step2_stop;
//     cudaEventCreate(&step2_start);
//     cudaEventCreate(&step2_stop);
//     cudaEventRecord(step2_start, streams[0]);
//
//     // 2. 动态分区: 将行分为高密度区(>32)和低密度区(<=32)
//     int *h_row_nnz = new int[sparse.rows];
//     cudaMemcpyAsync(h_row_nnz, d_row_nnz, sparse.rows * sizeof(int),
//                     cudaMemcpyDeviceToHost, streams[0]);
//     cudaStreamSynchronize(streams[0]);
//
//     std::vector<int> high_density_rows;
//     std::vector<int> low_density_rows;
//     int threshold = 32;
//
//     for (int i = 0; i < sparse.rows; ++i) {
//         if (h_row_nnz[i] > threshold) {
//             high_density_rows.push_back(i);
//         } else {
//             low_density_rows.push_back(i);
//         }
//     }
//
//     int high_row_count = high_density_rows.size();
//     int low_row_count = low_density_rows.size();
//
//     // 复制行索引到设备端 - 使用异步拷贝
//     int *d_high_density_rows = nullptr;
//     int *d_low_density_rows = nullptr;
//
//     if (high_row_count > 0) {
//         cudaMalloc(&d_high_density_rows, high_row_count * sizeof(int));
//         cudaMemcpyAsync(d_high_density_rows, high_density_rows.data(),
//                         high_row_count * sizeof(int), cudaMemcpyHostToDevice, streams[0]);
//     }
//     if (low_row_count > 0) {
//         cudaMalloc(&d_low_density_rows, low_row_count * sizeof(int));
//         cudaMemcpyAsync(d_low_density_rows, low_density_rows.data(),
//                         low_row_count * sizeof(int), cudaMemcpyHostToDevice, streams[0]);
//     }
//
//     delete[] h_row_nnz;
//     cudaFree(d_row_nnz);
//
//     cudaEventRecord(step2_stop, streams[0]);
//     cudaEventSynchronize(step2_stop);
//     float step2_ms;
//     cudaEventElapsedTime(&step2_ms, step2_start, step2_stop);
//     printf("[步骤2] 动态分区耗时: %.3f ms (高密度行: %d, 低密度行: %d)\n",
//            step2_ms, high_row_count, low_row_count);
//
//     // 步骤3计时事件: 高密度区计算
//     cudaEvent_t step3_start, step3_stop;
//     cudaEventCreate(&step3_start);
//     cudaEventCreate(&step3_stop);
//     cudaEventRecord(step3_start);
//
//     // 高密度区计算 - 使用多流并行，将高密度行均匀分配到多个流
//     if (high_row_count > 0) {
//         // 共享内存计算
//         size_t shared_mem_size = (kBlockWidth * VEC) * sizeof(int) + 2 * KB * sizeof(float);
//
//         if (shared_mem_size > prop.sharedMemPerBlock) {
//             std::cerr << "错误：共享内存需求 (" << shared_mem_size / 1024
//                     << "KB) 超过设备限制 (" << prop.sharedMemPerBlock / 1024 << "KB)" << std::endl;
//             exit(1);
//         }
//
//         dim3 block_hd(HIGH_DENSITY_THREADS);
//
//         // 将高密度行分块到多个流
//         int rows_per_stream = (high_row_count + num_streams - 1) / num_streams;
//         printf("高密度区分块: %d 行, 每流处理约 %d 行\n", high_row_count, rows_per_stream);
//
//         for (int i = 0; i < num_streams; i++) {
//             int start_row = i * rows_per_stream;
//             int end_row = (i == num_streams - 1) ? high_row_count : (i + 1) * rows_per_stream;
//             int chunk_size = end_row - start_row;
//
//             if (chunk_size > 0) {
//                 sddmm_high_density_kernel<float4>
//                         <<<chunk_size, block_hd, shared_mem_size, streams[i]>>>(
//                             d_A, d_B,
//                             sparse.row_ptr,
//                             sparse.col_idx,
//                             d_high_density_rows + start_row,
//                             sparse.values,
//                             sparse.rows, K, sparse.cols,
//                             chunk_size
//                         );
//             }
//         }
//     }
//
//     // 步骤4计时事件: 低密度区计算 - 使用独立流
//     cudaEvent_t step4_start, step4_stop;
//     cudaEventCreate(&step4_start);
//     cudaEventCreate(&step4_stop);
//     cudaEventRecord(step4_start, streams[num_streams - 1]);
//
//     if (low_row_count > 0) {
//         dim3 block_ld(256);
//         dim3 grid_ld((low_row_count + block_ld.x - 1) / block_ld.x);
//
//         // 使用最后一个流处理低密度区
//         sddmm_low_density_kernel<<<grid_ld, block_ld, 0, streams[num_streams - 1]>>>(
//             d_A, d_B,
//             sparse.row_ptr,
//             sparse.col_idx,
//             d_low_density_rows,
//             sparse.values,
//             sparse.rows, K, sparse.cols,
//             low_row_count
//         );
//     }
//
//     // 等待所有流完成
//     for (int i = 0; i < num_streams; i++) {
//         cudaStreamSynchronize(streams[i]);
//     }
//
//     cudaEventRecord(step3_stop);
//     cudaEventSynchronize(step3_stop);
//     float step3_ms;
//     cudaEventElapsedTime(&step3_ms, step3_start, step3_stop);
//     printf("[步骤3] 高密度区计算耗时: %.3f ms\n", step3_ms);
//
//     cudaEventRecord(step4_stop, streams[num_streams - 1]);
//     cudaEventSynchronize(step4_stop);
//     float step4_ms;
//     cudaEventElapsedTime(&step4_ms, step4_start, step4_stop);
//     printf("[步骤4] 低密度区计算耗时: %.3f ms\n", step4_ms);
//
//     // 释放临时内存
//     if (d_high_density_rows) cudaFree(d_high_density_rows);
//     if (d_low_density_rows) cudaFree(d_low_density_rows);
//
//     // 销毁流
//     for (int i = 0; i < num_streams; ++i) {
//         cudaStreamDestroy(streams[i]);
//     }
// }
//
//
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// // 从Matrix Market文件加载COO格式稀疏矩阵
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     // 跳过注释行
//     while (std::getline(file, line) && line[0] == '%');
//
//     // 读取矩阵元数据
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     // 调整向量大小
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     // 读取非零元素
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value; // 忽略值，只关心位置
//         file >> row >> col;
//         if (file.peek() != '\n') {
//             file >> value; // 如果有值则读取
//         }
//         // 转换为0-based索引
//         rows[i] = row - 1;
//         cols[i] = col - 1;
//     }
//     file.close();
// }
//
// // 将COO格式转换为CSR格式
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
//     // 初始化行指针
//     csr_row_ptr.resize(M + 1, 0);
//
//     // 计算每行非零元素数量
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[rows[i]]++;
//     }
//
//     // 前缀和计算行指针
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     // 创建临时行计数器和列索引数组
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     // 填充列索引
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int col = cols[i];
//         int index = csr_row_ptr[row] + row_counter[row];
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr)); // 初始化随机种子
//
//     std::string filename = argv[1];
//     int K = 128; // 默认公共维度
//     if (argc > 2) K = std::atoi(argv[2]);
//
//     // 1. 从文件加载稀疏矩阵
//     int M, N, nnz;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//     std::cout << "加载矩阵: " << filename << std::endl;
//     std::cout << "行数: " << M << ", 列数: " << N << ", 非零元数: " << nnz << std::endl;
//
//     // 2. 转换为CSR格式
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx);
//     std::cout << "CSR转换完成" << std::endl;
//
//     // 3. 分配主机内存并初始化稠密矩阵
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     std::generate(h_A.begin(), h_A.end(), []() { return (rand() % 100) / 100.0f; });
//     std::generate(h_B.begin(), h_B.end(), []() { return (rand() % 100) / 100.0f; });
//
//     // 4. 分配设备内存
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//
//     // 5. 设置稀疏矩阵结构
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//
//     // 分配设备内存并复制CSR结构
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//
//     cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//
//     // 初始化结果值为0
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 6. 创建计时事件
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//
//     // 7. 执行SDDMM
//     std::cout << "开始执行SDDMM..." << std::endl;
//     cudaEventRecord(start);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     // 8. 计算性能
//     float milliseconds = 0;
//     cudaEventElapsedTime(&milliseconds, start, stop);
//     double gflops = (2.0 * nnz * K) / (milliseconds * 1e6);
//     printf("SDDMM总执行时间: %.3f ms\n", milliseconds);
//     printf("GFLOPS: %.2f\n", gflops);
//
//     // 9. 验证结果
//     std::vector<float> h_values_gpu(nnz);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     std::vector<float> h_values_cpu(nnz);
//     std::cout << "开始CPU参考计算..." << std::endl;
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//     std::cout << "CPU参考计算完成" << std::endl;
//
//     // 10. 结果对比
//     int correct_count = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_gpu[i] - h_values_cpu[i]);
//         max_error = fmax(max_error, diff);
//         if (diff < 1e-4) correct_count++;
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct_count / nnz) << "%" << std::endl;
//
//     // 11. 资源释放
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//     cudaEventDestroy(start);
//     cudaEventDestroy(stop);
//
//     return 0;
// }
//
// // CUDA流实现（将高密度行均匀分配到多个流）
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 加载矩阵: /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 行数: 12471, 列数: 872622, 非零元数: 22624727
// // CSR转换完成
// // 开始执行SDDMM...
// // 使用 2 个CUDA流进行并行优化
// // [步骤1] 行非零元统计耗时: 2.428 ms
// // [步骤2] 动态分区耗时: 0.295 ms (高密度行: 11012, 低密度行: 1459)
// // 高密度区分块: 11012 行, 每流处理约 5506 行
// // [步骤3] 高密度区计算耗时: 134.662 ms
// // [步骤4] 低密度区计算耗时: 0.372 ms
// // SDDMM执行完成
// // SDDMM总执行时间: 137.525 ms
// // GFLOPS: 42.12
// // 开始CPU参考计算...
// // CPU参考计算完成
// // 验证结果:
// // 最大绝对误差: 1.52588e-05
// // 正确率: 100%
// //
// // 进程已结束，退出代码为 0
