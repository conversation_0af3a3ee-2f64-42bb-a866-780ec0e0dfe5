// #include <cuda_runtime.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
// #include <cstdint>
// #include <thrust/scan.h>
// #include <thrust/execution_policy.h>
// #include <cassert>
//
// // 常量定义
// const int VECTOR_SIZE = 4;
// const int HIGH_DENSITY_THREADS = 256;
// const int THREADS_PER_BLOCK = 256;
//
// // 稀疏矩阵结构体 (CSR格式)
// struct CSRMatrix {
//     int *row_ptr;
//     int *col_idx;
//     float *values;
//     int rows;
//     int cols;
//     int nnz;
// };
//
// // 线程块信息结构体
// struct BlockInfo {
//     int row;        // 行索引
//     int start_idx;  // 非零元素起始位置
// };
//
// // 计算每行非零元素数量
// __global__ void compute_row_nnz_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int rows
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         row_nnz[row] = row_ptr[row + 1] - row_ptr[row];
//     }
// }
//
// // 行分类内核
// __global__ void classify_rows_kernel(
//     const int *row_nnz,
//     int *d_flag_high,
//     int *d_flag_low,
//     int rows,
//     int threshold
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         int flag_high = (row_nnz[row] > threshold) ? 1 : 0;
//         int flag_low = 1 - flag_high;
//         d_flag_high[row] = flag_high;
//         d_flag_low[row] = flag_low;
//     }
// }
//
// // 重构的高密度区SDDMM内核
// __global__ void sddmm_high_density_per_element_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const BlockInfo *block_info,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int total_blocks
// ) {
//     extern __shared__ float shared_A[];
//     if (blockIdx.x >= total_blocks) return;
//
//     BlockInfo binfo = block_info[blockIdx.x];
//     int orig_row = binfo.row;
//     int start_idx = binfo.start_idx;
//
//     // 加载A行到共享内存
//     for (int k = threadIdx.x; k < K; k += blockDim.x) {
//         shared_A[k] = __ldg(&A[orig_row * K + k]);
//     }
//     __syncthreads();
//
//     // 计算当前线程处理的非零元素位置
//     int idx = start_idx + threadIdx.x;
//     int row_end = row_ptr[orig_row + 1];
//
//     if (idx < row_end) {
//         int col = __ldg(&col_idx[idx]);
//         float sum = 0.0f;
//
//         // 向量化计算
//         int k = 0;
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4*>(&shared_A[k]);
//             float4 b_vec = __ldg(reinterpret_cast<const float4*>(&B[col * K + k]));
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y
//                  + a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//         // 处理尾部
//         for (; k < K; ++k) {
//             sum += shared_A[k] * __ldg(&B[col * K + k]);
//         }
//
//         result[idx] = sum;
//     }
// }
//
// // 优化后的低密度区SDDMM内核
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_low_rows
// ) {
//     int row_in_group = blockIdx.x;
//     if (row_in_group >= num_low_rows) return;
//
//     int orig_row = row_indices[row_in_group];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     // 每个线程处理该行的一部分非零元素
//     for (int idx = threadIdx.x; idx < nnz_in_row; idx += blockDim.x) {
//         int col = __ldg(&col_idx[row_start + idx]);
//         float sum = 0.0f;
//
//         // 向量化计算
//         int k = 0;
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = __ldg(reinterpret_cast<const float4*>(&A[orig_row * K + k]));
//             float4 b_vec = __ldg(reinterpret_cast<const float4*>(&B[col * K + k]));
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y
//                  + a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//         // 处理尾部
//         for (; k < K; ++k) {
//             sum += __ldg(&A[orig_row * K + k]) * __ldg(&B[col * K + k]);
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 主SDDMM函数
// void sddmm_csr(
//     const float *d_A,
//     const float *d_B,
//     CSRMatrix &sparse,
//     int K
// ) {
//     // 直接在GPU上统计和分类
//     int *d_row_nnz, *d_high_rows, *d_low_rows;
//     int *d_flag_high, *d_flag_low;
//     int threshold = 32;  // 密度阈值
//
//     // 分配设备内存
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_low_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_flag_high, sparse.rows * sizeof(int));
//     cudaMalloc(&d_flag_low, sparse.rows * sizeof(int));
//
//     // 初始化标志数组
//     cudaMemset(d_flag_high, 0, sparse.rows * sizeof(int));
//     cudaMemset(d_flag_low, 0, sparse.rows * sizeof(int));
//
//     dim3 block(THREADS_PER_BLOCK);
//     dim3 grid((sparse.rows + block.x - 1) / block.x);
//
//     cudaEvent_t step1_start, step1_stop;
//     cudaEventCreate(&step1_start);
//     cudaEventCreate(&step1_stop);
//     cudaEventRecord(step1_start);
//
//     // 步骤1: 计算每行非零元素数量
//     compute_row_nnz_kernel<<<grid, block>>>(
//         sparse.row_ptr, d_row_nnz, sparse.rows
//     );
//     cudaDeviceSynchronize();
//     cudaError_t err = cudaGetLastError();
//     if (err != cudaSuccess) {
//         std::cerr << "compute_row_nnz_kernel failed: " << cudaGetErrorString(err) << std::endl;
//         return;
//     }
//
//     cudaEventRecord(step1_stop);
//     cudaEventSynchronize(step1_stop);
//     float step1_ms;
//     cudaEventElapsedTime(&step1_ms, step1_start, step1_stop);
//     printf("[步骤1] 低密度区计算耗时: %.3f ms\n", step1_ms);
//
//     cudaEvent_t step2_start, step2_stop;
//     cudaEventCreate(&step2_start);
//     cudaEventCreate(&step2_stop);
//     cudaEventRecord(step2_start);
//
//     // 步骤2: 分类行
//     classify_rows_kernel<<<grid, block>>>(
//         d_row_nnz, d_flag_high, d_flag_low,
//         sparse.rows, threshold
//     );
//     cudaDeviceSynchronize();
//     err = cudaGetLastError();
//     if (err != cudaSuccess) {
//         std::cerr << "classify_rows_kernel failed: " << cudaGetErrorString(err) << std::endl;
//         return;
//     }
//
//     cudaEventRecord(step2_stop);
//     cudaEventSynchronize(step2_stop);
//     float step2_ms;
//     cudaEventElapsedTime(&step1_ms, step1_start, step2_stop);
//     printf("[步骤2] 低密度区计算耗时: %.3f ms\n", step2_ms);
//
//     // 步骤3: 使用Thrust进行前缀和扫描
//     thrust::exclusive_scan(thrust::device, d_flag_high, d_flag_high + sparse.rows, d_high_rows);
//     thrust::exclusive_scan(thrust::device, d_flag_low, d_flag_low + sparse.rows, d_low_rows);
//
//     // 获取高/低密度行的数量
//     int last_flag_high, last_flag_low;
//     cudaMemcpy(&last_flag_high, d_flag_high + sparse.rows - 1, sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(&last_flag_low, d_flag_low + sparse.rows - 1, sizeof(int), cudaMemcpyDeviceToHost);
//
//     int h_high_count, h_low_count;
//     cudaMemcpy(&h_high_count, d_high_rows + sparse.rows - 1, sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(&h_low_count, d_low_rows + sparse.rows - 1, sizeof(int), cudaMemcpyDeviceToHost);
//
//     h_high_count += last_flag_high;
//     h_low_count += last_flag_low;
//
//     std::cout << "高密度行数: " << h_high_count << ", 低密度行数: " << h_low_count << std::endl;
//
//     // 计算共享内存大小
//     size_t shared_mem_size = K * sizeof(float);
//     cudaDeviceProp prop;
//     cudaGetDeviceProperties(&prop, 0);
//     if (shared_mem_size > prop.sharedMemPerBlock) {
//         std::cerr << "错误: 共享内存需求 (" << shared_mem_size
//                   << " bytes) 超过设备支持 (" << prop.sharedMemPerBlock << " bytes)" << std::endl;
//         return;
//     }
//
//     // ================= 重构高密度区处理逻辑 =================
//     if (h_high_count > 0) {
//         // 收集高密度行的实际行索引
//         std::vector<int> h_flag_high(sparse.rows);
//         cudaMemcpy(h_flag_high.data(), d_flag_high, sparse.rows * sizeof(int), cudaMemcpyDeviceToHost);
//
//         std::vector<int> h_high_rows_indices;
//         for (int i = 0; i < sparse.rows; ++i) {
//             if (h_flag_high[i] == 1) {
//                 h_high_rows_indices.push_back(i);
//             }
//         }
//
//         // 获取行信息
//         std::vector<int> h_row_nnz(sparse.rows);
//         std::vector<int> h_csr_row_ptr(sparse.rows + 1);
//         cudaMemcpy(h_row_nnz.data(), d_row_nnz, sparse.rows * sizeof(int), cudaMemcpyDeviceToHost);
//         cudaMemcpy(h_csr_row_ptr.data(), sparse.row_ptr, (sparse.rows + 1) * sizeof(int), cudaMemcpyDeviceToHost);
//
//         // 计算线程块信息
//         std::vector<BlockInfo> h_block_info;
//         for (int row : h_high_rows_indices) {
//             int row_start = h_csr_row_ptr[row];
//             int nnz_in_row = h_row_nnz[row];
//             int blocks_needed = (nnz_in_row + THREADS_PER_BLOCK - 1) / THREADS_PER_BLOCK;
//
//             for (int j = 0; j < blocks_needed; ++j) {
//                 BlockInfo binfo;
//                 binfo.row = row;
//                 binfo.start_idx = row_start + j * THREADS_PER_BLOCK;
//                 h_block_info.push_back(binfo);
//             }
//         }
//
//         // 复制到设备
//         BlockInfo *d_block_info;
//         cudaMalloc(&d_block_info, h_block_info.size() * sizeof(BlockInfo));
//         cudaMemcpy(d_block_info, h_block_info.data(), h_block_info.size() * sizeof(BlockInfo), cudaMemcpyHostToDevice);
//
//         // 启动重构的高密度内核
//         dim3 block_hd(THREADS_PER_BLOCK);
//         dim3 grid_hd(h_block_info.size());
//         std::cout << "启动高密度内核: " << grid_hd.x << " 个块, 每块 " << block_hd.x << " 线程" << std::endl;
//
//         cudaEvent_t step3_start, step3_stop;
//         cudaEventCreate(&step3_start);
//         cudaEventCreate(&step3_stop);
//         cudaEventRecord(step3_start);
//
//         sddmm_high_density_per_element_kernel<<<grid_hd, block_hd, shared_mem_size>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx,
//             d_block_info, sparse.values,
//             sparse.rows, K, sparse.cols,
//             h_block_info.size()
//         );
//         cudaDeviceSynchronize();
//
//         cudaEventRecord(step3_stop);
//         cudaEventSynchronize(step3_stop);
//         float step3_ms;
//         cudaEventElapsedTime(&step3_ms, step3_start, step3_stop);
//         printf("[步骤3] 高密度区计算耗时: %.3f ms\n", step3_ms);
//
//         err = cudaGetLastError();
//         if (err != cudaSuccess) {
//             std::cerr << "sddmm_high_density_per_element_kernel failed: " << cudaGetErrorString(err) << std::endl;
//         }
//         cudaFree(d_block_info);
//     }
//
//     cudaEvent_t step4_start, step4_stop;
//     cudaEventCreate(&step4_start);
//     cudaEventCreate(&step4_stop);
//     cudaEventRecord(step4_start);
//
//     // ================= 重构低密度区处理逻辑 =================
//     if (h_low_count > 0) {
//         // 收集低密度行的实际行索引
//         std::vector<int> h_flag_low(sparse.rows);
//         cudaMemcpy(h_flag_low.data(), d_flag_low, sparse.rows * sizeof(int), cudaMemcpyDeviceToHost);
//
//         std::vector<int> h_low_rows_indices;
//         for (int i = 0; i < sparse.rows; ++i) {
//             if (h_flag_low[i] == 1) {
//                 h_low_rows_indices.push_back(i);
//             }
//         }
//
//         // 复制到设备
//         int *d_low_rows_indices;
//         cudaMalloc(&d_low_rows_indices, h_low_count * sizeof(int));
//         cudaMemcpy(d_low_rows_indices, h_low_rows_indices.data(), h_low_count * sizeof(int), cudaMemcpyHostToDevice);
//
//         // 启动内核
//         dim3 block_ld(THREADS_PER_BLOCK);
//         dim3 grid_ld(h_low_count);
//         std::cout << "启动低密度内核: " << grid_ld.x << " 个块, 每块 " << block_ld.x << " 线程" << std::endl;
//
//         sddmm_low_density_kernel<<<grid_ld, block_ld>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx,
//             d_low_rows_indices, sparse.values,
//             sparse.rows, K, sparse.cols,
//             h_low_count
//         );
//         cudaDeviceSynchronize();
//         err = cudaGetLastError();
//         if (err != cudaSuccess) {
//             std::cerr << "sddmm_low_density_kernel failed: " << cudaGetErrorString(err) << std::endl;
//         }
//         cudaFree(d_low_rows_indices);
//     }
//
//     cudaEventRecord(step4_stop);
//     cudaEventSynchronize(step4_stop);
//     float step4_ms;
//     cudaEventElapsedTime(&step4_ms, step4_start, step4_stop);
//     printf("[步骤4] 低密度区计算耗时: %.3f ms\n", step4_ms);
//
//     // 释放资源
//     cudaFree(d_row_nnz);
//     cudaFree(d_high_rows);
//     cudaFree(d_low_rows);
//     cudaFree(d_flag_high);
//     cudaFree(d_flag_low);
// }
//
// // 从Matrix Market文件加载COO格式稀疏矩阵
// bool load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         return false;
//     }
//
//     std::string line;
//     // 跳过注释行
//     while (std::getline(file, line)) {
//         if (line[0] != '%') break;
//     }
//
//     // 读取矩阵元数据
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//     if (M <= 0 || N <= 0 || nnz <= 0) {
//         std::cerr << "无效的矩阵尺寸: M=" << M << ", N=" << N << ", nnz=" << nnz << std::endl;
//         return false;
//     }
//
//     // 调整向量大小
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     // 读取非零元素
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value;
//         if (!(file >> row >> col)) {
//             std::cerr << "读取非零元素失败，索引: " << i << std::endl;
//             return false;
//         }
//         if (file.peek() != '\n' && file.peek() != '\r') {
//             file >> value; // 如果有值则读取
//         }
//         // 转换为0-based索引
//         rows[i] = row - 1;
//         cols[i] = col - 1;
//
//         // 检查索引范围
//         if (rows[i] < 0 || rows[i] >= M || cols[i] < 0 || cols[i] >= N) {
//             std::cerr << "无效的索引: 行=" << rows[i] << ", 列=" << cols[i]
//                       << " (范围: 0-" << M-1 << ", 0-" << N-1 << ")" << std::endl;
//             return false;
//         }
//     }
//     file.close();
//     return true;
// }
//
// // 将COO格式转换为CSR格式
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx,
//                 int &max_nnz_per_row) {
//     // 初始化行指针
//     csr_row_ptr.resize(M + 1, 0);
//     max_nnz_per_row = 0;
//
//     // 计算每行非零元素数量
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         if (row < 0 || row >= M) {
//             std::cerr << "警告: 无效的行索引 " << row << " 在位置 " << i << std::endl;
//             continue;
//         }
//         csr_row_ptr[row]++;
//     }
//
//     // 前缀和计算行指针
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         if (temp > max_nnz_per_row) max_nnz_per_row = temp;
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     // 创建临时行计数器和列索引数组
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     // 填充列索引
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int col = cols[i];
//         if (row < 0 || row >= M) continue;
//         int index = csr_row_ptr[row] + row_counter[row];
//         if (index < 0 || index >= nnz) {
//             std::cerr << "警告: 无效的索引 " << index << " 在位置 " << i << std::endl;
//             continue;
//         }
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// __global__ void start(){}
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "Usage: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr));
//     std::string filename = argv[1];
//     int K = (argc > 2) ? std::atoi(argv[2]) : 128;
//
//     // 加载矩阵
//     int M, N, nnz, max_nnz_per_row = 0;
//     std::vector<int> coo_rows, coo_cols;
//     if (!load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols)) {
//         std::cerr << "矩阵加载失败" << std::endl;
//         return 1;
//     }
//
//     // 转换为CSR格式
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx, max_nnz_per_row);
//
//     // 初始化稠密矩阵
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     for (int i = 0; i < M * K; ++i) h_A[i] = (rand() % 100) / 100.0f;
//     for (int i = 0; i < N * K; ++i) h_B[i] = (rand() % 100) / 100.0f;
//
//     // 分配设备内存
//     float *d_A = nullptr, *d_B = nullptr;
//     cudaError_t err = cudaMalloc(&d_A, M * K * sizeof(float));
//     if (err != cudaSuccess) {
//         std::cerr << "cudaMalloc d_A failed: " << cudaGetErrorString(err) << std::endl;
//         return 1;
//     }
//     err = cudaMalloc(&d_B, N * K * sizeof(float));
//     if (err != cudaSuccess) {
//         std::cerr << "cudaMalloc d_B failed: " << cudaGetErrorString(err) << std::endl;
//         cudaFree(d_A);
//         return 1;
//     }
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//
//     // 设置稀疏矩阵结构
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//     sparse.row_ptr = nullptr;
//     sparse.col_idx = nullptr;
//     sparse.values = nullptr;
//
//     err = cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     if (err != cudaSuccess) {
//         std::cerr << "cudaMalloc row_ptr failed: " << cudaGetErrorString(err) << std::endl;
//         cudaFree(d_A); cudaFree(d_B);
//         return 1;
//     }
//     err = cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     if (err != cudaSuccess) {
//         std::cerr << "cudaMalloc col_idx failed: " << cudaGetErrorString(err) << std::endl;
//         cudaFree(d_A); cudaFree(d_B); cudaFree(sparse.row_ptr);
//         return 1;
//     }
//     err = cudaMalloc(&sparse.values, nnz * sizeof(float));
//     if (err != cudaSuccess) {
//         std::cerr << "cudaMalloc values failed: " << cudaGetErrorString(err) << std::endl;
//         cudaFree(d_A); cudaFree(d_B); cudaFree(sparse.row_ptr); cudaFree(sparse.col_idx);
//         return 1;
//     }
//
//     cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 打印设备信息
//     cudaDeviceProp prop;
//     cudaGetDeviceProperties(&prop, 0);
//     std::cout << "使用设备: " << prop.name << std::endl;
//     std::cout << "计算能力: " << prop.major << "." << prop.minor << std::endl;
//     std::cout << "共享内存/块: " << prop.sharedMemPerBlock << " bytes" << std::endl;
//     std::cout << "矩阵维度: " << M << " x " << N << std::endl;
//     std::cout << "非零元素: " << nnz << std::endl;
//     std::cout << "向量维度 K: " << K << std::endl;
//     std::cout << "最大每行非零数: " << max_nnz_per_row << std::endl;
//
//     // 预热GPU
//     std::cout << "预热GPU..." << std::endl;
//     start<<<1, 1>>>();
//
//     // 执行SDDMM
//     std::cout << "开始执行SDDMM..." << std::endl;
//
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//     cudaEventRecord(start);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     float ms = 0;
//     cudaEventElapsedTime(&ms, start, stop);
//     printf("SDDMM计算时间: %.3f ms\n", ms);
//     printf("计算吞吐量: %.3f GFLOP/s\n", (2.0 * nnz * K) / (ms * 1e6));
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     // 验证结果
//     std::vector<float> h_values_gpu(nnz);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     // CPU参考计算
//     std::vector<float> h_values_cpu(nnz);
//     for (int row = 0; row < M; ++row) {
//         int start = h_csr_row_ptr[row];
//         int end = h_csr_row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = h_csr_col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += h_A[row * K + k] * h_B[col * K + k];
//             }
//             h_values_cpu[idx] = sum;
//         }
//     }
//
//     // 结果对比
//     int correct = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_cpu[i] - h_values_gpu[i]);
//         if (diff < 1e-4) correct++;
//         if (diff > max_error) max_error = diff;
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct / nnz) << "%" << std::endl;
//
//     // 释放资源
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//     cudaEventDestroy(start);
//     cudaEventDestroy(stop);
//
//     return 0;
// }
//
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 使用设备: Tesla P100-PCIE-16GB
// // 计算能力: 6.0
// // 共享内存/块: 49152 bytes
// // 矩阵维度: 12471 x 872622
// // 非零元素: 22624727
// // 向量维度 K: 128
// // 最大每行非零数: 75355
// // 预热GPU...
// // 开始执行SDDMM...
// // [步骤1] 低密度区计算耗时: 0.020 ms
// // [步骤2] 低密度区计算耗时: 0.000 ms
// // 高密度行数: 11012, 低密度行数: 1459
// // 启动高密度内核: 94070 个块, 每块 256 线程
// // [步骤3] 高密度区计算耗时: 78.900 ms
// // 启动低密度内核: 1459 个块, 每块 256 线程
// // [步骤4] 低密度区计算耗时: 0.251 ms
// // SDDMM计算时间: 81.348 ms
// // 计算吞吐量: 71.200 GFLOP/s
// // SDDMM执行完成
// // 验证结果:
// // 最大绝对误差: 3.43323e-05
// // 正确率: 100%
// //
// // 进程已结束，退出代码为 0
//
// // 1.协作加载模式：
// // 所有线程共同将A矩阵的当前行加载到共享内存
// // 每个线程负责加载多个元素（间隔为blockDim.x）
// // 使用__syncthreads()确保共享内存数据就绪
// // 2.线程-元素映射：
// // 每个线程处理一个非零元素（idx = start_idx + threadIdx.x）
// // 通过block_info数组确定处理范围
// // 有效性检查：idx < row_ptr[orig_row + 1]
// // 3.计算模式：
// // 使用float4实现向量化内存访问
// // 每个线程计算完整的K维点积（A行向量与B列向量的内积）
// // 尾部处理保证任意K值都能正确计算
// // 4.性能优化：
// // 共享内存重用A行数据（避免全局内存重复访问）
// // __ldg()指令实现只读缓存
// // 向量化加载提高内存吞吐量