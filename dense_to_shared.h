#ifndef SPUTNIK_SDDMM_DENSE_TO_SHARED_H_
#define SPUTNIK_SDDMM_DENSE_TO_SHARED_H_

#include "basic_utils.h"
#include "common_utils.h"

template <typename LoadType, int kBlockItemsK, int kBlockWidth>
struct DenseToShared {
  static_assert(kBlockItemsK >= kBlockWidth,
                "Sparse tile K-items must be >= thread block width.");
  static_assert(kBlockItemsK % kBlockWidth == 0,
                "Sparse tile K-items must be divisible by block width.");
  static_assert((sizeof(LoadType) / sizeof(float)) <=
                    (kBlockItemsK / kBlockWidth),
                "The number of values per load must be <= values per thread.");

  //
  /// Static members.
  //

  // The number of values that will be loaded per-thread, per-load.
  static constexpr int kValuesPerLoad_ = sizeof(LoadType) / sizeof(float);

  // The number of data items in the k-dimension that each thread owns.
  static constexpr int kThreadItemsK_ =
      kBlockItemsK / kBlockWidth / kValuesPerLoad_;

  //
  /// Member variables.
  //

  // Pointer to the dense matrix in global memory.
  const LoadType *matrix_;

  // Register file framgnet for the loaded values.
  LoadType *matrix_fragment_;

  /**
   * @brief Set the initial pointer offsets.
   */
  __device__ __forceinline__ DenseToShared(int k, int m_index,
                                           const float *matrix,
                                           float *matrix_fragment) {
    matrix_ =
        reinterpret_cast<const LoadType *>(matrix + m_index * k) + threadIdx.x;
    matrix_fragment_ = reinterpret_cast<LoadType *>(matrix_fragment);
  }

  /**
   * @brief Strip-mine a 1-dimensional tile from the matrix.
   */
  __device__ __forceinline__ void Load() {
    #pragma unroll
    for (int k_item_idx = 0; k_item_idx < kThreadItemsK_; ++k_item_idx) {
      // Load the values into smem.
      Store(SPC::Load(matrix_), matrix_fragment_ + k_item_idx);

      // Increment our pointers for the next iteration.
      matrix_ += kBlockWidth;
    }
  }

  /**
   * @brief Loads any residual elements from the matrix.
   */
  __device__ __forceinline__ void Residue(int residue) {
    const LoadType *matrix = matrix_;
    #pragma unroll
    for (int k_item_idx = 0; k_item_idx < kThreadItemsK_; ++k_item_idx) {
      if (residue > 0) {
        // Load the values into smem.
        Store(SPC::Load(matrix), matrix_fragment_ + k_item_idx);
      }
      // Increment our pointer & value index for the next iteration.
      matrix += kBlockWidth;
      residue -= kBlockWidth * kValuesPerLoad_;
    }
  }
};

#endif  // SPUTNIK_SDDMM_DENSE_TO_SHARED_H_
