// #include <cuda_runtime.h>
// #include <cublas_v2.h>
// #include <vector>
// #include <unordered_map>
// #include <algorithm>
// #include <iostream>
// #include <cuda_fp16.h>
// #include <thrust/scan.h>
// #include <thrust/execution_policy.h>
//
// // 定义常量
// const int MAX_BUCKET_SIZE = 512; // 最大桶大小
// const int MAX_BUCKETS = 1024; // 最大桶数量
// const int NUM_STREAMS = 4; // 流数量
//
// // 传统SDDMM核函数 - 单精度
// __global__ void sddmm_kernel(
//     const float *Q, // [num_queries, dim]
//     const float *K, // [num_keys, dim]
//     float *output, // [num_queries, num_keys]
//     int num_queries,
//     int num_keys,
//     int dim
// ) {
//     int query_idx = blockIdx.y * blockDim.y + threadIdx.y;
//     int key_idx = blockIdx.x * blockDim.x + threadIdx.x;
//
//     if (query_idx < num_queries && key_idx < num_keys) {
//         float sum = 0.0f;
//         for (int d = 0; d < dim; d++) {
//             sum += Q[query_idx * dim + d] * K[key_idx * dim + d];
//         }
//         output[query_idx * num_keys + key_idx] = sum;
//     }
// }
//
// // 传统SDDMM核函数 - 半精度
// #if __CUDA_ARCH__ >= 700
// __global__ void sddmm_kernel_half(
//     const __half* Q,        // [num_queries, dim]
//     const __half* K,        // [num_keys, dim]
//     __half* output,         // [num_queries, num_keys]
//     int num_queries,
//     int num_keys,
//     int dim
// ) {
//     int query_idx = blockIdx.y * blockDim.y + threadIdx.y;
//     int key_idx = blockIdx.x * blockDim.x + threadIdx.x;
//
//     if (query_idx < num_queries && key_idx < num_keys) {
//         __half sum = __float2half(0.0f);
//         for (int d = 0; d < dim; d++) {
//             sum = __hadd(sum, __hmul(Q[query_idx * dim + d], K[key_idx * dim + d]));
//         }
//         output[query_idx * num_keys + key_idx] = sum;
//     }
// }
// #endif
//
// // 传统SDDMM实现
// cudaError_t traditional_sddmm(
//     const float *d_Q, // GPU上的Q矩阵 [num_queries, dim]
//     const float *d_K, // GPU上的K矩阵 [num_keys, dim]
//     float *d_output, // GPU输出矩阵 [num_queries, num_keys]
//     int num_queries,
//     int num_keys,
//     int dim,
//     bool use_half_precision = false
// ) {
//     // 设置执行配置
//     dim3 block(16, 16);
//     dim3 grid(
//         (num_keys + block.x - 1) / block.x,
//         (num_queries + block.y - 1) / block.y
//     );
//
//     // 选择核函数版本
//     if (use_half_precision) {
// #if __CUDA_ARCH__ >= 700
//         sddmm_kernel_half<<<grid, block>>>(
//             reinterpret_cast<const __half*>(d_Q),
//             reinterpret_cast<const __half*>(d_K),
//             reinterpret_cast<__half*>(d_output),
//             num_queries, num_keys, dim
//         );
// #else
//         // 回退到单精度
//         sddmm_kernel<<<grid, block>>>(
//             d_Q, d_K, d_output, num_queries, num_keys, dim
//         );
// #endif
//     } else {
//         sddmm_kernel<<<grid, block>>>(
//             d_Q, d_K, d_output, num_queries, num_keys, dim
//         );
//     }
//
//     return cudaGetLastError();
// }
//
// // 计算64位整数的汉明距离
// __device__ __host__ inline int hamming_distance(uint64_t a, uint64_t b) {
//     uint64_t x = a ^ b;
// #ifdef __CUDA_ARCH__ // 设备端使用内置函数
//     return __popcll(x);
// #else // 主机端使用位操作
//     int dist = 0;
//     while (x) {
//         dist++;
//         x &= x - 1;
//     }
//     return dist;
// #endif
// }
//
// // GPU核函数：初始化矩阵为负无穷大
// __global__ void init_matrix_kernel(float *matrix, int rows, int cols, float value) {
//     int row = blockIdx.y * blockDim.y + threadIdx.y;
//     int col = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows && col < cols) {
//         matrix[row * cols + col] = value;
//     }
// }
//
// // 优化的GEMM核函数（使用共享内存和向量化）
// __global__ void gemv_kernel(
//     const float *Q, // [num_queries, dim]
//     const float *K, // [num_keys, dim]
//     float *output, // [q_size, k_size]
//     const int *q_indices, // [q_size]
//     const int *k_indices, // [k_size]
//     int dim, int q_size, int k_size
// ) {
//     if (threadIdx.x == 0 && threadIdx.y == 0 && blockIdx.x == 0) {
//         printf("Running gemv_kernel: q_size=%d, k_size=%d\n", q_size, k_size);
//     }
//
//     extern __shared__ float shared_mem[]; // 动态共享内存
//
//     // 共享内存布局：
//     // 前半部分: 当前线程块处理的keys [blockDim.y * dim]
//     // 后半部分: 当前线程块处理的query [blockDim.x * dim]
//     float *shared_keys = shared_mem;
//     float *shared_query = shared_mem + blockDim.y * dim;
//
//     int local_q_idx = threadIdx.x; // 每个线程处理一个query
//     int local_k_idx = threadIdx.y; // 每个线程处理多个keys
//
//     int global_q_idx = (blockIdx.x * blockDim.x + threadIdx.x);
//     if (global_q_idx >= q_size) return;
//
//     // 获取全局query索引和指针
//     int q_global_idx = q_indices[global_q_idx];
//     const float *q_ptr = Q + q_global_idx * dim;
//
//     // 协作加载当前query到共享内存
//     for (int d = local_k_idx; d < dim; d += blockDim.y) {
//         shared_query[local_q_idx * dim + d] = q_ptr[d];
//     }
//
//     // 结果累加器
//     float results[8] = {0}; // 每个线程计算8个key的结果
//
//     // 遍历key的block
//     for (int kb = 0; kb < k_size; kb += blockDim.y) {
//         int global_k_idx = kb + local_k_idx;
//         __syncthreads();
//
//         // 协作加载当前key块到共享内存
//         if (global_k_idx < k_size) {
//             int k_global_idx = k_indices[global_k_idx];
//             const float *k_ptr = K + k_global_idx * dim;
//             for (int d = threadIdx.x; d < dim; d += blockDim.x) {
//                 shared_keys[local_k_idx * dim + d] = k_ptr[d];
//             }
//         }
//         __syncthreads();
//
//         // 计算当前key块的点积
//         for (int i = 0; i < min(blockDim.y, k_size - kb); i++) {
//             float sum = 0.0f;
// #pragma unroll(4)
//             for (int d = 0; d < dim; d++) {
//                 sum += shared_query[local_q_idx * dim + d] *
//                         shared_keys[i * dim + d];
//             }
//
//             if (kb + i < k_size) {
//                 results[i] = sum;
//             }
//         }
//
//         // 存储当前key块的结果
//         for (int i = 0; i < min(blockDim.y, k_size - kb); i++) {
//             if (kb + i < k_size) {
//                 output[global_q_idx * k_size + (kb + i)] = results[i];
//             }
//         }
//     }
// }
//
// // 半精度优化的GEMM核函数（支持Tensor Core）
// #if __CUDA_ARCH__ >= 700
// __global__ void gemv_kernel_half(
//     const __half* Q,          // [num_queries, dim]
//     const __half* K,          // [num_keys, dim]
//     __half* output,           // [q_size, k_size]
//     const int* q_indices,    // [q_size]
//     const int* k_indices,    // [k_size]
//     int dim, int q_size, int k_size
// ) {
//     extern __shared__ __half shared_mem[];
//     __half* shared_keys = shared_mem;
//     __half* shared_query = shared_mem + blockDim.y * dim;
//
//     int local_q_idx = threadIdx.x;
//     int local_k_idx = threadIdx.y;
//     int global_q_idx = blockIdx.x * blockDim.x + threadIdx.x;
//
//     if (global_q_idx >= q_size) return;
//
//     // 获取全局query索引和指针
//     int q_global_idx = q_indices[global_q_idx];
//     const __half* q_ptr = Q + q_global_idx * dim;
//
//     // 协作加载当前query到共享内存
//     for (int d = local_k_idx; d < dim; d += blockDim.y) {
//         shared_query[local_q_idx * dim + d] = q_ptr[d];
//     }
//
//     // 结果累加器
//     __half results[8] = {__float2half(0.0f)};
//
//     // 使用half2向量化加载
//     const int vec_dim = dim / 2;
//
//     // 遍历key的block
//     for (int kb = 0; kb < k_size; kb += blockDim.y) {
//         int global_k_idx = kb + local_k_idx;
//         __syncthreads();
//
//         // 协作加载当前key块到共享内存
//         if (global_k_idx < k_size) {
//             int k_global_idx = k_indices[global_k_idx];
//             const __half* k_ptr = K + k_global_idx * dim;
//
//             // 使用half2向量化加载
//             for (int d = threadIdx.x; d < vec_dim; d += blockDim.x) {
//                 int idx = d * 2;
//                 half2 val = *reinterpret_cast<const half2*>(k_ptr + idx);
//                 *reinterpret_cast<half2*>(&shared_keys[local_k_idx * dim + idx]) = val;
//             }
//         }
//         __syncthreads();
//
//         // 使用向量化计算点积
//         for (int i = 0; i < min(blockDim.y, k_size - kb); i++) {
//             __half sum = __float2half(0.0f);
//             for (int d = 0; d < vec_dim; d++) {
//                 int idx = d * 2;
//                 half2 q_val = *reinterpret_cast<half2*>(&shared_query[local_q_idx * dim + idx]);
//                 half2 k_val = *reinterpret_cast<half2*>(&shared_keys[i * dim + idx]);
//                 sum = __hadd(sum, __hadd(__hmul2(q_val, k_val).x);
//                 sum = __hadd(sum, __hadd(__hmul2(q_val, k_val).y);
//             }
//
//             if (kb + i < k_size) {
//                 results[i] = sum;
//             }
//         }
//
//         // 存储当前key块的结果
//         for (int i = 0; i < min(blockDim.y, k_size - kb); i++) {
//             if (kb + i < k_size) {
//                 output[global_q_idx * k_size + (kb + i)] = results[i];
//             }
//         }
//     }
// }
// #endif
//
// // 小桶核函数（简单实现）
// __global__ void small_bucket_kernel(
//     const float *Q,
//     const float *K,
//     float *output,
//     const int *q_indices,
//     const int *k_indices,
//     int dim, int q_size, int k_size
// ) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     int stride = blockDim.x * gridDim.x;
//
//     for (int idx = tid; idx < q_size * k_size; idx += stride) {
//         int q_idx = idx / k_size;
//         int k_idx = idx % k_size;
//
//         int global_q_idx = q_indices[q_idx];
//         int global_k_idx = k_indices[k_idx];
//
//         float sum = 0.0f;
//         for (int d = 0; d < dim; d++) {
//             sum += Q[global_q_idx * dim + d] * K[global_k_idx * dim + d];
//         }
//         output[idx] = sum;
//     }
// }
//
// // 核函数：计算每个桶的项目数
// __global__ void count_items_kernel(
//     const uint64_t *hashes,
//     int *bucket_counts,
//     int num_items
// ) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (idx < num_items) {
//         uint64_t hash = hashes[idx];
//         atomicAdd(&bucket_counts[hash], 1);
//     }
// }
//
// // 核函数：填充桶索引
// __global__ void fill_buckets_kernel(
//     const uint64_t *hashes,
//     int *bucket_offsets,
//     int *item_indices,
//     int num_items
// ) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (idx < num_items) {
//         uint64_t hash = hashes[idx];
//         int pos = atomicAdd(&bucket_offsets[hash], 1);
//         item_indices[pos] = idx;
//     }
// }
//
// // GPU桶结构
// struct GPUBucket {
//     int count;
//     uint64_t hash_value;
//     int start_offset;
// };
//
// // 在GPU上构建哈希桶
// cudaError_t build_buckets_gpu(
//     const uint64_t *d_hashes, // 设备端哈希值
//     int num_items, // 项目数量
//     int max_buckets, // 最大桶数量
//     int *d_bucket_counts, // 桶计数
//     int *d_bucket_offsets, // 桶偏移量
//     int *d_item_indices // 项目索引
// ) {
//     // 1. 重置桶计数
//     cudaMemset(d_bucket_counts, 0, max_buckets * sizeof(int));
//     cudaMemset(d_bucket_offsets, 0, max_buckets * sizeof(int));
//
//     // 2. 计算每个桶的项目数
//     dim3 countBlock(256);
//     dim3 countGrid((num_items + 255) / 256);
//
//     count_items_kernel<<<countGrid, countBlock>>>(
//         d_hashes, d_bucket_counts, num_items
//     );
//     cudaDeviceSynchronize();
//
//     // 3. 计算桶偏移量
//     thrust::exclusive_scan(thrust::device,
//                            d_bucket_counts, d_bucket_counts + max_buckets,
//                            d_bucket_offsets);
//
//     // 4. 重置桶计数用于填充
//     cudaMemset(d_bucket_counts, 0, max_buckets * sizeof(int));
//
//     // 5. 填充桶索引
//     fill_buckets_kernel<<<countGrid, countBlock>>>(
//         d_hashes, d_bucket_offsets, d_item_indices, num_items
//     );
//
//     return cudaGetLastError();
// }
//
// // 原子散射核函数
// __global__ void atomic_scatter_kernel(
//     const float *block_result,
//     float *global_matrix,
//     const int *q_indices,
//     const int *k_indices,
//     int block_rows,
//     int block_cols,
//     int global_cols
// ) {
//     int idx = blockIdx.x * blockDim.x + threadIdx.x;
//     int idy = blockIdx.y * blockDim.y + threadIdx.y;
//
//     if (idx < block_cols && idy < block_rows) {
//         int global_row = q_indices[idy];
//         int global_col = k_indices[idx];
//         float value = block_result[idy * block_cols + idx];
//
//         // 使用原子操作确保正确更新
//         atomicAdd(&global_matrix[global_row * global_cols + global_col], value);
//     }
// }
//
// // 内存池结构
// struct MemoryPool {
//     float *d_block_results;
//     int *d_indices;
// };
//
// // 初始化内存池
// cudaError_t init_memory_pool(MemoryPool *pool) {
//     size_t block_size = MAX_BUCKET_SIZE * MAX_BUCKET_SIZE * sizeof(float);
//     size_t indices_size = 2 * MAX_BUCKET_SIZE * MAX_BUCKETS * sizeof(int);
//
//     cudaMalloc(&pool->d_block_results, block_size);
//     cudaMalloc(&pool->d_indices, indices_size);
//
//     return cudaMemset(pool->d_block_results, 0, block_size);
// }
//
// // 主函数：哈希引导的SDDMM
// cudaError_t hashed_sddmm(
//     const float *d_Q,
//     const float *d_K,
//     const uint64_t *q_hashes,
//     const uint64_t *k_hashes,
//     float *d_output,
//     int num_queries,
//     int num_keys,
//     int dim,
//     int hash_length,
//     int hamming_threshold,
//     float negative_infinity,
//     bool use_half_precision,
//     int min_bucket_size
// ) {
//     cudaError_t err;
//
//     // 0. 初始化
//     // 创建CUDA流
//     cudaStream_t streams[NUM_STREAMS];
//     for (int i = 0; i < NUM_STREAMS; i++) {
//         cudaStreamCreate(&streams[i]);
//     }
//
//     // 初始化内存池
//     MemoryPool pool;
//     init_memory_pool(&pool);
//
//     // 1. 初始化输出矩阵为负无穷大
//     dim3 blockDim(16, 16);
//     dim3 gridDim((num_keys + 15) / 16, (num_queries + 15) / 16);
//     init_matrix_kernel<<<gridDim, blockDim>>>(d_output, num_queries, num_keys, negative_infinity);
//     cudaDeviceSynchronize();
//
//     // 2. GPU端创建哈希桶
//     // 设备内存分配
//     uint64_t *d_q_hashes, *d_k_hashes;
//     cudaMalloc(&d_q_hashes, num_queries * sizeof(uint64_t));
//     cudaMalloc(&d_k_hashes, num_keys * sizeof(uint64_t));
//     cudaMemcpy(d_q_hashes, q_hashes, num_queries * sizeof(uint64_t), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_k_hashes, k_hashes, num_keys * sizeof(uint64_t), cudaMemcpyHostToDevice);
//
//     // 桶数据结构
//     int *d_q_bucket_counts, *d_k_bucket_counts;
//     int *d_q_bucket_offsets, *d_k_bucket_offsets;
//     int *d_q_item_indices, *d_k_item_indices;
//
//     cudaMalloc(&d_q_bucket_counts, MAX_BUCKETS * sizeof(int));
//     cudaMalloc(&d_k_bucket_counts, MAX_BUCKETS * sizeof(int));
//     cudaMalloc(&d_q_bucket_offsets, MAX_BUCKETS * sizeof(int));
//     cudaMalloc(&d_k_bucket_offsets, MAX_BUCKETS * sizeof(int));
//     cudaMalloc(&d_q_item_indices, MAX_BUCKETS * MAX_BUCKET_SIZE * sizeof(int));
//     cudaMalloc(&d_k_item_indices, MAX_BUCKETS * MAX_BUCKET_SIZE * sizeof(int));
//
//     // 构建桶
//     build_buckets_gpu(d_q_hashes, num_queries, MAX_BUCKETS,
//                       d_q_bucket_counts, d_q_bucket_offsets, d_q_item_indices);
//     build_buckets_gpu(d_k_hashes, num_keys, MAX_BUCKETS,
//                       d_k_bucket_counts, d_k_bucket_offsets, d_k_item_indices);
//
//     // 3. 计算桶之间的相似关系（主机端）
//     // 将桶信息复制回主机
//     int *h_q_bucket_counts = new int[MAX_BUCKETS];
//     int *h_k_bucket_counts = new int[MAX_BUCKETS];
//     int *h_q_bucket_offsets = new int[MAX_BUCKETS];
//     int *h_k_bucket_offsets = new int[MAX_BUCKETS];
//
//     cudaMemcpy(h_q_bucket_counts, d_q_bucket_counts, MAX_BUCKETS * sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(h_k_bucket_counts, d_k_bucket_counts, MAX_BUCKETS * sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(h_q_bucket_offsets, d_q_bucket_offsets, MAX_BUCKETS * sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(h_k_bucket_offsets, d_k_bucket_offsets, MAX_BUCKETS * sizeof(int), cudaMemcpyDeviceToHost);
//
//     std::vector<std::pair<int, int> > similar_buckets;
//     for (int i = 0; i < MAX_BUCKETS; ++i) {
//         if (h_q_bucket_counts[i] == 0) continue;
//         for (int j = 0; j < MAX_BUCKETS; ++j) {
//             if (h_k_bucket_counts[j] == 0) continue;
//             if (hamming_distance(i, j) <= hamming_threshold) {
//                 similar_buckets.emplace_back(i, j);
//             }
//         }
//     }
//
//     // 4. 并行处理相似桶对
//     for (int i = 0; i < similar_buckets.size(); ++i) {
//         int stream_id = i % NUM_STREAMS;
//         auto &bucket_pair = similar_buckets[i];
//         int q_bucket_id = bucket_pair.first;
//         int k_bucket_id = bucket_pair.second;
//
//         int q_size = h_q_bucket_counts[q_bucket_id];
//         int k_size = h_k_bucket_counts[k_bucket_id];
//
//         if (q_size == 0 || k_size == 0)
//             continue;
//
//         // 记录处理的桶对
//         std::cout << "Processing bucket pair (" << q_bucket_id << ", " << k_bucket_id
//                   << ") sizes: " << q_size << "x" << k_size << std::endl;
//
//         // 5. 从内存池获取资源
//         float *d_block_result = pool.d_block_results;
//         int *d_q_indices = pool.d_indices + (2 * i * MAX_BUCKET_SIZE);
//         int *d_k_indices = d_q_indices + MAX_BUCKET_SIZE;
//
//         // 6. 复制桶索引到设备
//         int q_offset = h_q_bucket_offsets[q_bucket_id];
//         int k_offset = h_k_bucket_offsets[k_bucket_id];
//
//         cudaMemcpyAsync(d_q_indices, d_q_item_indices + q_offset,
//                         q_size * sizeof(int), cudaMemcpyDeviceToDevice, streams[stream_id]);
//         cudaMemcpyAsync(d_k_indices, d_k_item_indices + k_offset,
//                         k_size * sizeof(int), cudaMemcpyDeviceToDevice, streams[stream_id]);
//
//         // 7. 选择并执行核函数
//         if (q_size >= min_bucket_size && k_size >= min_bucket_size) {
//             dim3 gemmBlock(32, 8); // 256 threads
//             dim3 gemmGrid((q_size + 31) / 32);
//             size_t shared_mem_size = (32 * dim + 8 * dim) * sizeof(float);
//
//             if (use_half_precision) {
//                 // 半精度版本
//             } else {
//                 gemv_kernel<<<gemmGrid, gemmBlock, shared_mem_size, streams[stream_id]>>>(
//                     d_Q, d_K, d_block_result,
//                     d_q_indices, d_k_indices,
//                     dim, q_size, k_size
//                 );
//             }
//         } else {
//             // 小桶处理
//             dim3 gemmBlock(256);
//             dim3 gemmGrid((q_size * k_size + 255) / 256);
//
//             if (use_half_precision) {
//                 // 半精度版本
//             } else {
//                 small_bucket_kernel<<<gemmGrid, gemmBlock, 0, streams[stream_id]>>>(
//                     d_Q, d_K, d_block_result,
//                     d_q_indices, d_k_indices,
//                     dim, q_size, k_size
//                 );
//             }
//         }
//
//         // 8. 原子散射结果
//         dim3 scatterBlocks(16, 16);
//         dim3 scatterGrid(
//             (k_size + 15) / 16,
//             (q_size + 15) / 16
//         );
//
//         atomic_scatter_kernel<<<scatterGrid, scatterBlocks, 0, streams[stream_id]>>>(
//             d_block_result, d_output,
//             d_q_indices, d_k_indices,
//             q_size, k_size, num_keys
//         );
//     }
//
//     // 9. 同步所有流
//     for (int i = 0; i < NUM_STREAMS; i++) {
//         cudaStreamSynchronize(streams[i]);
//     }
//
//     // 10. 清理资源
//     for (int i = 0; i < NUM_STREAMS; i++) {
//         cudaStreamDestroy(streams[i]);
//     }
//
//     delete[] h_q_bucket_counts;
//     delete[] h_k_bucket_counts;
//     delete[] h_q_bucket_offsets;
//     delete[] h_k_bucket_offsets;
//
//     cudaFree(d_q_hashes);
//     cudaFree(d_k_hashes);
//     cudaFree(d_q_bucket_counts);
//     cudaFree(d_k_bucket_counts);
//     cudaFree(d_q_bucket_offsets);
//     cudaFree(d_k_bucket_offsets);
//     cudaFree(d_q_item_indices);
//     cudaFree(d_k_item_indices);
//     cudaFree(pool.d_block_results);
//     cudaFree(pool.d_indices);
//
//     return cudaSuccess;
// }
//
// // 主函数 - 对比实验
// int main(int argc, char **argv) {
//     if (argc < 5) {
//         std::cerr << "Usage: " << argv[0] << " <matrix.mtx> <dim> <num_queries> <num_keys>" << std::endl;
//         return 1;
//     }
//
//     // 解析参数
//     const char *matrix_file = argv[1];
//     const int dim = atoi(argv[2]);
//     const int num_queries = atoi(argv[3]);
//     const int num_keys = atoi(argv[4]);
//
//     // 参数设置
//     const int hash_length = 64;
//     const int hamming_threshold = 3;
//     const float negative_infinity = -1e9f;
//     const bool use_half_precision = false; // 禁用半精度保证正确性
//     const int min_bucket_size = 32;
//
//     // 分配GPU内存
//     float *d_Q, *d_K, *d_output, *d_traditional_output;
//     cudaMalloc(&d_Q, num_queries * dim * sizeof(float));
//     cudaMalloc(&d_K, num_keys * dim * sizeof(float));
//     cudaMalloc(&d_output, num_queries * num_keys * sizeof(float));
//     cudaMalloc(&d_traditional_output, num_queries * num_keys * sizeof(float));
//
//     // 初始化数据（实际应用中应从文件加载）
//     std::vector<float> h_Q(num_queries * dim);
//     std::vector<float> h_K(num_keys * dim);
//     for (int i = 0; i < num_queries * dim; i++) {
//         h_Q[i] = static_cast<float>(rand()) / RAND_MAX;
//     }
//     for (int j = 0; j < num_keys * dim; j++) {
//         h_K[j] = static_cast<float>(rand()) / RAND_MAX;
//     }
//
//     // 生成哈希值
//     std::vector<uint64_t> q_hashes(num_queries);
//     std::vector<uint64_t> k_hashes(num_keys);
//     for (int i = 0; i < num_queries; i++) {
//         q_hashes[i] = rand() % MAX_BUCKETS;
//     }
//     for (int j = 0; j < num_keys; j++) {
//         k_hashes[j] = rand() % MAX_BUCKETS;
//     }
//
//     // 复制数据到设备
//     cudaMemcpy(d_Q, h_Q.data(), num_queries * dim * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_K, h_K.data(), num_keys * dim * sizeof(float), cudaMemcpyHostToDevice);
//
//     // 计时变量
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//     float elapsed_time;
//
//     // 运行传统SDDMM
//     cudaEventRecord(start);
//     traditional_sddmm(d_Q, d_K, d_traditional_output, num_queries, num_keys, dim, use_half_precision);
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     cudaEventElapsedTime(&elapsed_time, start, stop);
//     std::cout << "Traditional SDDMM completed in " << elapsed_time << " ms" << std::endl;
//
//     // 运行优化SDDMM
//     cudaEventRecord(start);
//     cudaError_t err = hashed_sddmm(
//         d_Q, d_K,
//         q_hashes.data(), k_hashes.data(),
//         d_output,
//         num_queries, num_keys, dim,
//         hash_length, hamming_threshold,
//         negative_infinity,
//         use_half_precision,
//         min_bucket_size
//     );
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     cudaEventElapsedTime(&elapsed_time, start, stop);
//
//     if (err != cudaSuccess) {
//         std::cerr << "Hashed SDDMM failed: " << cudaGetErrorString(err) << std::endl;
//     } else {
//         std::cout << "Hashed SDDMM completed in " << elapsed_time << " ms" << std::endl;
//     }
//
//     // 验证结果一致性
//     std::vector<float> h_traditional_output(num_queries * num_keys);
//     std::vector<float> h_hashed_output(num_queries * num_keys);
//
//     cudaMemcpy(h_traditional_output.data(), d_traditional_output,
//                num_queries * num_keys * sizeof(float), cudaMemcpyDeviceToHost);
//     cudaMemcpy(h_hashed_output.data(), d_output,
//                num_queries * num_keys * sizeof(float), cudaMemcpyDeviceToHost);
//
//     float max_diff = 0.0f;
//     int error_count = 0;
//     int computed_count = 0;
//
//     for (int i = 0; i < num_queries; i++) {
//         for (int j = 0; j < num_keys; j++) {
//             float trad_val = h_traditional_output[i * num_keys + j];
//             float hashed_val = h_hashed_output[i * num_keys + j];
//
//             // 忽略未计算的位置
//             if (hashed_val == negative_infinity) continue;
//
//             computed_count++;
//             float diff = fabs(trad_val - hashed_val);
//             if (diff > 1e-3) {
//                 error_count++;
//                 if (diff > max_diff) max_diff = diff;
//             }
//         }
//     }
//
//     float error_rate = (computed_count > 0) ? (100.0f * error_count / computed_count) : 0;
//     std::cout << "Computed elements: " << computed_count << "/" << num_queries * num_keys
//             << " (" << (100.0f * computed_count / (num_queries * num_keys)) << "%)" << std::endl;
//     std::cout << "Mismatched elements: " << error_count << "/" << computed_count
//             << " (" << error_rate << "%)" << std::endl;
//     std::cout << "Max difference: " << max_diff << std::endl;
//
//     // 清理内存
//     cudaFree(d_Q);
//     cudaFree(d_K);
//     cudaFree(d_output);
//     cudaFree(d_traditional_output);
//     cudaEventDestroy(start);
//     cudaEventDestroy(stop);
//
//     return 0;
// }
