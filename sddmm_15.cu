// #include <cuda_runtime.h>
// #include <thrust/sort.h>
// #include <thrust/device_ptr.h>
// #include <thrust/gather.h>
// #include <thrust/scan.h>
// #include <thrust/sequence.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
//
// // 常量定义
// const int TILE_SIZE = 32; // 分块大小
// const int VECTOR_SIZE = 4; // 向量化加载大小(float4)
// const int HIGH_DENSITY_THREADS = 256; // 高密度区线程数/块
//
// // 稀疏矩阵结构体 (CSR格式)
// struct CSRMatrix {
//     int *row_ptr; // 行指针
//     int *col_idx; // 列索引
//     float *values; // 非零值 (SDDMM结果存放位置)
//     int rows; // 矩阵行数
//     int cols; // 矩阵列数
//     int nnz; // 非零元数量
// };
//
// // GPU端行非零元统计内核
// __global__ void compute_row_nnz_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int rows
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         row_nnz[row] = row_ptr[row + 1] - row_ptr[row];
//     }
// }
//
// __global__ void reorder_col_idx_kernel(
//     const int *row_permutation, // 行重排映射
//     const int *orig_row_ptr, // 原始行指针
//     const int *orig_col_idx, // 原始列索引
//     const int *sorted_row_ptr, // 排序后行指针
//     int *sorted_col_idx, // 排序后列索引
//     int rows // 总行数
// ) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     if (tid >= rows) return;
//
//     // 获取原始行号
//     int orig_row = row_permutation[tid];
//
//     // 原始行数据范围
//     int orig_start = orig_row_ptr[orig_row];
//     int orig_end = orig_row_ptr[orig_row + 1];
//     int nnz_in_row = orig_end - orig_start;
//
//     // 排序后行数据范围
//     int sorted_start = sorted_row_ptr[tid];
//
//     // 复制列索引
//     for (int i = 0; i < nnz_in_row; i++) {
//         sorted_col_idx[sorted_start + i] = orig_col_idx[orig_start + i];
//     }
// }
//
// // 结果还原内核
// __global__ void restore_results_kernel(
//     const int *row_permutation,
//     const int *orig_row_ptr,
//     const int *sorted_row_ptr,
//     const float *sorted_values,
//     float *orig_values,
//     int rows
// ) {
//     int tid = blockIdx.x * blockDim.x + threadIdx.x;
//     if (tid >= rows) return;
//
//     int orig_row = row_permutation[tid];
//     int sorted_start = sorted_row_ptr[tid];
//     int orig_start = orig_row_ptr[orig_row];
//     int nnz_in_row = orig_row_ptr[orig_row + 1] - orig_start;
//
//     for (int i = 0; i < nnz_in_row; i++) {
//         orig_values[orig_start + i] = sorted_values[sorted_start + i];
//     }
// }
//
// // 高密度区SDDMM内核 (每行非零元 > 阈值)
// __global__ void sddmm_high_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ sorted_row_ptr,
//     const int *__restrict__ sorted_col_idx,
//     const int *__restrict__ row_permutation, // 行映射
//     float *__restrict__ result,
//     int M, int K, int N,
//     int start_row, int end_row
// ) {
//     // 共享内存声明
//     __shared__ float As[TILE_SIZE][TILE_SIZE];
//     __shared__ float Bs[TILE_SIZE][TILE_SIZE];
//
//     int row_id = blockIdx.y;
//     int global_row = start_row + row_id;
//     if (global_row >= end_row) return;
//
//     // 通过映射获取原始行号
//     const int orig_row = row_permutation[global_row];
//     const int row_start = sorted_row_ptr[global_row];
//     const int row_end = sorted_row_ptr[global_row + 1];
//     const int nnz_in_row = row_end - row_start;
//
//     int tid = threadIdx.y * blockDim.x + threadIdx.x;
//     int threads_per_block = blockDim.x * blockDim.y;
//
//     for (int idx = tid; idx < nnz_in_row; idx += threads_per_block) {
//         const int col = sorted_col_idx[row_start + idx];
//         float sum = 0.0f;
//
//         for (int t = 0; t < K; t += TILE_SIZE) {
//             // 协作加载A的分块
//             if (threadIdx.y == 0 && threadIdx.x < TILE_SIZE) {
//                 int load_idx = t + threadIdx.x;
//                 if (load_idx < K) {
//                     As[0][threadIdx.x] = A[orig_row * K + load_idx];
//                 }
//             }
//
//             // 协作加载B的分块
//             if (threadIdx.x == 0 && threadIdx.y < TILE_SIZE) {
//                 int load_idx = t + threadIdx.y;
//                 if (load_idx < K) {
//                     Bs[threadIdx.y][0] = B[col * K + load_idx];
//                 }
//             }
//             __syncthreads();
//
//             // 计算分块乘积
//             int steps = min(TILE_SIZE, K - t);
//             for (int k = 0; k < steps; ++k) {
//                 sum += As[0][k] * Bs[k][0];
//             }
//             __syncthreads();
//         }
//         result[row_start + idx] = sum;
//     }
// }
//
// // 低密度区SDDMM内核 (每行非零元 ≤ 阈值)
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ sorted_row_ptr,
//     const int *__restrict__ sorted_col_idx,
//     const int *__restrict__ row_permutation, // 行映射
//     float *__restrict__ result,
//     int M, int K, int N,
//     int start_row, int end_row
// ) {
//     int global_idx = blockIdx.x * blockDim.x + threadIdx.x;
//     if (global_idx >= (end_row - start_row)) return;
//
//     int global_row = start_row + global_idx;
//     int orig_row = row_permutation[global_row]; // 通过映射获取
//     int row_start = sorted_row_ptr[global_row];
//     int row_end = sorted_row_ptr[global_row + 1];
//
//     for (int idx = row_start; idx < row_end; ++idx) {
//         int col = sorted_col_idx[idx];
//         float sum = 0.0f;
//
//         // 处理完整向量
//         int k = 0;
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4 *>(&A[orig_row * K + k]);
//             float4 b_vec = *reinterpret_cast<const float4 *>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x;
//             sum += a_vec.y * b_vec.y;
//             sum += a_vec.z * b_vec.z;
//             sum += a_vec.w * b_vec.w;
//         }
//
//         // 处理剩余元素
//         for (; k < K; ++k) {
//             sum += A[orig_row * K + k] * B[col * K + k];
//         }
//         result[idx] = sum;
//     }
// }
//
// // 主SDDMM函数
// void sddmm_csr(
//     const float *d_A, // 设备端稠密矩阵A (MxK)
//     const float *d_B, // 设备端稠密矩阵B (KxN)
//     CSRMatrix &sparse, // 稀疏矩阵结构 (CSR格式)
//     int K // 公共维度
// ) {
//     // 步骤1计时事件
//     cudaEvent_t step1_start, step1_stop;
//     cudaEventCreate(&step1_start);
//     cudaEventCreate(&step1_stop);
//     cudaEventRecord(step1_start);
//
//     // 1. 统计每行非零元素数量
//     int *d_row_nnz;
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//
//     dim3 block(256);
//     dim3 grid((sparse.rows + block.x - 1) / block.x);
//     compute_row_nnz_kernel<<<grid, block>>>(sparse.row_ptr, d_row_nnz, sparse.rows);
//     cudaDeviceSynchronize();
//
//     cudaEventRecord(step1_stop);
//     cudaEventSynchronize(step1_stop);
//     float step1_ms;
//     cudaEventElapsedTime(&step1_ms, step1_start, step1_stop);
//     printf("[步骤1] 行非零元统计耗时: %.3f ms\n", step1_ms);
//
//     // 保存原始行非零元数据用于分区计算
//     int *d_orig_row_nnz;
//     cudaMalloc(&d_orig_row_nnz, sparse.rows * sizeof(int));
//     cudaMemcpy(d_orig_row_nnz, d_row_nnz, sparse.rows * sizeof(int), cudaMemcpyDeviceToDevice);
//
//     // 步骤2计时事件
//     cudaEvent_t step2_start, step2_stop;
//     cudaEventCreate(&step2_start);
//     cudaEventCreate(&step2_stop);
//     cudaEventRecord(step2_start);
//
//     // 2. 生成行重排索引
//     int *d_row_permutation;
//     cudaMalloc(&d_row_permutation, sparse.rows * sizeof(int));
//     thrust::sequence(thrust::device, d_row_permutation, d_row_permutation + sparse.rows);
//
//     cudaEventRecord(step2_stop);
//     cudaEventSynchronize(step2_stop);
//     float step2_ms;
//     cudaEventElapsedTime(&step2_ms, step2_start, step2_stop);
//     printf("[步骤2] 行索引生成耗时: %.3f ms\n", step2_ms);
//
//     // 步骤3计时事件
//     cudaEvent_t step3_start, step3_stop;
//     cudaEventCreate(&step3_start);
//     cudaEventCreate(&step3_stop);
//     cudaEventRecord(step3_start);
//
//     // 3. 按非零元素数量降序排序
//     thrust::sort_by_key(
//         thrust::device,
//         d_row_nnz, d_row_nnz + sparse.rows,
//         d_row_permutation,
//         thrust::greater<int>()
//     );
//
//     cudaEventRecord(step3_stop);
//     cudaEventSynchronize(step3_stop);
//     float step3_ms;
//     cudaEventElapsedTime(&step3_ms, step3_start, step3_stop);
//     printf("[步骤3] 行排序耗时: %.3f ms\n", step3_ms);
//
//     // 步骤4计时事件
//     cudaEvent_t step4_start, step4_stop;
//     cudaEventCreate(&step4_start);
//     cudaEventCreate(&step4_stop);
//     cudaEventRecord(step4_start);
//
//     // 4. 构建排序后的行指针数组 (前缀和)
//     int *d_sorted_row_ptr;
//     cudaMalloc(&d_sorted_row_ptr, (sparse.rows + 1) * sizeof(int));
//
//     thrust::device_ptr<int> d_nnz_ptr(d_row_nnz);
//     thrust::device_ptr<int> d_sorted_ptr(d_sorted_row_ptr);
//
//     d_sorted_ptr[0] = 0;
//     thrust::inclusive_scan(
//         thrust::device,
//         d_nnz_ptr, d_nnz_ptr + sparse.rows,
//         d_sorted_ptr + 1
//     );
//     cudaDeviceSynchronize();
//
//     cudaEventRecord(step4_stop);
//     cudaEventSynchronize(step4_stop);
//     float step4_ms;
//     cudaEventElapsedTime(&step4_ms, step4_start, step4_stop);
//     printf("[步骤4] 构建行指针数组耗时: %.3f ms\n", step4_ms);
//
//     //步骤5计时事件
//     cudaEvent_t step5_start, step5_stop;
//     cudaEventCreate(&step5_start);
//     cudaEventCreate(&step5_stop);
//     cudaEventRecord(step5_start);
//
//     // 5. 重排列索引
//     int *d_sorted_col_idx;
//     cudaMalloc(&d_sorted_col_idx, sparse.nnz * sizeof(int));
//
//     // 内核重排列索引
//     dim3 reorder_block(256);
//     dim3 reorder_grid((sparse.rows + reorder_block.x - 1) / reorder_block.x);
//     reorder_col_idx_kernel<<<reorder_grid, reorder_block>>>(
//         d_row_permutation,
//         sparse.row_ptr,
//         sparse.col_idx,
//         d_sorted_row_ptr,
//         d_sorted_col_idx,
//         sparse.rows
//     );
//     cudaDeviceSynchronize();
//
//     cudaEventRecord(step5_stop);
//     cudaEventSynchronize(step5_stop);
//     float step5_ms;
//     cudaEventElapsedTime(&step5_ms, step5_start, step5_stop);
//     printf("[步骤5] 重排列索引耗时: %.3f ms\n", step5_ms);
//
//     //步骤6计时事件
//     cudaEvent_t step6_start, step6_stop;
//     cudaEventCreate(&step6_start);
//     cudaEventCreate(&step6_stop);
//     cudaEventRecord(step6_start);
//
//     // 6. 动态分区计算
//     int avg_nnz = sparse.nnz / sparse.rows;
//     int threshold = 32; // 经验阈值
//
//     // 使用原始行非零元数据查找分区点
//     int *h_orig_row_nnz = new int[sparse.rows];
//     cudaMemcpy(h_orig_row_nnz, d_orig_row_nnz, sparse.rows * sizeof(int), cudaMemcpyDeviceToHost);
//
//     int split_index = sparse.rows;
//     for (int i = 0; i < sparse.rows; i++) {
//         if (h_orig_row_nnz[i] <= threshold) {
//             split_index = i;
//             break;
//         }
//     }
//     delete[] h_orig_row_nnz;
//
//     cudaEventRecord(step6_stop);
//     cudaEventSynchronize(step6_stop);
//     float step6_ms;
//     cudaEventElapsedTime(&step6_ms, step6_start, step6_stop);
//     printf("[步骤6] 动态分区计算耗时: %.3f ms\n", step6_ms);
//
//     //步骤7计时事件
//     cudaEvent_t step7_start, step7_stop;
//     cudaEventCreate(&step7_start);
//     cudaEventCreate(&step7_stop);
//     cudaEventRecord(step7_start);
//
//     // 7. 高密度区计算
//     if (split_index > 0) {
//         dim3 block_hd(32, 8); // 256线程/块 (32x8)
//         dim3 grid_hd(1, split_index);
//
//         sddmm_high_density_kernel<<<grid_hd, block_hd>>>(
//             d_A, d_B,
//             d_sorted_row_ptr,
//             d_sorted_col_idx,
//             d_row_permutation,
//             sparse.values,
//             sparse.rows, K, sparse.cols,
//             0, split_index
//         );
//         cudaDeviceSynchronize();
//     }
//
//     cudaEventRecord(step7_stop);
//     cudaEventSynchronize(step7_stop);
//     float step7_ms;
//     cudaEventElapsedTime(&step7_ms, step7_start, step7_stop);
//     printf("[步骤7] 高密度区计算耗时: %.3f ms\n", step7_ms);
//
//     //步骤8计时事件
//     cudaEvent_t step8_start, step8_stop;
//     cudaEventCreate(&step8_start);
//     cudaEventCreate(&step8_stop);
//     cudaEventRecord(step8_start);
//
//     // 8. 低密度区计算
//     if (split_index < sparse.rows) {
//         int low_rows = sparse.rows - split_index;
//         dim3 block_ld(256);
//         dim3 grid_ld((low_rows + block_ld.x - 1) / block_ld.x);
//
//         sddmm_low_density_kernel<<<grid_ld, block_ld>>>(
//             d_A, d_B,
//             d_sorted_row_ptr,
//             d_sorted_col_idx,
//             d_row_permutation,
//             sparse.values,
//             sparse.rows, K, sparse.cols,
//             split_index, sparse.rows
//         );
//         cudaDeviceSynchronize();
//     }
//
//     cudaEventRecord(step8_stop);
//     cudaEventSynchronize(step8_stop);
//     float step8_ms;
//     cudaEventElapsedTime(&step8_ms, step8_start, step8_stop);
//     printf("[步骤8] 低密度区计算耗时: %.3f ms\n", step8_ms);
//
//     cudaEvent_t step9_start, step9_stop;
//     cudaEventCreate(&step9_start);
//     cudaEventCreate(&step9_stop);
//     cudaEventRecord(step9_start);
//
//     // 9. 还原结果到原始顺序
//     float *d_orig_values;
//     cudaMalloc(&d_orig_values, sparse.nnz * sizeof(float));
//
//     dim3 restore_block(256);
//     dim3 restore_grid((sparse.rows + restore_block.x - 1) / restore_block.x);
//     restore_results_kernel<<<restore_grid, restore_block>>>(
//         d_row_permutation,
//         sparse.row_ptr,
//         d_sorted_row_ptr,
//         sparse.values,
//         d_orig_values,
//         sparse.rows
//     );
//     cudaDeviceSynchronize();
//
//     // 将还原后的结果复制回原始值数组
//     cudaMemcpy(sparse.values, d_orig_values, sparse.nnz * sizeof(float), cudaMemcpyDeviceToDevice);
//     cudaFree(d_orig_values);
//
//     cudaEventRecord(step9_stop);
//     cudaEventSynchronize(step9_stop);
//     float step9_ms;
//     cudaEventElapsedTime(&step9_ms, step9_start, step9_stop);
//     printf("[步骤9] 还原结果到原始顺序耗时: %.3f ms\n", step9_ms);
//
//     // 10. 释放临时内存
//     cudaFree(d_row_nnz);
//     cudaFree(d_orig_row_nnz);
//     cudaFree(d_row_permutation);
//     cudaFree(d_sorted_row_ptr);
//     cudaFree(d_sorted_col_idx);
// }
//
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// // 从Matrix Market文件加载COO格式稀疏矩阵
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     // 跳过注释行
//     while (std::getline(file, line) && line[0] == '%');
//
//     // 读取矩阵元数据
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     // 调整向量大小
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     // 读取非零元素
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value; // 忽略值，只关心位置
//         file >> row >> col;
//         if (file.peek() != '\n') {
//             file >> value; // 如果有值则读取
//         }
//         // 转换为0-based索引
//         rows[i] = row - 1;
//         cols[i] = col - 1;
//     }
//     file.close();
// }
//
// // 将COO格式转换为CSR格式
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx) {
//     // 初始化行指针
//     csr_row_ptr.resize(M + 1, 0);
//
//     // 计算每行非零元素数量
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[rows[i]]++;
//     }
//
//     // 前缀和计算行指针
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     // 创建临时行计数器和列索引数组
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     // 填充列索引
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int col = cols[i];
//         int index = csr_row_ptr[row] + row_counter[row];
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "用法: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr)); // 初始化随机种子
//
//     std::string filename = argv[1];
//     int K = 128; // 默认公共维度
//     if (argc > 2) K = std::atoi(argv[2]);
//
//     // 1. 从文件加载稀疏矩阵
//     int M, N, nnz;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//     std::cout << "加载矩阵: " << filename << std::endl;
//     std::cout << "行数: " << M << ", 列数: " << N << ", 非零元数: " << nnz << std::endl;
//
//     // 2. 转换为CSR格式
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx);
//     std::cout << "CSR转换完成" << std::endl;
//
//     // 3. 分配主机内存并初始化稠密矩阵
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     std::generate(h_A.begin(), h_A.end(), []() { return (rand() % 100) / 100.0f; });
//     std::generate(h_B.begin(), h_B.end(), []() { return (rand() % 100) / 100.0f; });
//
//     // 4. 分配设备内存
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//
//     // 5. 设置稀疏矩阵结构
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//
//     // 分配设备内存并复制CSR结构
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//
//     cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//
//     // 初始化结果值为0
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 6. 创建计时事件
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//
//     // 7. 执行SDDMM
//     std::cout << "开始执行SDDMM..." << std::endl;
//     cudaEventRecord(start);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     // 8. 计算性能
//     float milliseconds = 0;
//     cudaEventElapsedTime(&milliseconds, start, stop);
//     double gflops = (2.0 * nnz * K) / (milliseconds * 1e6);
//     printf("SDDMM执行时间: %.3f ms\n", milliseconds);
//     printf("GFLOPS: %.2f\n", gflops);
//
//     // 9. 验证结果（可选）
//     std::vector<float> h_values_gpu(nnz);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     std::vector<float> h_values_cpu(nnz);
//     std::cout << "开始CPU参考计算..." << std::endl;
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//     std::cout << "CPU参考计算完成" << std::endl;
//
//     // 10. 结果对比
//     int correct_count = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_gpu[i] - h_values_cpu[i]);
//         max_error = fmax(max_error, diff);
//         if (diff < 1e-4) correct_count++;
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct_count / nnz) << "%" << std::endl;
//
//     // 11. 资源释放
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//     cudaEventDestroy(start);
//     cudaEventDestroy(stop);
//
//     return 0;
// }
//
// // 多少非零元素作为划分界限
// // 执行策略
// // 额外开销