# SDDMM 优化版本 Makefile

# 编译器设置
NVCC = nvcc

# 编译选项
NVCC_FLAGS = -O3 -std=c++11
ARCH_FLAGS = -arch=sm_70  # 根据你的GPU架构调整

# 目标文件
TARGETS = sddmm_original sddmm_optimized

# 默认目标
all: $(TARGETS)

# 编译原始版本
sddmm_original: sddmm_34.cu
	$(NVCC) $(NVCC_FLAGS) $(ARCH_FLAGS) -o $@ $<

# 编译完整优化版本
sddmm_optimized: sddmm_complete_optimized.cu
	$(NVCC) $(NVCC_FLAGS) $(ARCH_FLAGS) -o $@ $<

# 运行测试
test: sddmm_optimized
	@echo "运行优化版本测试..."
	@if [ -f "dataset/12month1.mtx" ]; then \
		./sddmm_optimized dataset/12month1.mtx 128; \
	else \
		echo "测试数据集不存在，创建小型测试矩阵..."; \
		mkdir -p dataset; \
		echo "%%MatrixMarket matrix coordinate real general" > dataset/test.mtx; \
		echo "100 100 1000" >> dataset/test.mtx; \
		for i in {1..1000}; do echo "$$((RANDOM % 100 + 1)) $$((RANDOM % 100 + 1)) 1.0" >> dataset/test.mtx; done; \
		./sddmm_optimized dataset/test.mtx 128; \
	fi

# 运行不同K值的测试
test_k: sddmm_optimized
	@echo "测试不同K值的性能..."
	@for k in 64 128 256; do \
		echo "=== K=$$k ==="; \
		if [ -f "dataset/12month1.mtx" ]; then \
			./sddmm_optimized dataset/12month1.mtx $$k; \
		else \
			./sddmm_optimized dataset/test.mtx $$k; \
		fi; \
		echo ""; \
	done

# 比较原始版本和优化版本
compare: sddmm_original sddmm_optimized
	@echo "比较原始版本和优化版本性能..."
	@echo "=== 原始版本 ==="
	@if [ -f "dataset/12month1.mtx" ]; then \
		./sddmm_original dataset/12month1.mtx 128; \
	fi
	@echo ""
	@echo "=== 优化版本 ==="
	@if [ -f "dataset/12month1.mtx" ]; then \
		./sddmm_optimized dataset/12month1.mtx 128; \
	fi

# 性能分析
profile: sddmm_optimized
	@echo "使用nvprof进行性能分析..."
	@if [ -f "dataset/12month1.mtx" ]; then \
		nvprof --print-gpu-trace ./sddmm_optimized dataset/12month1.mtx 128; \
	fi

# 内存检查
memcheck: sddmm_optimized
	@echo "使用cuda-memcheck检查内存错误..."
	@if [ -f "dataset/12month1.mtx" ]; then \
		cuda-memcheck ./sddmm_optimized dataset/12month1.mtx 128; \
	fi

# 清理
clean:
	rm -f $(TARGETS) *.o

# 创建测试数据目录
setup:
	@mkdir -p dataset
	@echo "请将测试矩阵文件放入 dataset/ 目录"

# 帮助信息
help:
	@echo "可用的make目标:"
	@echo "  all              - 编译所有版本"
	@echo "  sddmm_original   - 编译原始版本"
	@echo "  sddmm_optimized  - 编译完整优化版本"
	@echo "  test             - 运行基本性能测试"
	@echo "  test_k           - 测试不同K值的性能"
	@echo "  compare          - 比较原始版本和优化版本"
	@echo "  profile          - 使用nvprof进行性能分析"
	@echo "  memcheck         - 使用cuda-memcheck检查内存"
	@echo "  clean            - 清理编译文件"
	@echo "  setup            - 创建测试数据目录"
	@echo "  help             - 显示此帮助信息"

# 检查CUDA环境
check_cuda:
	@echo "检查CUDA环境..."
	@nvcc --version
	@nvidia-smi

.PHONY: all test test_k compare profile memcheck clean setup help check_cuda
