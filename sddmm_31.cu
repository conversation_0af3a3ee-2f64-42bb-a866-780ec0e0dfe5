// #include <cuda_runtime.h>
// #include <iostream>
// #include <cmath>
// #include <fstream>
// #include <vector>
// #include <algorithm>
// #include <sstream>
// #include <string>
// #include <cstdlib>
// #include <ctime>
// #include <cstdint>
//
// // 常量定义
// const int TILE_SIZE = 32;
// const int VECTOR_SIZE = 4;
// const int HIGH_DENSITY_THREADS = 256;
// const int BLOCK_SIZE = 4;
// const int BITMAP_WORD_SIZE = 32; // Bitmap每字位数
//
// // 稀疏矩阵结构体 (CSR + Bitmap格式)
// struct CSRMatrix {
//     int *row_ptr;
//     int *col_idx;
//     float *values;
//     uint32_t *bitmap;
//     int *bitmap_offset;
//     int rows;
//     int cols;
//     int nnz;
// };
//
// // 生成Bitmap格式
// void generate_bitmap_format(
//     const std::vector<int> &h_row_ptr,
//     const std::vector<int> &h_col_idx,
//     std::vector<uint32_t> &h_bitmap,
//     std::vector<int> &h_bitmap_offset,
//     int M
// ) {
//     // 计算每行需要的字数
//     std::vector<int> words_per_row(M);
//     for (int row = 0; row < M; ++row) {
//         int nnz = h_row_ptr[row + 1] - h_row_ptr[row];
//         words_per_row[row] = (nnz + BITMAP_WORD_SIZE - 1) / BITMAP_WORD_SIZE;
//     }
//
//     // 计算前缀和
//     h_bitmap_offset.resize(M + 1);
//     h_bitmap_offset[0] = 0;
//     for (int row = 0; row < M; ++row) {
//         h_bitmap_offset[row + 1] = h_bitmap_offset[row] + words_per_row[row];
//     }
//
//     // 生成bitmap
//     int total_words = h_bitmap_offset[M];
//     h_bitmap.resize(total_words, 0);
//     for (int row = 0; row < M; ++row) {
//         int start = h_row_ptr[row];
//         int end = h_row_ptr[row + 1];
//         int bitmap_start = h_bitmap_offset[row];
//         int words_this_row = words_per_row[row];
//         for (int idx = start; idx < end; ++idx) {
//             int pos_in_row = idx - start;
//             int word_idx = pos_in_row / BITMAP_WORD_SIZE;
//             int bit_pos = pos_in_row % BITMAP_WORD_SIZE;
//             if (word_idx < words_this_row) {
//                 h_bitmap[bitmap_start + word_idx] |= (1 << bit_pos);
//             }
//         }
//     }
// }
//
// // 合并的行统计和分类内核
// __global__ void compute_row_nnz_and_classify_kernel(
//     const int *row_ptr,
//     int *row_nnz,
//     int *d_high_rows,
//     int *d_low_rows,
//     int *high_count,
//     int *low_count,
//     int rows,
//     int threshold
// ) {
//     int row = blockIdx.x * blockDim.x + threadIdx.x;
//     if (row < rows) {
//         int nnz = row_ptr[row + 1] - row_ptr[row];
//         row_nnz[row] = nnz;
//
//         if (nnz > threshold) {
//             int pos = atomicAdd(high_count, 1);
//             if (pos < rows) d_high_rows[pos] = row;
//         } else {
//             int pos = atomicAdd(low_count, 1);
//             if (pos < rows) d_low_rows[pos] = row;
//         }
//     }
// }
//
// // 高密度区SDDMM核函数
// __global__ void sddmm_high_density_bitmap_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const uint32_t *__restrict__ bitmap,
//     const int *__restrict__ bitmap_offset,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     extern __shared__ float shared_A[]; // 仅缓存A行
//
//     int row_in_group = blockIdx.x;
//     if (row_in_group >= num_rows) return;
//
//     int orig_row = row_indices[row_in_group];
//     int row_start = row_ptr[orig_row];
//     int row_end = row_ptr[orig_row + 1];
//     int nnz_in_row = row_end - row_start;
//
//     int bitmap_start = bitmap_offset[orig_row];
//     int bitmap_words = (nnz_in_row + BITMAP_WORD_SIZE - 1) / BITMAP_WORD_SIZE;
//
//     int tid = threadIdx.x;
//     int num_threads = blockDim.x;
//     int warp_id = tid / 32;
//     int lane_id = tid % 32;
//     int warps_per_block = num_threads / 32;
//
//     // 加载A[row]到共享内存
//     for (int k = tid; k < K; k += num_threads) {
//         shared_A[k] = A[orig_row * K + k];
//     }
//     __syncthreads();
//
//     // 处理位图
//     for (int word_idx = warp_id; word_idx < bitmap_words; word_idx += warps_per_block) {
//         uint32_t bitmap_word = bitmap[bitmap_start + word_idx];
//         int base_pos = word_idx * BITMAP_WORD_SIZE;
//         int pos_in_row = base_pos + lane_id;
//         if (pos_in_row < nnz_in_row) {
//             int bit_pos_in_word = lane_id;
//             int flag = (bitmap_word >> bit_pos_in_word) & 0x1;
//             if (flag) {
//                 int col = col_idx[row_start + pos_in_row];
//                 float sum = 0.0f;
//                 int k = 0;
//                 // 向量化计算
//                 for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//                     float4 a_vec = *reinterpret_cast<const float4*>(&shared_A[k]);
//                     float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k]);
//                     sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y + a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//                 }
//                 // 处理尾部
//                 for (; k < K; ++k) {
//                     sum += shared_A[k] * B[col * K + k];
//                 }
//                 result[row_start + pos_in_row] = sum;
//             }
//         }
//     }
// }
//
// // 低密度区SDDMM核函数
// __global__ void sddmm_low_density_kernel(
//     const float *__restrict__ A,
//     const float *__restrict__ B,
//     const int *__restrict__ row_ptr,
//     const int *__restrict__ col_idx,
//     const int *__restrict__ row_indices,
//     float *__restrict__ result,
//     int M, int K, int N,
//     int num_rows
// ) {
//     int index = blockIdx.x * blockDim.x + threadIdx.x;
//     if (index >= num_rows) return;
//
//     int orig_row = row_indices[index];
//     const int row_start = row_ptr[orig_row];
//     const int row_end = row_ptr[orig_row + 1];
//     const int nnz_in_row = row_end - row_start;
//
//     // 动态处理K维度
//     for (int idx = 0; idx < nnz_in_row; ++idx) {
//         int col = col_idx[row_start + idx];
//         if (col < 0 || col >= N) continue;
//
//         float sum = 0.0f;
//         int k = 0;
//
//         // 向量化计算
//         for (; k <= K - VECTOR_SIZE; k += VECTOR_SIZE) {
//             float4 a_vec = *reinterpret_cast<const float4*>(&A[orig_row * K + k]);
//             float4 b_vec = *reinterpret_cast<const float4*>(&B[col * K + k]);
//             sum += a_vec.x * b_vec.x + a_vec.y * b_vec.y + a_vec.z * b_vec.z + a_vec.w * b_vec.w;
//         }
//
//         // 处理尾部
//         for (; k < K; ++k) {
//             sum += A[orig_row * K + k] * B[col * K + k];
//         }
//
//         result[row_start + idx] = sum;
//     }
// }
//
// // 主SDDMM函数
// void sddmm_csr(
//     const float *d_A,
//     const float *d_B,
//     CSRMatrix &sparse,
//     int K
// ) {
//     int *d_row_nnz, *d_high_rows, *d_low_rows;
//     int *d_high_count, *d_low_count;
//     int h_high_count = 0, h_low_count = 0;
//     int threshold = 32;
//
//     cudaMalloc(&d_row_nnz, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_low_rows, sparse.rows * sizeof(int));
//     cudaMalloc(&d_high_count, sizeof(int));
//     cudaMalloc(&d_low_count, sizeof(int));
//     cudaMemset(d_high_count, 0, sizeof(int));
//     cudaMemset(d_low_count, 0, sizeof(int));
//
//     dim3 block(256);
//     dim3 grid((sparse.rows + block.x - 1) / block.x);
//     compute_row_nnz_and_classify_kernel<<<grid, block>>>(
//         sparse.row_ptr, d_row_nnz, d_high_rows, d_low_rows,
//         d_high_count, d_low_count, sparse.rows, threshold
//     );
//
//     cudaMemcpy(&h_high_count, d_high_count, sizeof(int), cudaMemcpyDeviceToHost);
//     cudaMemcpy(&h_low_count, d_low_count, sizeof(int), cudaMemcpyDeviceToHost);
//     printf("高密度行数: %d\n", h_high_count);
//     printf("低密度行数: %d\n", h_low_count);
//     printf("总行数: %d\n", h_high_count + h_low_count);
//
//     // 创建CUDA流
//     cudaStream_t stream_high, stream_low;
//     cudaStreamCreate(&stream_high);
//     cudaStreamCreate(&stream_low);
//
//     // 计算共享内存大小（仅缓存A行）
//     size_t shared_mem_size = K * sizeof(float);
//     printf("高密度区共享内存大小: %zu bytes\n", shared_mem_size);
//
//     // 创建事件用于计时
//     cudaEvent_t step4_start, step4_stop, step5_start, step5_stop;
//     cudaEventCreate(&step4_start);
//     cudaEventCreate(&step4_stop);
//     cudaEventCreate(&step5_start);
//     cudaEventCreate(&step5_stop);
//
//     // 启动高密度区核函数
//     if (h_high_count > 0) {
//         dim3 block_hd(HIGH_DENSITY_THREADS);
//         dim3 grid_hd(h_high_count);
//         cudaEventRecord(step4_start, stream_high);
//         sddmm_high_density_bitmap_kernel<<<grid_hd, block_hd, shared_mem_size, stream_high>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx,
//             sparse.bitmap, sparse.bitmap_offset,
//             d_high_rows, sparse.values,
//             sparse.rows, K, sparse.cols,
//             h_high_count
//         );
//         cudaError_t err = cudaGetLastError();
//         if (err != cudaSuccess) {
//             printf("高密度核函数启动错误: %s\n", cudaGetErrorString(err));
//         }
//     }
//
//     // 启动低密度区核函数
//     if (h_low_count > 0) {
//         dim3 block_ld(256);
//         dim3 grid_ld((h_low_count + block_ld.x - 1) / block_ld.x);
//         cudaEventRecord(step5_start, stream_low);
//         sddmm_low_density_kernel<<<grid_ld, block_ld, 0, stream_low>>>(
//             d_A, d_B, sparse.row_ptr, sparse.col_idx,
//             d_low_rows, sparse.values,
//             sparse.rows, K, sparse.cols,
//             h_low_count
//         );
//         cudaError_t err = cudaGetLastError();
//         if (err != cudaSuccess) {
//             printf("低密度核函数启动错误: %s\n", cudaGetErrorString(err));
//         }
//     }
//
//     // 测量高密度区时间
//     if (h_high_count > 0) {
//         cudaEventRecord(step4_stop, stream_high);
//         cudaEventSynchronize(step4_stop);
//         float step4_ms;
//         cudaEventElapsedTime(&step4_ms, step4_start, step4_stop);
//         printf("[步骤4] 高密度区计算耗时: %.3f ms\n", step4_ms);
//     }
//
//     // 测量低密度区时间
//     if (h_low_count > 0) {
//         cudaEventRecord(step5_stop, stream_low);
//         cudaEventSynchronize(step5_stop);
//         float step5_ms;
//         cudaEventElapsedTime(&step5_ms, step5_start, step5_stop);
//         printf("[步骤5] 低密度区计算耗时: %.3f ms\n", step5_ms);
//     }
//
//     // 同步流
//     cudaStreamSynchronize(stream_high);
//     cudaStreamSynchronize(stream_low);
//
//     // 销毁流和事件
//     cudaStreamDestroy(stream_high);
//     cudaStreamDestroy(stream_low);
//     cudaEventDestroy(step4_start);
//     cudaEventDestroy(step4_stop);
//     cudaEventDestroy(step5_start);
//     cudaEventDestroy(step5_stop);
//
//     // 释放资源
//     cudaFree(d_row_nnz);
//     cudaFree(d_high_rows);
//     cudaFree(d_low_rows);
//     cudaFree(d_high_count);
//     cudaFree(d_low_count);
// }
//
// // CPU参考实现
// void sddmm_cpu_reference(
//     const float *A, const float *B,
//     const int *row_ptr, const int *col_idx,
//     float *values, int M, int N, int K) {
//     for (int row = 0; row < M; ++row) {
//         int start = row_ptr[row];
//         int end = row_ptr[row + 1];
//         for (int idx = start; idx < end; ++idx) {
//             int col = col_idx[idx];
//             float sum = 0.0f;
//             for (int k = 0; k < K; ++k) {
//                 sum += A[row * K + k] * B[col * K + k];
//             }
//             values[idx] = sum;
//         }
//     }
// }
//
// // 从Matrix Market文件加载COO格式稀疏矩阵
// void load_coo_matrix(const std::string &filename, int &M, int &N, int &nnz,
//                      std::vector<int> &rows, std::vector<int> &cols) {
//     std::ifstream file(filename);
//     if (!file.is_open()) {
//         std::cerr << "无法打开文件: " << filename << std::endl;
//         exit(1);
//     }
//
//     std::string line;
//     while (std::getline(file, line) && line[0] == '%');
//
//     std::istringstream header(line);
//     header >> M >> N >> nnz;
//
//     rows.resize(nnz);
//     cols.resize(nnz);
//
//     for (int i = 0; i < nnz; ++i) {
//         int row, col;
//         float value;
//         file >> row >> col;
//         if (file.peek() != '\n') {
//             file >> value;
//         }
//         rows[i] = row - 1;
//         cols[i] = col - 1;
//     }
//     file.close();
// }
//
// // 将COO格式转换为CSR格式
// void coo_to_csr(const std::vector<int> &rows, const std::vector<int> &cols,
//                 int M, int N, int nnz,
//                 std::vector<int> &csr_row_ptr, std::vector<int> &csr_col_idx,
//                 int &max_nnz_per_row) {
//     csr_row_ptr.resize(M + 1, 0);
//     max_nnz_per_row = 0;
//
//     for (int i = 0; i < nnz; ++i) {
//         csr_row_ptr[rows[i]]++;
//     }
//
//     int sum = 0;
//     for (int i = 0; i < M; ++i) {
//         int temp = csr_row_ptr[i];
//         if (temp > max_nnz_per_row) max_nnz_per_row = temp;
//         csr_row_ptr[i] = sum;
//         sum += temp;
//     }
//     csr_row_ptr[M] = sum;
//
//     std::vector<int> row_counter(M, 0);
//     csr_col_idx.resize(nnz);
//
//     for (int i = 0; i < nnz; ++i) {
//         int row = rows[i];
//         int col = cols[i];
//         int index = csr_row_ptr[row] + row_counter[row];
//         csr_col_idx[index] = col;
//         row_counter[row]++;
//     }
// }
//
// int main(int argc, char **argv) {
//     if (argc < 2) {
//         std::cerr << "Usage: " << argv[0] << " <matrix_file.mtx> [K=128]" << std::endl;
//         return 1;
//     }
//
//     std::srand(std::time(nullptr));
//     std::string filename = argv[1];
//     int K = (argc > 2) ? std::atoi(argv[2]) : 128;
//
//     // 加载矩阵
//     int M, N, nnz, max_nnz_per_row = 0;
//     std::vector<int> coo_rows, coo_cols;
//     load_coo_matrix(filename, M, N, nnz, coo_rows, coo_cols);
//     printf("矩阵信息: M=%d, N=%d, nnz=%d\n", M, N, nnz);
//
//     // 转换为CSR格式
//     std::vector<int> h_csr_row_ptr, h_csr_col_idx;
//     coo_to_csr(coo_rows, coo_cols, M, N, nnz, h_csr_row_ptr, h_csr_col_idx, max_nnz_per_row);
//     printf("最大行非零元数: %d\n", max_nnz_per_row);
//
//     // 生成Bitmap格式
//     std::vector<uint32_t> h_bitmap;
//     std::vector<int> h_bitmap_offset;
//     generate_bitmap_format(h_csr_row_ptr, h_csr_col_idx, h_bitmap, h_bitmap_offset, M);
//
//     // 初始化稠密矩阵
//     std::vector<float> h_A(M * K);
//     std::vector<float> h_B(N * K);
//     for (int i = 0; i < M * K; ++i) h_A[i] = (rand() % 100) / 100.0f;
//     for (int i = 0; i < N * K; ++i) h_B[i] = (rand() % 100) / 100.0f;
//
//     // 分配设备内存
//     float *d_A, *d_B;
//     cudaMalloc(&d_A, M * K * sizeof(float));
//     cudaMalloc(&d_B, N * K * sizeof(float));
//     cudaMemcpy(d_A, h_A.data(), M * K * sizeof(float), cudaMemcpyHostToDevice);
//     cudaMemcpy(d_B, h_B.data(), N * K * sizeof(float), cudaMemcpyHostToDevice);
//
//     // 设置稀疏矩阵结构
//     CSRMatrix sparse;
//     sparse.rows = M;
//     sparse.cols = N;
//     sparse.nnz = nnz;
//     cudaMalloc(&sparse.row_ptr, (M + 1) * sizeof(int));
//     cudaMalloc(&sparse.col_idx, nnz * sizeof(int));
//     cudaMalloc(&sparse.values, nnz * sizeof(float));
//     cudaMalloc(&sparse.bitmap, h_bitmap.size() * sizeof(uint32_t));
//     cudaMalloc(&sparse.bitmap_offset, (M + 1) * sizeof(int));
//     cudaMemcpy(sparse.row_ptr, h_csr_row_ptr.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.col_idx, h_csr_col_idx.data(), nnz * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.bitmap, h_bitmap.data(), h_bitmap.size() * sizeof(uint32_t), cudaMemcpyHostToDevice);
//     cudaMemcpy(sparse.bitmap_offset, h_bitmap_offset.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice);
//     cudaMemset(sparse.values, 0, nnz * sizeof(float));
//
//     // 执行SDDMM
//     std::cout << "开始执行SDDMM..." << std::endl;
//
//     cudaEvent_t start, stop;
//     cudaEventCreate(&start);
//     cudaEventCreate(&stop);
//     cudaEventRecord(start);
//
//     sddmm_csr(d_A, d_B, sparse, K);
//
//     cudaEventRecord(stop);
//     cudaEventSynchronize(stop);
//     float ms;
//     cudaEventElapsedTime(&ms, start, stop);
//     printf("SDDMM计算时间: %.3f ms\n", ms);
//     std::cout << "SDDMM执行完成" << std::endl;
//
//     // 验证结果
//     std::vector<float> h_values_gpu(nnz);
//     cudaMemcpy(h_values_gpu.data(), sparse.values, nnz * sizeof(float), cudaMemcpyDeviceToHost);
//
//     std::vector<float> h_values_cpu(nnz);
//     sddmm_cpu_reference(h_A.data(), h_B.data(),
//                         h_csr_row_ptr.data(), h_csr_col_idx.data(),
//                         h_values_cpu.data(), M, N, K);
//
//     int correct = 0;
//     float max_error = 0.0f;
//     for (int i = 0; i < nnz; ++i) {
//         float diff = fabs(h_values_cpu[i] - h_values_gpu[i]);
//         if (diff < 1e-4) correct++;
//         if (diff > max_error) max_error = diff;
//     }
//
//     std::cout << "验证结果:" << std::endl;
//     std::cout << "最大绝对误差: " << max_error << std::endl;
//     std::cout << "正确率: " << (100.0f * correct / nnz) << "%" << std::endl;
//
//     // 释放资源
//     cudaFree(d_A);
//     cudaFree(d_B);
//     cudaFree(sparse.row_ptr);
//     cudaFree(sparse.col_idx);
//     cudaFree(sparse.values);
//     cudaFree(sparse.bitmap);
//     cudaFree(sparse.bitmap_offset);
//
//     return 0;
// }
//
// // 流水线+位图
// // /data/data/qjs/CLionProjects/CUDAProjects/cmake-build-debug/CUDAProjects /data/data/qjs/CLionProjects/CUDAProjects/dataset/12month1.mtx
// // 矩阵信息: M=12471, N=872622, nnz=22624727
// // 最大行非零元数: 75355
// // 开始执行SDDMM...
// // 高密度行数: 11012
// // 低密度行数: 1459
// // 总行数: 12471
// // 高密度区共享内存大小: 512 bytes
// // [步骤4] 高密度区计算耗时: 92.805 ms
// // [步骤5] 低密度区计算耗时: 93.177 ms
// // SDDMM计算时间: 98.934 ms
// // SDDMM执行完成
// // 验证结果:
// // 最大绝对误差: 3.43323e-05
// // 正确率: 100%
// //
// // 进程已结束，退出代码为 0